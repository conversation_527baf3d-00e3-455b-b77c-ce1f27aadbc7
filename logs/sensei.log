{"event": "{\"config\": {\"environment\": \"production\", \"debug\": false, \"project_name\": \"sensei-ai\", \"version\": \"1.0.0\", \"bigquery\": {\"project_id\": \"datalake-sensei\", \"dataset_id\": \"serving_layer\", \"location\": \"EU\", \"max_bytes_billed\": ***********}, \"api\": {\"host\": \"0.0.0.0\", \"port\": 8000, \"workers\": 2, \"reload\": false}, \"models\": {\"cache_size_mb\": 1000, \"timeout_seconds\": 30, \"max_versions\": 3}, \"training\": {\"max_samples\": 10000, \"max_features\": 50, \"hyperopt_trials\": 50, \"timeout_minutes\": 30}}, \"event\": \"Configuration loaded\", \"logger\": \"sensei.utils.config\", \"level\": \"info\", \"timestamp\": \"2025-07-23T09:11:09.871358Z\"}"}
{"event": "{\"api_key\": \"sk-sensei-demo-4Lj-rNnZ_siA-uFdZNlAgi4q3EFJ7LfkUzXaha4rDeI\", \"user_id\": \"admin\", \"event\": \"Default credentials created\", \"logger\": \"sensei.api.auth\", \"level\": \"info\", \"timestamp\": \"2025-07-23T09:11:09.871732Z\"}"}
{"event": "{\"config\": {\"environment\": \"production\", \"debug\": false, \"project_name\": \"sensei-ai\", \"version\": \"1.0.0\", \"bigquery\": {\"project_id\": \"datalake-sensei\", \"dataset_id\": \"serving_layer\", \"location\": \"EU\", \"max_bytes_billed\": ***********}, \"api\": {\"host\": \"0.0.0.0\", \"port\": 8000, \"workers\": 2, \"reload\": false}, \"models\": {\"cache_size_mb\": 1000, \"timeout_seconds\": 30, \"max_versions\": 3}, \"training\": {\"max_samples\": 10000, \"max_features\": 50, \"hyperopt_trials\": 50, \"timeout_minutes\": 30}}, \"event\": \"Configuration loaded\", \"logger\": \"sensei.utils.config\", \"level\": \"info\", \"timestamp\": \"2025-07-23T09:12:51.217359Z\"}"}
{"event": "{\"api_key\": \"sk-sensei-demo-doNzg7n29ziYOwJiM21Reb-ic4Aeyjaur81TyxGrRqw\", \"user_id\": \"admin\", \"event\": \"Default credentials created\", \"logger\": \"sensei.api.auth\", \"level\": \"info\", \"timestamp\": \"2025-07-23T09:12:51.217884Z\"}"}
{"event": "{\"config\": {\"environment\": \"production\", \"debug\": false, \"project_name\": \"sensei-ai\", \"version\": \"1.0.0\", \"bigquery\": {\"project_id\": \"datalake-sensei\", \"dataset_id\": \"serving_layer\", \"location\": \"EU\", \"max_bytes_billed\": ***********}, \"api\": {\"host\": \"0.0.0.0\", \"port\": 8000, \"workers\": 2, \"reload\": false}, \"models\": {\"cache_size_mb\": 1000, \"timeout_seconds\": 30, \"max_versions\": 3}, \"training\": {\"max_samples\": 10000, \"max_features\": 50, \"hyperopt_trials\": 50, \"timeout_minutes\": 30}}, \"event\": \"Configuration loaded\", \"logger\": \"sensei.utils.config\", \"level\": \"info\", \"timestamp\": \"2025-07-23T09:14:56.375047Z\"}"}
{"event": "{\"api_key\": \"sk-sensei-demo-o1BBN63bx3K8NypB1mMsT2limbhWeshnXzlq5Qh3t9M\", \"user_id\": \"admin\", \"event\": \"Default credentials created\", \"logger\": \"sensei.api.auth\", \"level\": \"info\", \"timestamp\": \"2025-07-23T09:14:56.375666Z\"}"}
{"event": "Some imports failed: No module named 'sensei.models.optimization'"}
{"event": "HTTP Request: GET http://testserver/health \"HTTP/1.1 200 OK\""}
{"event": "HTTP Request: GET http://testserver/version \"HTTP/1.1 200 OK\""}
{"event": "\u2713 Simple API creation successful"}
{"event": "\u2713 Data processing pipeline successful: 7 features"}
{"event": "\u2713 ML pipeline successful: accuracy=0.940, auc=0.985"}
{"event": "\u2713 Config loaded: production"}
{"event": "{\"event\": \"Test log message\", \"logger\": \"test\", \"level\": \"info\", \"timestamp\": \"2025-07-23T09:15:00.291165Z\"}"}
{"event": "\u2713 Logging setup successful"}
{"event": "\u2713 Model validation: CV AUC = 0.974 \u00b1 0.010"}
{"event": "\u2713 Feature importance analysis: [0.82650522 0.0423593  0.03466291 0.04333298 0.05313959]"}
{"event": "{\"config\": {\"environment\": \"production\", \"debug\": false, \"project_name\": \"sensei-ai\", \"version\": \"1.0.0\", \"bigquery\": {\"project_id\": \"datalake-sensei\", \"dataset_id\": \"serving_layer\", \"location\": \"EU\", \"max_bytes_billed\": ***********}, \"api\": {\"host\": \"0.0.0.0\", \"port\": 8000, \"workers\": 2, \"reload\": false}, \"models\": {\"cache_size_mb\": 1000, \"timeout_seconds\": 30, \"max_versions\": 3}, \"training\": {\"max_samples\": 10000, \"max_features\": 50, \"hyperopt_trials\": 50, \"timeout_minutes\": 30}}, \"event\": \"Configuration loaded\", \"logger\": \"sensei.utils.config\", \"level\": \"info\", \"timestamp\": \"2025-07-23T09:15:52.274695Z\"}"}
{"event": "{\"api_key\": \"sk-sensei-demo-kKz00nssaTLS-WUrI927pyyEAWODUbOXyNbJSN-Ed8I\", \"user_id\": \"admin\", \"event\": \"Default credentials created\", \"logger\": \"sensei.api.auth\", \"level\": \"info\", \"timestamp\": \"2025-07-23T09:15:52.275305Z\"}"}
{"event": "Some imports failed: No module named 'sensei.models.optimization'"}
{"event": "HTTP Request: GET http://testserver/health \"HTTP/1.1 200 OK\""}
{"event": "HTTP Request: GET http://testserver/version \"HTTP/1.1 200 OK\""}
{"event": "\u2713 Simple API creation successful"}
{"event": "\u2713 Data processing pipeline successful: 7 features"}
{"event": "\u2713 ML pipeline successful: accuracy=0.940, auc=0.985"}
{"event": "\u2713 Config loaded: production"}
{"event": "{\"event\": \"Test log message\", \"logger\": \"test\", \"level\": \"info\", \"timestamp\": \"2025-07-23T09:15:56.026095Z\"}"}
{"event": "\u2713 Logging setup successful"}
{"event": "\u2713 Model validation: CV AUC = 0.974 \u00b1 0.010"}
{"event": "\u2713 Feature importance analysis: [0.82650522 0.0423593  0.03466291 0.04333298 0.05313959]"}
{"event": "{\"config\": {\"environment\": \"production\", \"debug\": false, \"project_name\": \"sensei-ai\", \"version\": \"1.0.0\", \"bigquery\": {\"project_id\": \"datalake-sensei\", \"dataset_id\": \"serving_layer\", \"location\": \"EU\", \"max_bytes_billed\": ***********}, \"api\": {\"host\": \"0.0.0.0\", \"port\": 8000, \"workers\": 2, \"reload\": false}, \"models\": {\"cache_size_mb\": 1000, \"timeout_seconds\": 30, \"max_versions\": 3}, \"training\": {\"max_samples\": 10000, \"max_features\": 50, \"hyperopt_trials\": 50, \"timeout_minutes\": 30}}, \"event\": \"Configuration loaded\", \"logger\": \"sensei.utils.config\", \"level\": \"info\", \"timestamp\": \"2025-07-23T09:17:12.668939Z\"}"}
{"event": "{\"api_key\": \"sk-sensei-demo-Q0YmFBjX-TbqK55beeWsDTigsCVb2hHTAFWlUrpVgHI\", \"user_id\": \"admin\", \"event\": \"Default credentials created\", \"logger\": \"sensei.api.auth\", \"level\": \"info\", \"timestamp\": \"2025-07-23T09:17:12.669511Z\"}"}
{"event": "Some imports failed: No module named 'sensei.models.optimization'"}
{"event": "HTTP Request: GET http://testserver/health \"HTTP/1.1 200 OK\""}
{"event": "HTTP Request: GET http://testserver/version \"HTTP/1.1 200 OK\""}
{"event": "\u2713 Simple API creation successful"}
{"event": "\u2713 Data processing pipeline successful: 7 features"}
{"event": "\u2713 ML pipeline successful: accuracy=0.940, auc=0.985"}
{"event": "\u2713 Config loaded: production"}
{"event": "{\"event\": \"Test log message\", \"logger\": \"test\", \"level\": \"info\", \"timestamp\": \"2025-07-23T09:17:16.671722Z\"}"}
{"event": "\u2713 Logging setup successful"}
{"event": "\u2713 Model validation: CV AUC = 0.974 \u00b1 0.010"}
{"event": "\u2713 Feature importance analysis: [0.82650522 0.0423593  0.03466291 0.04333298 0.05313959]"}
{"event": "\u2713 Data quality validation: {'high_missing': {'missing_feature': 0.2}, 'constant_features': ['missing_feature', 'constant_feature'], 'outlier_features': ['outlier_feature']}"}
{"event": "{\"config\": {\"environment\": \"production\", \"debug\": false, \"project_name\": \"sensei-ai\", \"version\": \"1.0.0\", \"bigquery\": {\"project_id\": \"datalake-sensei\", \"dataset_id\": \"serving_layer\", \"location\": \"EU\", \"max_bytes_billed\": ***********}, \"api\": {\"host\": \"0.0.0.0\", \"port\": 8000, \"workers\": 2, \"reload\": false}, \"models\": {\"cache_size_mb\": 1000, \"timeout_seconds\": 30, \"max_versions\": 3}, \"training\": {\"max_samples\": 10000, \"max_features\": 50, \"hyperopt_trials\": 50, \"timeout_minutes\": 30}}, \"event\": \"Configuration loaded\", \"logger\": \"src.sensei.utils.config\", \"level\": \"info\", \"timestamp\": \"2025-07-23T09:17:30.042504Z\"}"}
{"event": "{\"api_key\": \"sk-sensei-demo-6pALA0tG-NbhE7u_veq3NnObfdT40cvHDqT_nM8ZoTo\", \"user_id\": \"admin\", \"event\": \"Default credentials created\", \"logger\": \"src.sensei.api.auth\", \"level\": \"info\", \"timestamp\": \"2025-07-23T09:17:30.042888Z\"}"}
{"event": "{\"config\": {\"environment\": \"production\", \"debug\": false, \"project_name\": \"sensei-ai\", \"version\": \"1.0.0\", \"bigquery\": {\"project_id\": \"datalake-sensei\", \"dataset_id\": \"serving_layer\", \"location\": \"EU\", \"max_bytes_billed\": ***********}, \"api\": {\"host\": \"0.0.0.0\", \"port\": 8000, \"workers\": 2, \"reload\": false}, \"models\": {\"cache_size_mb\": 1000, \"timeout_seconds\": 30, \"max_versions\": 3}, \"training\": {\"max_samples\": 10000, \"max_features\": 50, \"hyperopt_trials\": 50, \"timeout_minutes\": 30}}, \"event\": \"Configuration loaded\", \"logger\": \"sensei.utils.config\", \"level\": \"info\", \"timestamp\": \"2025-07-23T09:18:33.393619Z\"}"}
{"event": "{\"api_key\": \"sk-sensei-demo-UDg_bYHq2nRC2iKmqUs3sBQONxqXLpaTo_wz7KzBhzs\", \"user_id\": \"admin\", \"event\": \"Default credentials created\", \"logger\": \"sensei.api.auth\", \"level\": \"info\", \"timestamp\": \"2025-07-23T09:18:33.395072Z\"}"}
{"event": "Sensei modules not fully available: No module named 'fastapi.middleware.base'"}
{"event": "\u2713 Basic data processing works"}
{"event": "\u2713 Basic ML model works: accuracy = 0.883"}
{"event": "\u2713 LightGBM works: AUC = 0.997"}
{"event": "\u2713 CatBoost works: AUC = 0.999"}
{"event": "HTTP Request: GET http://testserver/ \"HTTP/1.1 200 OK\""}
{"event": "HTTP Request: GET http://testserver/health \"HTTP/1.1 200 OK\""}
{"event": "\u2713 FastAPI basic functionality works"}
{"event": "\u2713 Hyperparameter optimization works: best_value = 0.980"}
{"event": "\u2713 Conversion data generated: 100 samples, 29.0% conversion rate"}
{"event": "\u2713 Channel timing data generated: 50 samples"}
{"event": "\u2713 NLP data generated: 30 samples"}
{"event": "\u2713 No excessive missing values"}
{"event": "\u2713 No obvious data leakage patterns"}
{"event": "\u2713 No excessive feature correlations"}
{"event": "Training with 800 samples, 22 features"}
{"event": "Class distribution: {0: np.int64(578), 1: np.int64(222)}"}
{"event": "  simple: AUC = 0.577 \u00b1 0.016"}
{"event": "  medium: AUC = 0.579 \u00b1 0.028"}
{"event": "  complex: AUC = 0.558 \u00b1 0.025"}
{"event": "\u2713 No obvious overfitting detected"}
{"event": "\u2713 Model training validation completed"}
{"event": "Hyperparameter optimization with 400 samples"}
{"event": "\u2713 Optimization completed: best AUC = 0.674"}
{"event": "  Best params: {'n_estimators': 77, 'max_depth': 7, 'min_samples_split': 10}"}
{"event": "\u2713 Best model validation: AUC = 0.674 \u00b1 0.025"}
{"event": "\u2713 Utils imported successfully"}
{"event": "\u2713 Base model imported successfully"}
{"event": "HTTP Request: GET http://testserver/health \"HTTP/1.1 200 OK\""}
{"event": "HTTP Request: GET http://testserver/version \"HTTP/1.1 200 OK\""}
{"event": "\u2713 Simple API creation successful"}
{"event": "\u2713 Data processing pipeline successful: 7 features"}
{"event": "\u2713 ML pipeline successful: accuracy=0.940, auc=0.985"}
{"event": "\u2713 Config loaded: production"}
{"event": "{\"event\": \"Test log message\", \"logger\": \"test\", \"level\": \"info\", \"timestamp\": \"2025-07-23T09:18:39.953669Z\"}"}
{"event": "\u2713 Logging setup successful"}
{"event": "\u2713 Model validation: CV AUC = 0.974 \u00b1 0.010"}
{"event": "\u2713 Feature importance analysis: [0.82650522 0.0423593  0.03466291 0.04333298 0.05313959]"}
{"event": "\u2713 Data quality validation: {'high_missing': {'missing_feature': 0.2}, 'constant_features': ['missing_feature', 'constant_feature'], 'outlier_features': ['outlier_feature']}"}
{"event": "{\"config\": {\"environment\": \"production\", \"debug\": false, \"project_name\": \"sensei-ai\", \"version\": \"1.0.0\", \"bigquery\": {\"project_id\": \"datalake-sensei\", \"dataset_id\": \"serving_layer\", \"location\": \"EU\", \"max_bytes_billed\": ***********}, \"api\": {\"host\": \"0.0.0.0\", \"port\": 8000, \"workers\": 2, \"reload\": false}, \"models\": {\"cache_size_mb\": 1000, \"timeout_seconds\": 30, \"max_versions\": 3}, \"training\": {\"max_samples\": 10000, \"max_features\": 50, \"hyperopt_trials\": 50, \"timeout_minutes\": 30}}, \"event\": \"Configuration loaded\", \"logger\": \"sensei.utils.config\", \"level\": \"info\", \"timestamp\": \"2025-07-23T09:20:17.844086Z\"}"}
{"event": "{\"api_key\": \"sk-sensei-demo-ZqB-hMQDADjwqCLNVSffUxKIpHtgXBVbRgny62IJXjE\", \"user_id\": \"admin\", \"event\": \"Default credentials created\", \"logger\": \"sensei.api.auth\", \"level\": \"info\", \"timestamp\": \"2025-07-23T09:20:17.844883Z\"}"}
{"event": "Sensei modules not fully available: No module named 'fastapi.middleware.base'"}
{"event": "\u2713 Basic data processing works"}
{"event": "\u2713 Basic ML model works: accuracy = 0.883"}
{"event": "\u2713 LightGBM works: AUC = 0.997"}
{"event": "\u2713 CatBoost works: AUC = 0.999"}
{"event": "HTTP Request: GET http://testserver/ \"HTTP/1.1 200 OK\""}
{"event": "HTTP Request: GET http://testserver/health \"HTTP/1.1 200 OK\""}
{"event": "\u2713 FastAPI basic functionality works"}
{"event": "\u2713 Hyperparameter optimization works: best_value = 0.979"}
{"event": "{\"config\": {\"environment\": \"production\", \"debug\": false, \"project_name\": \"sensei-ai\", \"version\": \"1.0.0\", \"bigquery\": {\"project_id\": \"datalake-sensei\", \"dataset_id\": \"serving_layer\", \"location\": \"EU\", \"max_bytes_billed\": ***********}, \"api\": {\"host\": \"0.0.0.0\", \"port\": 8000, \"workers\": 2, \"reload\": false}, \"models\": {\"cache_size_mb\": 1000, \"timeout_seconds\": 30, \"max_versions\": 3}, \"training\": {\"max_samples\": 10000, \"max_features\": 50, \"hyperopt_trials\": 50, \"timeout_minutes\": 30}}, \"event\": \"Configuration loaded\", \"logger\": \"sensei.utils.config\", \"level\": \"info\", \"timestamp\": \"2025-07-23T09:20:27.177643Z\"}"}
{"event": "{\"api_key\": \"sk-sensei-demo-khQBB9NS5V33mjh3rFq9i9T5F_MX7PX8b6vo2cVRARM\", \"user_id\": \"admin\", \"event\": \"Default credentials created\", \"logger\": \"sensei.api.auth\", \"level\": \"info\", \"timestamp\": \"2025-07-23T09:20:27.178013Z\"}"}
{"event": "Some imports failed: No module named 'fastapi.middleware.base'"}
{"event": "HTTP Request: GET http://testserver/health \"HTTP/1.1 200 OK\""}
{"event": "HTTP Request: GET http://testserver/version \"HTTP/1.1 200 OK\""}
{"event": "\u2713 Simple API creation successful"}
{"event": "\u2713 Data processing pipeline successful: 7 features"}
{"event": "\u2713 ML pipeline successful: accuracy=0.940, auc=0.985"}
{"event": "\u2713 Config loaded: production"}
{"event": "{\"event\": \"Test log message\", \"logger\": \"test\", \"level\": \"info\", \"timestamp\": \"2025-07-23T09:20:30.616102Z\"}"}
{"event": "\u2713 Logging setup successful"}
{"event": "\u2713 Model validation: CV AUC = 0.974 \u00b1 0.010"}
{"event": "\u2713 Feature importance analysis: [0.82650522 0.0423593  0.03466291 0.04333298 0.05313959]"}
{"event": "\u2713 Data quality validation: {'high_missing': {'missing_feature': 0.2}, 'constant_features': ['missing_feature', 'constant_feature'], 'outlier_features': ['outlier_feature']}"}
{"event": "{\"config\": {\"environment\": \"production\", \"debug\": false, \"project_name\": \"sensei-ai\", \"version\": \"1.0.0\", \"bigquery\": {\"project_id\": \"datalake-sensei\", \"dataset_id\": \"serving_layer\", \"location\": \"EU\", \"max_bytes_billed\": ***********}, \"api\": {\"host\": \"0.0.0.0\", \"port\": 8000, \"workers\": 2, \"reload\": false}, \"models\": {\"cache_size_mb\": 1000, \"timeout_seconds\": 30, \"max_versions\": 3}, \"training\": {\"max_samples\": 10000, \"max_features\": 50, \"hyperopt_trials\": 50, \"timeout_minutes\": 30}}, \"event\": \"Configuration loaded\", \"logger\": \"sensei.utils.config\", \"level\": \"info\", \"timestamp\": \"2025-07-23T09:20:32.740892Z\"}"}
{"event": "{\"api_key\": \"sk-sensei-demo-nGxCUp_RUXpc0AbTxk2RfyoeNJnf-UZy80VChH4OaRU\", \"user_id\": \"admin\", \"event\": \"Default credentials created\", \"logger\": \"sensei.api.auth\", \"level\": \"info\", \"timestamp\": \"2025-07-23T09:20:32.741151Z\"}"}
{"event": "Sensei modules not fully available: No module named 'fastapi.middleware.base'"}
{"event": "\u2713 Basic data processing works"}
{"event": "\u2713 Basic ML model works: accuracy = 0.883"}
{"event": "\u2713 LightGBM works: AUC = 0.997"}
{"event": "\u2713 CatBoost works: AUC = 0.999"}
{"event": "HTTP Request: GET http://testserver/ \"HTTP/1.1 200 OK\""}
{"event": "HTTP Request: GET http://testserver/health \"HTTP/1.1 200 OK\""}
{"event": "\u2713 FastAPI basic functionality works"}
{"event": "\u2713 Hyperparameter optimization works: best_value = 0.980"}
{"event": "\u2713 Conversion data generated: 100 samples, 29.0% conversion rate"}
{"event": "\u2713 Channel timing data generated: 50 samples"}
{"event": "\u2713 NLP data generated: 30 samples"}
{"event": "\u2713 No excessive missing values"}
{"event": "\u2713 No obvious data leakage patterns"}
{"event": "\u2713 No excessive feature correlations"}
{"event": "Training with 800 samples, 22 features"}
{"event": "Class distribution: {0: np.int64(578), 1: np.int64(222)}"}
{"event": "  simple: AUC = 0.577 \u00b1 0.016"}
{"event": "  medium: AUC = 0.579 \u00b1 0.028"}
{"event": "  complex: AUC = 0.558 \u00b1 0.025"}
{"event": "\u2713 No obvious overfitting detected"}
{"event": "\u2713 Model training validation completed"}
{"event": "Hyperparameter optimization with 400 samples"}
{"event": "\u2713 Optimization completed: best AUC = 0.674"}
{"event": "  Best params: {'n_estimators': 62, 'max_depth': 7, 'min_samples_split': 9}"}
{"event": "\u2713 Best model validation: AUC = 0.674 \u00b1 0.022"}
{"event": "\u2713 Utils imported successfully"}
{"event": "\u2713 Base model imported successfully"}
{"event": "HTTP Request: GET http://testserver/health \"HTTP/1.1 200 OK\""}
{"event": "HTTP Request: GET http://testserver/version \"HTTP/1.1 200 OK\""}
{"event": "\u2713 Simple API creation successful"}
{"event": "\u2713 Data processing pipeline successful: 7 features"}
{"event": "\u2713 ML pipeline successful: accuracy=0.940, auc=0.985"}
{"event": "\u2713 Config loaded: production"}
{"event": "{\"event\": \"Test log message\", \"logger\": \"test\", \"level\": \"info\", \"timestamp\": \"2025-07-23T09:20:38.817817Z\"}"}
{"event": "\u2713 Logging setup successful"}
{"event": "\u2713 Model validation: CV AUC = 0.974 \u00b1 0.010"}
{"event": "\u2713 Feature importance analysis: [0.82650522 0.0423593  0.03466291 0.04333298 0.05313959]"}
{"event": "\u2713 Data quality validation: {'high_missing': {'missing_feature': 0.2}, 'constant_features': ['missing_feature', 'constant_feature'], 'outlier_features': ['outlier_feature']}"}
{"event": "{\"config\": {\"environment\": \"production\", \"debug\": false, \"project_name\": \"sensei-ai\", \"version\": \"1.0.0\", \"bigquery\": {\"project_id\": \"datalake-sensei\", \"dataset_id\": \"serving_layer\", \"location\": \"EU\", \"max_bytes_billed\": ***********}, \"api\": {\"host\": \"0.0.0.0\", \"port\": 8000, \"workers\": 2, \"reload\": false}, \"models\": {\"cache_size_mb\": 1000, \"timeout_seconds\": 30, \"max_versions\": 3}, \"training\": {\"max_samples\": 10000, \"max_features\": 50, \"hyperopt_trials\": 50, \"timeout_minutes\": 30}}, \"event\": \"Configuration loaded\", \"logger\": \"sensei.utils.config\", \"level\": \"info\", \"timestamp\": \"2025-07-23T09:36:58.021398Z\"}"}
{"event": "{\"api_key\": \"sk-sensei-demo-QaTOs1XKK7BbdqkPFzYK46nsFKTeTfaumySvVpNskiA\", \"user_id\": \"admin\", \"event\": \"Default credentials created\", \"logger\": \"sensei.api.auth\", \"level\": \"info\", \"timestamp\": \"2025-07-23T09:36:58.021885Z\"}"}
{"event": "Erreur cr\u00e9ation client: No module named 'fastapi.middleware.base'"}
{"event": "Credentials: /Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/credentials/sensei-ai-service-account.json"}
{"event": "{\"path\": \"/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/credentials/sensei-ai-service-account.json\", \"event\": \"Credentials loaded from config\", \"logger\": \"sensei.data.bigquery_client\", \"level\": \"info\", \"timestamp\": \"2025-07-23T09:37:01.965117Z\"}"}
{"event": "Erreur connexion: Project must be a string."}
{"event": "Credentials: /Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/credentials/sensei-ai-service-account.json"}
{"event": "{\"path\": \"/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/credentials/sensei-ai-service-account.json\", \"event\": \"Credentials loaded from config\", \"logger\": \"sensei.data.bigquery_client\", \"level\": \"info\", \"timestamp\": \"2025-07-23T09:37:02.090339Z\"}"}
{"event": "Erreur acc\u00e8s serving_layer: Project must be a string."}
{"event": "Credentials: /Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/credentials/sensei-ai-service-account.json"}
{"event": "{\"path\": \"/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/credentials/sensei-ai-service-account.json\", \"event\": \"Credentials loaded from config\", \"logger\": \"sensei.data.bigquery_client\", \"level\": \"info\", \"timestamp\": \"2025-07-23T09:37:02.230733Z\"}"}
{"event": "Erreur acc\u00e8s donn\u00e9es conversion: Project must be a string."}
{"event": "Credentials: /Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/credentials/sensei-ai-service-account.json"}
{"event": "{\"path\": \"/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/credentials/sensei-ai-service-account.json\", \"event\": \"Credentials loaded from config\", \"logger\": \"sensei.data.bigquery_client\", \"level\": \"info\", \"timestamp\": \"2025-07-23T09:37:02.317391Z\"}"}
{"event": "Erreur acc\u00e8s donn\u00e9es HubSpot: Project must be a string."}
{"event": "Acc\u00e8s HubSpot non disponible: Project must be a string."}
{"event": "Credentials: /Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/credentials/sensei-ai-service-account.json"}
{"event": "{\"path\": \"/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/credentials/sensei-ai-service-account.json\", \"event\": \"Credentials loaded from config\", \"logger\": \"sensei.data.bigquery_client\", \"level\": \"info\", \"timestamp\": \"2025-07-23T09:37:02.372385Z\"}"}
{"event": "Erreur v\u00e9rification qualit\u00e9: Project must be a string."}
{"event": "Credentials: /Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/credentials/sensei-ai-service-account.json"}
{"event": "{\"path\": \"/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/credentials/sensei-ai-service-account.json\", \"event\": \"Credentials loaded from config\", \"logger\": \"sensei.data.bigquery_client\", \"level\": \"info\", \"timestamp\": \"2025-07-23T09:37:02.454740Z\"}"}
{"event": "Erreur test performance: Project must be a string."}
{"event": "{\"config\": {\"environment\": \"production\", \"debug\": false, \"project_name\": \"sensei-ai\", \"version\": \"1.0.0\", \"bigquery\": {\"project_id\": \"datalake-sensei\", \"dataset_id\": \"serving_layer\", \"location\": \"EU\", \"max_bytes_billed\": ***********}, \"api\": {\"host\": \"0.0.0.0\", \"port\": 8000, \"workers\": 2, \"reload\": false}, \"models\": {\"cache_size_mb\": 1000, \"timeout_seconds\": 30, \"max_versions\": 3}, \"training\": {\"max_samples\": 10000, \"max_features\": 50, \"hyperopt_trials\": 50, \"timeout_minutes\": 30}}, \"event\": \"Configuration loaded\", \"logger\": \"sensei.utils.config\", \"level\": \"info\", \"timestamp\": \"2025-07-23T10:32:11.037864Z\"}"}
{"event": "{\"api_key\": \"sk-sensei-demo-D6Nf9L9O5gD1mI-l8uzHQYqtsVBhPOU8X9Vi7AyfZgo\", \"user_id\": \"admin\", \"event\": \"Default credentials created\", \"logger\": \"sensei.api.auth\", \"level\": \"info\", \"timestamp\": \"2025-07-23T10:32:11.038251Z\"}"}
{"event": "Erreur feature engineering avanc\u00e9: No module named 'fastapi.middleware.base'"}
