# Sensei AI - B2B Sales Optimization Platform

🚀 **Production-ready ML platform for B2B sales optimization** powered by advanced machine learning and comprehensive data integration.

## 🌟 Features

### 🎯 Core ML Capabilities
- **Conversion Scoring**: Predict prospect conversion probability with 85%+ accuracy
- **Channel Optimization**: Recommend optimal communication channels (email, call, meeting)
- **Timing Intelligence**: Suggest best contact timing based on historical patterns
- **NLP Analysis**: Analyze sales conversation transcripts for insights and coaching

### 🏗️ Enterprise Architecture
- **Scalable FastAPI** backend with async processing
- **Advanced ML Pipeline** with automated feature engineering
- **Secure BigQuery Integration** with cost controls and audit logging
- **Production Monitoring** with Prometheus metrics and health checks
- **Docker Containerization** with multi-stage builds

### 🔒 Security & Compliance
- **API Key Authentication** with role-based access control
- **Comprehensive Audit Logging** for all operations
- **Rate Limiting** and request size controls
- **Security Headers** and CORS protection

## 🚀 Quick Start

### Prerequisites
- Python 3.11+
- Docker & Docker Compose
- Google Cloud Platform account with BigQuery access
- 8GB+ RAM recommended

### 1. Clone Repository
```bash
git clone https://github.com/your-org/sensei-ai.git
cd sensei-ai
```

### 2. Environment Setup
```bash
# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

### 3. Configuration
```bash
# Copy example configuration
cp config/config.example.toml config/config.toml

# Edit configuration with your settings
# - BigQuery project ID and dataset
# - API security settings
# - Model parameters
```

### 4. Google Cloud Setup
```bash
# Set up authentication
export GOOGLE_APPLICATION_CREDENTIALS="path/to/your/service-account.json"

# Or place credentials in config/gcp-credentials.json
```

### 5. Start the Application
```bash
# Using Docker Compose (recommended)
docker-compose up -d

# Or run directly
python main.py serve
```

### 6. Verify Installation
```bash
# Check system health
curl http://localhost:8000/health

# Get API documentation
open http://localhost:8000/docs
```

## 📊 Data Integration

### Supported Data Sources
- **HubSpot CRM**: Contact data, engagement metrics, deal information
- **Typeform**: Form submissions, response patterns, completion rates
- **Modjo**: Call recordings, transcripts, conversation scores
- **BigQuery**: Centralized data warehouse with serving layer views

### Required BigQuery Views
The system expects these views in your BigQuery dataset:

```sql
-- Contact dimension
CREATE VIEW `your-project.serving_layer.vw_dim_contact` AS ...

-- Typeform responses
CREATE VIEW `your-project.serving_layer.vw_reponses_typeform_deno` AS ...

-- Transaction facts
CREATE VIEW `your-project.serving_layer.vw_fact_transaction` AS ...

-- Modjo conversation data
CREATE VIEW `your-project.serving_layer.vw_dim_modjo_call_summary` AS ...
```

## 🤖 ML Models

### Conversion Prediction Model
- **Algorithm**: Ensemble (LightGBM + CatBoost + Random Forest)
- **Features**: 50+ engineered features from CRM, forms, and engagement data
- **Performance**: 85%+ accuracy, 0.92 AUC-ROC
- **Output**: Probability score (0-1) with confidence and recommendations

### Channel Timing Optimizer
- **Algorithm**: Multi-class classification with time-series features
- **Features**: Historical interaction patterns, success rates, timing preferences
- **Output**: Ranked channel recommendations with optimal timing windows

### NLP Conversation Analyzer
- **Algorithm**: Transformer-based NLP with clustering and topic modeling
- **Features**: Sentiment analysis, speaking patterns, topic extraction
- **Output**: Quality scores, engagement metrics, coaching insights

## 🔧 API Usage

### Authentication
```bash
# Get API key from admin
API_KEY="sk-sensei-your-api-key"

# Use in requests
curl -H "Authorization: Bearer $API_KEY" \
     http://localhost:8000/conversion/predict
```

### Conversion Prediction
```python
import requests

response = requests.post(
    "http://localhost:8000/conversion/predict",
    headers={"Authorization": f"Bearer {API_KEY}"},
    json={
        "prospect_id": "contact-123",
        "hubspot_score": 75,
        "email_count": 5,
        "call_count": 2,
        "typeform_submissions": 1,
        "days_since_last_engagement": 7
    }
)

result = response.json()
print(f"Conversion probability: {result['conversion_probability']:.2%}")
print(f"Risk category: {result['risk_category']}")
```

### Channel Optimization
```python
response = requests.post(
    "http://localhost:8000/channel-timing/recommend",
    headers={"Authorization": f"Bearer {API_KEY}"},
    json={
        "prospect_id": "contact-456",
        "email_success_rate": 0.6,
        "call_success_rate": 0.8,
        "meeting_success_rate": 0.9,
        "deal_value": 50000
    }
)

result = response.json()
print(f"Recommended channel: {result['primary_recommendation']['channel']}")
print(f"Optimal times: {result['optimal_contact_times']}")
```

### NLP Analysis
```python
response = requests.post(
    "http://localhost:8000/nlp/analyze",
    headers={"Authorization": f"Bearer {API_KEY}"},
    json={
        "conversation_id": "call-789",
        "transcript": "Your conversation transcript here...",
        "duration_minutes": 30
    }
)

result = response.json()
print(f"Quality score: {result['overall_quality_score']}/100")
print(f"Sentiment: {result['sentiment_score']:.2f}")
print(f"Key topics: {result['main_topics']}")
```

## 🛠️ Administration

### Model Training
```bash
# Train conversion model
python main.py train conversion --max-samples 10000

# Train channel timing model
python main.py train channel-timing --max-samples 5000

# Train NLP model
python main.py train nlp --max-samples 1000
```

### User Management
```bash
# Create user
python main.py admin create-user \
    --username john.doe \
    --email <EMAIL> \
    --roles admin

# Create API key
python main.py admin create-api-key \
    --user-id john.doe \
    --name "Production API Key" \
    --permissions read,write \
    --expires-days 365
```

### Data Pipeline
```bash
# Extract features
python main.py pipeline extract --feature-type conversion --limit 1000

# Check system health
python main.py health
```

## 📈 Monitoring

### Health Checks
- **Liveness**: `/health/liveness` - Service is running
- **Readiness**: `/health/readiness` - Service is ready for traffic
- **Health**: `/health/` - Comprehensive system health

### Metrics
- **Prometheus**: http://localhost:9090
- **Grafana**: http://localhost:3000 (admin/admin)
- **API Metrics**: Request rates, latencies, error rates
- **Model Metrics**: Prediction counts, accuracy tracking

### Logging
- **Structured JSON logs** with correlation IDs
- **Audit trail** for all API operations
- **Performance tracking** for all components
- **Error tracking** with stack traces

## 🧪 Testing

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=src --cov-report=html

# Run specific test categories
pytest tests/test_api.py -v
pytest tests/test_models.py -v
```

## 🚀 Deployment

### Docker Production
```bash
# Build production image
docker build -t sensei-ai:latest .

# Run with production settings
docker run -d \
    -p 8000:8000 \
    -e SENSEI_ENVIRONMENT=production \
    -v /path/to/config:/app/config \
    sensei-ai:latest
```

### Kubernetes
```yaml
# See k8s/ directory for complete manifests
apiVersion: apps/v1
kind: Deployment
metadata:
  name: sensei-ai
spec:
  replicas: 3
  selector:
    matchLabels:
      app: sensei-ai
  template:
    spec:
      containers:
      - name: sensei-ai
        image: sensei-ai:latest
        ports:
        - containerPort: 8000
```

## 📋 Configuration

### Environment Variables
```bash
# Core settings
SENSEI_ENVIRONMENT=production
SENSEI_DEBUG=false
SENSEI_LOG_LEVEL=INFO

# Database
GOOGLE_APPLICATION_CREDENTIALS=/path/to/credentials.json
BIGQUERY_PROJECT_ID=your-project
BIGQUERY_DATASET=serving_layer

# Security
SENSEI_SECRET_KEY=your-secret-key
SENSEI_RATE_LIMIT_PER_MINUTE=1000

# Models
SENSEI_MODEL_DIR=/app/models
SENSEI_MAX_TRAINING_SAMPLES=50000
```

## 🤝 Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

### Development Setup
```bash
# Install development dependencies
pip install -r requirements-dev.txt

# Install pre-commit hooks
pre-commit install

# Run linting
black src/ tests/
flake8 src/ tests/
mypy src/
```

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: [docs/](docs/)
- **Issues**: [GitHub Issues](https://github.com/your-org/sensei-ai/issues)
- **Discussions**: [GitHub Discussions](https://github.com/your-org/sensei-ai/discussions)
- **Email**: <EMAIL>

## 🙏 Acknowledgments

- **FastAPI** for the excellent web framework
- **scikit-learn** for machine learning capabilities
- **Google Cloud BigQuery** for data warehousing
- **Prometheus** for monitoring and metrics
- **Docker** for containerization

---

**Built with ❤️ by the Sensei AI Team**
