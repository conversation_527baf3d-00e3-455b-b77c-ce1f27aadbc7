"""
Système de Monitoring et Métriques ML Avancé pour Sensei AI v2.0

Fournit un monitoring complet, des métriques détaillées et l'explainability des modèles.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
import logging
from datetime import datetime, timedelta
import json
import pickle
from pathlib import Path
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import (
    accuracy_score, precision_score, recall_score, f1_score, roc_auc_score,
    confusion_matrix, classification_report, roc_curve, precision_recall_curve
)
from sklearn.inspection import permutation_importance
import shap
import warnings
warnings.filterwarnings("ignore")

logger = logging.getLogger(__name__)


class MLMonitoringSystem:
    """Système de monitoring ML complet."""
    
    def __init__(self, model_name: str = "sensei_v2", output_dir: str = "monitoring_output"):
        self.model_name = model_name
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        self.metrics_history = []
        self.feature_importance_history = []
        self.prediction_logs = []
        self.data_drift_logs = []
        
        # Seuils d'alerte
        self.alert_thresholds = {
            'auc_min': 0.7,
            'accuracy_min': 0.75,
            'precision_min': 0.6,
            'recall_min': 0.5,
            'data_drift_max': 0.3,
            'feature_importance_change_max': 0.5
        }
    
    def evaluate_model_comprehensive(self, model, X_test: pd.DataFrame, y_test: pd.Series, 
                                   feature_names: List[str] = None) -> Dict:
        """Évaluation complète du modèle avec toutes les métriques."""
        logger.info("📊 Évaluation complète du modèle...")
        
        if feature_names is None:
            feature_names = X_test.columns.tolist() if hasattr(X_test, 'columns') else [f'feature_{i}' for i in range(X_test.shape[1])]
        
        # Prédictions
        y_pred = model.predict(X_test)
        y_pred_proba = model.predict_proba(X_test)[:, 1] if hasattr(model, 'predict_proba') else None
        
        # Métriques de base
        metrics = {
            'timestamp': datetime.now().isoformat(),
            'model_name': self.model_name,
            'test_size': len(X_test),
            'positive_rate': y_test.mean(),
            'accuracy': accuracy_score(y_test, y_pred),
            'precision': precision_score(y_test, y_pred, average='weighted'),
            'recall': recall_score(y_test, y_pred, average='weighted'),
            'f1_score': f1_score(y_test, y_pred, average='weighted')
        }
        
        if y_pred_proba is not None:
            metrics['auc_roc'] = roc_auc_score(y_test, y_pred_proba)
        
        # Matrice de confusion
        cm = confusion_matrix(y_test, y_pred)
        metrics['confusion_matrix'] = {
            'tn': int(cm[0, 0]),
            'fp': int(cm[0, 1]),
            'fn': int(cm[1, 0]),
            'tp': int(cm[1, 1])
        }
        
        # Métriques par classe
        class_report = classification_report(y_test, y_pred, output_dict=True)
        metrics['class_metrics'] = class_report
        
        # Feature importance
        if hasattr(model, 'feature_importances_'):
            feature_importance = dict(zip(feature_names, model.feature_importances_))
            metrics['feature_importance'] = feature_importance
            
            # Top 10 features
            sorted_features = sorted(feature_importance.items(), key=lambda x: x[1], reverse=True)
            metrics['top_features'] = dict(sorted_features[:10])
        
        # Permutation importance
        try:
            perm_importance = permutation_importance(model, X_test, y_test, n_repeats=5, random_state=42)
            perm_importance_dict = dict(zip(feature_names, perm_importance.importances_mean))
            metrics['permutation_importance'] = perm_importance_dict
        except Exception as e:
            logger.warning(f"⚠️ Erreur permutation importance: {e}")
        
        # Sauvegarder les métriques
        self.metrics_history.append(metrics)
        self._save_metrics(metrics)
        
        # Vérifier les alertes
        alerts = self._check_alerts(metrics)
        if alerts:
            metrics['alerts'] = alerts
            logger.warning(f"🚨 Alertes détectées: {alerts}")
        
        logger.info(f"✅ Évaluation terminée - AUC: {metrics.get('auc_roc', 'N/A'):.3f}")
        
        return metrics
    
    def explain_predictions(self, model, X_sample: pd.DataFrame, 
                          feature_names: List[str] = None, n_samples: int = 100) -> Dict:
        """Explique les prédictions avec SHAP."""
        logger.info("🔍 Explication des prédictions avec SHAP...")
        
        if feature_names is None:
            feature_names = X_sample.columns.tolist() if hasattr(X_sample, 'columns') else [f'feature_{i}' for i in range(X_sample.shape[1])]
        
        try:
            # Limiter le nombre d'échantillons pour performance
            sample_size = min(n_samples, len(X_sample))
            X_explain = X_sample.iloc[:sample_size] if hasattr(X_sample, 'iloc') else X_sample[:sample_size]
            
            # Créer l'explainer SHAP
            explainer = shap.TreeExplainer(model)
            shap_values = explainer.shap_values(X_explain)
            
            # Si classification binaire, prendre les valeurs pour la classe positive
            if isinstance(shap_values, list) and len(shap_values) == 2:
                shap_values = shap_values[1]
            
            # Calculer les importances moyennes
            mean_shap_values = np.abs(shap_values).mean(axis=0)
            shap_importance = dict(zip(feature_names, mean_shap_values))
            
            # Top features par SHAP
            sorted_shap = sorted(shap_importance.items(), key=lambda x: x[1], reverse=True)
            
            explanation = {
                'timestamp': datetime.now().isoformat(),
                'sample_size': sample_size,
                'shap_importance': shap_importance,
                'top_shap_features': dict(sorted_shap[:10]),
                'shap_values_summary': {
                    'mean_abs_shap': float(np.abs(shap_values).mean()),
                    'max_abs_shap': float(np.abs(shap_values).max()),
                    'feature_with_max_impact': feature_names[np.abs(shap_values).mean(axis=0).argmax()]
                }
            }
            
            # Sauvegarder les valeurs SHAP pour visualisation
            shap_df = pd.DataFrame(shap_values, columns=feature_names)
            shap_df.to_csv(self.output_dir / f"shap_values_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv", index=False)
            
            logger.info(f"✅ Explication SHAP terminée - Top feature: {explanation['shap_values_summary']['feature_with_max_impact']}")
            
            return explanation
            
        except Exception as e:
            logger.error(f"❌ Erreur explication SHAP: {e}")
            return {'error': str(e)}
    
    def monitor_data_drift(self, X_train: pd.DataFrame, X_current: pd.DataFrame, 
                          feature_names: List[str] = None) -> Dict:
        """Monitore la dérive des données."""
        logger.info("📈 Monitoring de la dérive des données...")
        
        if feature_names is None:
            feature_names = X_train.columns.tolist() if hasattr(X_train, 'columns') else [f'feature_{i}' for i in range(X_train.shape[1])]
        
        drift_results = {
            'timestamp': datetime.now().isoformat(),
            'features_analyzed': len(feature_names),
            'drift_scores': {},
            'drifted_features': [],
            'drift_summary': {}
        }
        
        for i, feature in enumerate(feature_names):
            try:
                # Extraire les colonnes
                if hasattr(X_train, 'iloc'):
                    train_values = X_train.iloc[:, i]
                    current_values = X_current.iloc[:, i]
                else:
                    train_values = X_train[:, i]
                    current_values = X_current[:, i]
                
                # Calculer la dérive (KS test approximation)
                train_mean = np.mean(train_values)
                train_std = np.std(train_values)
                current_mean = np.mean(current_values)
                current_std = np.std(current_values)
                
                # Score de dérive simple
                mean_drift = abs(current_mean - train_mean) / (train_std + 1e-8)
                std_drift = abs(current_std - train_std) / (train_std + 1e-8)
                drift_score = (mean_drift + std_drift) / 2
                
                drift_results['drift_scores'][feature] = float(drift_score)
                
                # Marquer comme dérivé si score > seuil
                if drift_score > self.alert_thresholds['data_drift_max']:
                    drift_results['drifted_features'].append(feature)
                
            except Exception as e:
                logger.warning(f"⚠️ Erreur calcul dérive pour {feature}: {e}")
        
        # Résumé de la dérive
        drift_scores = list(drift_results['drift_scores'].values())
        drift_results['drift_summary'] = {
            'mean_drift_score': float(np.mean(drift_scores)),
            'max_drift_score': float(np.max(drift_scores)),
            'num_drifted_features': len(drift_results['drifted_features']),
            'drift_percentage': len(drift_results['drifted_features']) / len(feature_names) * 100
        }
        
        # Sauvegarder
        self.data_drift_logs.append(drift_results)
        
        # Alertes
        if drift_results['drift_summary']['drift_percentage'] > 20:
            logger.warning(f"🚨 Dérive significative détectée: {drift_results['drift_summary']['drift_percentage']:.1f}% des features")
        
        logger.info(f"✅ Monitoring dérive terminé - {len(drift_results['drifted_features'])} features dérivées")
        
        return drift_results
    
    def generate_model_report(self, model, X_test: pd.DataFrame, y_test: pd.Series,
                            X_train: pd.DataFrame = None, feature_names: List[str] = None) -> Dict:
        """Génère un rapport complet du modèle."""
        logger.info("📋 Génération du rapport complet...")
        
        report = {
            'metadata': {
                'model_name': self.model_name,
                'report_timestamp': datetime.now().isoformat(),
                'test_samples': len(X_test),
                'features_count': X_test.shape[1]
            }
        }
        
        # Évaluation complète
        evaluation = self.evaluate_model_comprehensive(model, X_test, y_test, feature_names)
        report['evaluation'] = evaluation
        
        # Explication des prédictions
        explanation = self.explain_predictions(model, X_test, feature_names, n_samples=50)
        report['explanation'] = explanation
        
        # Monitoring de dérive si données d'entraînement disponibles
        if X_train is not None:
            drift_analysis = self.monitor_data_drift(X_train, X_test, feature_names)
            report['data_drift'] = drift_analysis
        
        # Recommandations
        recommendations = self._generate_recommendations(report)
        report['recommendations'] = recommendations
        
        # Sauvegarder le rapport
        report_file = self.output_dir / f"model_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        logger.info(f"✅ Rapport sauvegardé: {report_file}")
        
        return report
    
    def _check_alerts(self, metrics: Dict) -> List[str]:
        """Vérifie les seuils d'alerte."""
        alerts = []
        
        if metrics.get('auc_roc', 1.0) < self.alert_thresholds['auc_min']:
            alerts.append(f"AUC faible: {metrics['auc_roc']:.3f} < {self.alert_thresholds['auc_min']}")
        
        if metrics.get('accuracy', 1.0) < self.alert_thresholds['accuracy_min']:
            alerts.append(f"Accuracy faible: {metrics['accuracy']:.3f} < {self.alert_thresholds['accuracy_min']}")
        
        if metrics.get('precision', 1.0) < self.alert_thresholds['precision_min']:
            alerts.append(f"Précision faible: {metrics['precision']:.3f} < {self.alert_thresholds['precision_min']}")
        
        if metrics.get('recall', 1.0) < self.alert_thresholds['recall_min']:
            alerts.append(f"Rappel faible: {metrics['recall']:.3f} < {self.alert_thresholds['recall_min']}")
        
        return alerts
    
    def _generate_recommendations(self, report: Dict) -> List[str]:
        """Génère des recommandations basées sur le rapport."""
        recommendations = []
        
        evaluation = report.get('evaluation', {})
        
        # Recommandations basées sur les métriques
        if evaluation.get('auc_roc', 1.0) < 0.8:
            recommendations.append("Considérer l'ajout de nouvelles features ou l'optimisation des hyperparamètres")
        
        if evaluation.get('precision', 1.0) < 0.7:
            recommendations.append("Améliorer la précision en ajustant le seuil de classification ou en équilibrant les données")
        
        if evaluation.get('recall', 1.0) < 0.6:
            recommendations.append("Améliorer le rappel en réduisant les faux négatifs")
        
        # Recommandations basées sur la dérive
        drift_data = report.get('data_drift', {})
        if drift_data.get('drift_summary', {}).get('drift_percentage', 0) > 15:
            recommendations.append("Dérive des données détectée - considérer un réentraînement du modèle")
        
        # Recommandations basées sur les features
        explanation = report.get('explanation', {})
        if explanation.get('shap_values_summary', {}).get('mean_abs_shap', 0) < 0.1:
            recommendations.append("Impact des features faible - vérifier la qualité des données d'entrée")
        
        if not recommendations:
            recommendations.append("Modèle performant - continuer le monitoring régulier")
        
        return recommendations
    
    def _save_metrics(self, metrics: Dict):
        """Sauvegarde les métriques."""
        metrics_file = self.output_dir / "metrics_history.json"
        
        # Charger l'historique existant
        if metrics_file.exists():
            with open(metrics_file, 'r') as f:
                history = json.load(f)
        else:
            history = []
        
        # Ajouter les nouvelles métriques
        history.append(metrics)
        
        # Sauvegarder
        with open(metrics_file, 'w') as f:
            json.dump(history, f, indent=2, default=str)
    
    def get_monitoring_dashboard_data(self) -> Dict:
        """Retourne les données pour le dashboard de monitoring."""
        return {
            'metrics_history': self.metrics_history[-10:],  # 10 dernières évaluations
            'current_alerts': self._get_current_alerts(),
            'feature_importance_trends': self._get_feature_importance_trends(),
            'model_health_score': self._calculate_model_health_score()
        }
    
    def _get_current_alerts(self) -> List[str]:
        """Retourne les alertes actuelles."""
        if not self.metrics_history:
            return []
        
        latest_metrics = self.metrics_history[-1]
        return self._check_alerts(latest_metrics)
    
    def _get_feature_importance_trends(self) -> Dict:
        """Analyse les tendances d'importance des features."""
        if len(self.metrics_history) < 2:
            return {}
        
        # Comparer les 2 dernières évaluations
        current = self.metrics_history[-1].get('feature_importance', {})
        previous = self.metrics_history[-2].get('feature_importance', {})
        
        trends = {}
        for feature in current.keys():
            if feature in previous:
                change = current[feature] - previous[feature]
                trends[feature] = {
                    'current': current[feature],
                    'previous': previous[feature],
                    'change': change,
                    'change_percentage': (change / previous[feature] * 100) if previous[feature] > 0 else 0
                }
        
        return trends
    
    def _calculate_model_health_score(self) -> float:
        """Calcule un score de santé global du modèle."""
        if not self.metrics_history:
            return 0.0
        
        latest = self.metrics_history[-1]
        
        # Pondération des métriques
        weights = {
            'auc_roc': 0.3,
            'accuracy': 0.2,
            'precision': 0.2,
            'recall': 0.2,
            'f1_score': 0.1
        }
        
        health_score = 0.0
        total_weight = 0.0
        
        for metric, weight in weights.items():
            if metric in latest:
                health_score += latest[metric] * weight
                total_weight += weight
        
        return health_score / total_weight if total_weight > 0 else 0.0


if __name__ == "__main__":
    # Test du système de monitoring
    monitoring = MLMonitoringSystem("test_model")
    print("Système de monitoring initialisé")
    print(f"Seuils d'alerte: {monitoring.alert_thresholds}")
    print(f"Répertoire de sortie: {monitoring.output_dir}")
