"""
Feature Engineering Avancé pour Sensei AI v2.0

Intègre les données Typeform, Modjo et engagement multi-canal pour créer
des features de nouvelle génération.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
import logging
from datetime import datetime, timedelta
from google.cloud import bigquery
import warnings
warnings.filterwarnings("ignore")

logger = logging.getLogger(__name__)


class AdvancedFeatureEngineer:
    """Feature Engineer avancé avec données multi-sources."""
    
    def __init__(self, client: bigquery.Client):
        self.client = client
        self.feature_cache = {}
        self.feature_metadata = {}
    
    def extract_typeform_features(self, contact_ids: List[str]) -> pd.DataFrame:
        """Extrait les features Typeform pour les contacts."""
        logger.info("📋 Extraction des features Typeform...")
        
        if not contact_ids:
            return pd.DataFrame()
        
        # Convertir les IDs en format compatible
        ids_str = "', '".join(str(id) for id in contact_ids)
        
        query = f"""
        WITH typeform_stats AS (
            SELECT 
                sf.id_prospect,
                COUNT(DISTINCT sf.id_soumission_formulaire) as nb_soumissions,
                AVG(sf.duree_reponses_minutes) as duree_moyenne_reponses,
                MIN(sf.dt_soumission) as premiere_soumission,
                MAX(sf.dt_soumission) as derniere_soumission,
                COUNT(DISTINCT r.id_reponse) as nb_reponses_total,
                COUNT(DISTINCT CASE WHEN q.type_question = 'rating' THEN r.id_reponse END) as nb_ratings,
                COUNT(DISTINCT CASE WHEN q.type_question = 'multiple_choice' THEN r.id_reponse END) as nb_choix_multiples,
                COUNT(DISTINCT CASE WHEN q.type_question = 'yes_no' THEN r.id_reponse END) as nb_yes_no,
                AVG(CASE WHEN q.type_question = 'rating' AND r.valeur_reponse IS NOT NULL 
                    THEN SAFE_CAST(r.valeur_reponse AS FLOAT64) END) as rating_moyen
            FROM `datalake-sensei.serving_layer.vw_fact_soumission_formulaire` sf
            LEFT JOIN `datalake-sensei.serving_layer.vw_dim_reponse` r 
                ON sf.id_soumission_formulaire = r.id_soumission_formulaire
            LEFT JOIN `datalake-sensei.serving_layer.vw_dim_option` o 
                ON r.id_option = o.id_option
            LEFT JOIN `datalake-sensei.serving_layer.vw_dim_choix` c 
                ON o.id_choix = c.id_choix
            LEFT JOIN `datalake-sensei.serving_layer.vw_dim_question` q 
                ON c.id_question = q.id_question
            WHERE sf.id_prospect IN ('{ids_str}')
            GROUP BY sf.id_prospect
        )
        SELECT 
            id_prospect,
            nb_soumissions,
            duree_moyenne_reponses,
            DATETIME_DIFF(CURRENT_DATETIME(), premiere_soumission, DAY) as jours_depuis_premiere_soumission,
            DATETIME_DIFF(derniere_soumission, premiere_soumission, DAY) as periode_activite_typeform,
            nb_reponses_total,
            nb_ratings,
            nb_choix_multiples,
            nb_yes_no,
            rating_moyen,
            -- Features dérivées
            SAFE_DIVIDE(nb_reponses_total, nb_soumissions) as reponses_par_soumission,
            CASE 
                WHEN duree_moyenne_reponses < 1 THEN 'rapide'
                WHEN duree_moyenne_reponses < 5 THEN 'normal'
                ELSE 'reflechi'
            END as profil_reponse,
            CASE 
                WHEN rating_moyen >= 4 THEN 'positif'
                WHEN rating_moyen >= 2.5 THEN 'neutre'
                ELSE 'negatif'
            END as sentiment_rating
        FROM typeform_stats
        """
        
        try:
            result = self.client.query(query).result()
            df = result.to_dataframe()
            
            if len(df) > 0:
                # Renommer la colonne pour correspondre aux contacts
                df = df.rename(columns={'id_prospect': 'id_contact'})
                
                logger.info(f"✅ Features Typeform: {len(df)} contacts, {len(df.columns)} features")
                
                # Métadonnées des features
                self.feature_metadata['typeform'] = {
                    'nb_soumissions': 'Nombre de formulaires soumis',
                    'duree_moyenne_reponses': 'Temps moyen de réponse (minutes)',
                    'jours_depuis_premiere_soumission': 'Ancienneté engagement Typeform',
                    'rating_moyen': 'Score de satisfaction moyen',
                    'reponses_par_soumission': 'Engagement par formulaire',
                    'profil_reponse': 'Profil comportemental (rapide/normal/réfléchi)',
                    'sentiment_rating': 'Sentiment global (positif/neutre/négatif)'
                }
            
            return df
            
        except Exception as e:
            logger.warning(f"⚠️ Erreur extraction Typeform: {e}")
            return pd.DataFrame()
    
    def extract_modjo_features(self, contact_ids: List[str]) -> pd.DataFrame:
        """Extrait les features Modjo pour les contacts."""
        logger.info("📞 Extraction des features Modjo...")
        
        if not contact_ids:
            return pd.DataFrame()
        
        ids_str = "', '".join(str(id) for id in contact_ids)
        
        query = f"""
        WITH modjo_stats AS (
            SELECT 
                fc.contactCrmId as id_contact,
                COUNT(DISTINCT fc.callId) as nb_appels,
                AVG(fc.duration) as duree_moyenne_appels,
                SUM(fc.duration) as duree_totale_appels,
                MIN(fc.startDate) as premier_appel,
                MAX(fc.startDate) as dernier_appel,
                COUNT(DISTINCT fc.userId) as nb_users_differents,
                COUNT(DISTINCT fc.dealId) as nb_deals_associes
            FROM `datalake-sensei.serving_layer.vw_fact_modjo_call` fc
            WHERE fc.contactCrmId IN ('{ids_str}')
            GROUP BY fc.contactCrmId
        ),
        modjo_scores AS (
            SELECT 
                fc.contactCrmId as id_contact,
                AVG(cs.score_appel_prequai) as score_prequai_moyen,
                AVG(cs.score_ice_breaker) as score_ice_breaker_moyen,
                AVG(cs.score_phase_decouverte) as score_decouverte_moyen,
                AVG(cs.score_consolider_vente) as score_vente_moyen,
                AVG(cs.score_visio_vente) as score_visio_moyen,
                COUNT(CASE WHEN cs.score_appel_prequai IS NOT NULL THEN 1 END) as nb_appels_avec_scores
            FROM `datalake-sensei.serving_layer.vw_fact_modjo_call` fc
            LEFT JOIN `datalake-sensei.serving_layer.vw_dim_modjo_call_summary` cs 
                ON fc.callId = cs.callId
            WHERE fc.contactCrmId IN ('{ids_str}')
            GROUP BY fc.contactCrmId
        )
        SELECT 
            COALESCE(ms.id_contact, msc.id_contact) as id_contact,
            COALESCE(ms.nb_appels, 0) as nb_appels,
            COALESCE(ms.duree_moyenne_appels, 0) as duree_moyenne_appels,
            COALESCE(ms.duree_totale_appels, 0) as duree_totale_appels,
            COALESCE(ms.nb_users_differents, 0) as nb_users_differents,
            COALESCE(ms.nb_deals_associes, 0) as nb_deals_associes,
            COALESCE(msc.score_prequai_moyen, 0) as score_prequai_moyen,
            COALESCE(msc.score_ice_breaker_moyen, 0) as score_ice_breaker_moyen,
            COALESCE(msc.score_decouverte_moyen, 0) as score_decouverte_moyen,
            COALESCE(msc.score_vente_moyen, 0) as score_vente_moyen,
            COALESCE(msc.score_visio_moyen, 0) as score_visio_moyen,
            COALESCE(msc.nb_appels_avec_scores, 0) as nb_appels_avec_scores,
            -- Features dérivées
            CASE 
                WHEN ms.premier_appel IS NOT NULL 
                THEN DATETIME_DIFF(CURRENT_DATETIME(), ms.premier_appel, DAY) 
                ELSE NULL 
            END as jours_depuis_premier_appel,
            CASE 
                WHEN ms.premier_appel IS NOT NULL AND ms.dernier_appel IS NOT NULL
                THEN DATETIME_DIFF(ms.dernier_appel, ms.premier_appel, DAY) 
                ELSE 0 
            END as periode_activite_appels,
            CASE 
                WHEN ms.nb_appels > 0 
                THEN ms.duree_totale_appels / ms.nb_appels 
                ELSE 0 
            END as duree_moyenne_calculee,
            -- Score composite Modjo
            (COALESCE(msc.score_prequai_moyen, 0) + 
             COALESCE(msc.score_decouverte_moyen, 0) + 
             COALESCE(msc.score_vente_moyen, 0)) / 3 as score_modjo_composite
        FROM modjo_stats ms
        FULL OUTER JOIN modjo_scores msc ON ms.id_contact = msc.id_contact
        """
        
        try:
            result = self.client.query(query).result()
            df = result.to_dataframe()
            
            if len(df) > 0:
                logger.info(f"✅ Features Modjo: {len(df)} contacts, {len(df.columns)} features")
                
                # Métadonnées des features
                self.feature_metadata['modjo'] = {
                    'nb_appels': 'Nombre d\'appels Modjo',
                    'duree_moyenne_appels': 'Durée moyenne des appels (secondes)',
                    'score_modjo_composite': 'Score composite qualité conversation',
                    'score_prequai_moyen': 'Score IA préqualification',
                    'score_decouverte_moyen': 'Score IA phase découverte',
                    'jours_depuis_premier_appel': 'Ancienneté relation téléphonique'
                }
            
            return df
            
        except Exception as e:
            logger.warning(f"⚠️ Erreur extraction Modjo: {e}")
            return pd.DataFrame()
    
    def extract_multichannel_engagement(self, contact_ids: List[str]) -> pd.DataFrame:
        """Extrait les features d'engagement multi-canal."""
        logger.info("🔄 Extraction des features multi-canal...")
        
        if not contact_ids:
            return pd.DataFrame()
        
        ids_str = "', '".join(str(id) for id in contact_ids)
        
        query = f"""
        WITH engagement_stats AS (
            SELECT 
                c.id_contact,
                -- Emails
                COUNT(DISTINCT e.id_email) as nb_emails,
                COUNT(DISTINCT CASE WHEN e.id_contact_emetteur = c.id_contact THEN e.id_email END) as nb_emails_envoyes,
                COUNT(DISTINCT CASE WHEN e.id_contact_destinataire = c.id_contact THEN e.id_email END) as nb_emails_recus,
                -- Réunions
                COUNT(DISTINCT r.id_reunion) as nb_reunions,
                -- Appels HubSpot
                COUNT(DISTINCT a.id_appel) as nb_appels_hubspot,
                AVG(CASE WHEN a.hs_call_duration IS NOT NULL THEN a.hs_call_duration END) as duree_moyenne_appels_hubspot,
                -- Dates d'engagement
                MIN(LEAST(
                    COALESCE(e.hs_createdate, '2099-01-01'),
                    COALESCE(r.dt_creation_reunion, '2099-01-01'),
                    COALESCE(a.dt_creation_appel, '2099-01-01')
                )) as premier_engagement,
                MAX(GREATEST(
                    COALESCE(e.hs_createdate, '1900-01-01'),
                    COALESCE(r.dt_creation_reunion, '1900-01-01'),
                    COALESCE(a.dt_creation_appel, '1900-01-01')
                )) as dernier_engagement
            FROM `datalake-sensei.serving_layer.vw_dim_contact` c
            LEFT JOIN `datalake-sensei.serving_layer.vw_dim_email` e 
                ON c.id_contact IN (e.id_contact_emetteur, e.id_contact_destinataire)
            LEFT JOIN `datalake-sensei.serving_layer.vw_dim_reunion` r 
                ON CAST(c.id_contact AS STRING) = r.hs_created_by
            LEFT JOIN `datalake-sensei.serving_layer.vw_dim_appel` a 
                ON c.id_contact = SAFE_CAST(a.hs_call_callee_object_type AS INT64)
            WHERE c.id_contact IN ({ids_str})
            GROUP BY c.id_contact
        )
        SELECT 
            id_contact,
            nb_emails,
            nb_emails_envoyes,
            nb_emails_recus,
            nb_reunions,
            nb_appels_hubspot,
            duree_moyenne_appels_hubspot,
            -- Features dérivées
            (nb_emails + nb_reunions + nb_appels_hubspot) as engagement_total,
            CASE 
                WHEN premier_engagement < '2099-01-01' 
                THEN DATETIME_DIFF(CURRENT_DATETIME(), premier_engagement, DAY) 
                ELSE NULL 
            END as jours_depuis_premier_engagement,
            CASE 
                WHEN premier_engagement < '2099-01-01' AND dernier_engagement > '1900-01-01'
                THEN DATETIME_DIFF(dernier_engagement, premier_engagement, DAY) 
                ELSE 0 
            END as periode_engagement_total,
            -- Ratios d'engagement
            SAFE_DIVIDE(nb_emails_envoyes, NULLIF(nb_emails, 0)) as ratio_emails_sortants,
            SAFE_DIVIDE(nb_reunions, NULLIF(nb_emails + nb_appels_hubspot, 0)) as ratio_reunions_vs_autres,
            -- Score d'engagement multi-canal
            (nb_emails * 0.3 + nb_reunions * 0.5 + nb_appels_hubspot * 0.2) as score_engagement_pondere,
            -- Profil d'engagement
            CASE 
                WHEN nb_emails > 10 AND nb_reunions > 2 THEN 'high_engagement'
                WHEN nb_emails > 5 OR nb_reunions > 1 THEN 'medium_engagement'
                WHEN nb_emails > 0 OR nb_appels_hubspot > 0 THEN 'low_engagement'
                ELSE 'no_engagement'
            END as profil_engagement
        FROM engagement_stats
        """
        
        try:
            result = self.client.query(query).result()
            df = result.to_dataframe()
            
            if len(df) > 0:
                logger.info(f"✅ Features Multi-canal: {len(df)} contacts, {len(df.columns)} features")
                
                # Métadonnées des features
                self.feature_metadata['multichannel'] = {
                    'engagement_total': 'Score d\'engagement total (tous canaux)',
                    'score_engagement_pondere': 'Score d\'engagement pondéré par importance canal',
                    'ratio_emails_sortants': 'Ratio d\'emails sortants (proactivité)',
                    'profil_engagement': 'Profil d\'engagement (high/medium/low/none)',
                    'jours_depuis_premier_engagement': 'Ancienneté de la relation'
                }
            
            return df
            
        except Exception as e:
            logger.warning(f"⚠️ Erreur extraction multi-canal: {e}")
            return pd.DataFrame()
    
    def create_advanced_features(self, base_df: pd.DataFrame) -> pd.DataFrame:
        """Crée des features avancées en combinant toutes les sources."""
        logger.info("🚀 Création des features avancées...")
        
        if 'id_contact' not in base_df.columns:
            logger.error("❌ Colonne id_contact manquante dans base_df")
            return base_df
        
        contact_ids = base_df['id_contact'].astype(str).tolist()
        
        # Extraire les features de chaque source
        typeform_features = self.extract_typeform_features(contact_ids)
        modjo_features = self.extract_modjo_features(contact_ids)
        multichannel_features = self.extract_multichannel_engagement(contact_ids)
        
        # Fusionner toutes les features
        result_df = base_df.copy()
        
        for features_df, source_name in [
            (typeform_features, 'Typeform'),
            (modjo_features, 'Modjo'),
            (multichannel_features, 'Multi-canal')
        ]:
            if len(features_df) > 0:
                # Convertir id_contact en même type
                features_df['id_contact'] = features_df['id_contact'].astype(str)
                result_df['id_contact'] = result_df['id_contact'].astype(str)
                
                result_df = result_df.merge(
                    features_df, 
                    on='id_contact', 
                    how='left'
                )
                logger.info(f"✅ {source_name}: {len(features_df)} contacts fusionnés")
            else:
                logger.warning(f"⚠️ {source_name}: Aucune donnée disponible")
        
        # Créer des features de synthèse
        result_df = self._create_synthesis_features(result_df)
        
        logger.info(f"🎯 Features avancées créées: {len(result_df.columns)} colonnes totales")
        
        return result_df
    
    def _create_synthesis_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Crée des features de synthèse combinant plusieurs sources."""
        logger.info("🔗 Création des features de synthèse...")
        
        # Score d'engagement global
        engagement_cols = [
            'nb_soumissions', 'nb_appels', 'nb_emails', 'nb_reunions'
        ]
        
        available_engagement_cols = [col for col in engagement_cols if col in df.columns]
        
        if available_engagement_cols:
            df['engagement_global_score'] = df[available_engagement_cols].fillna(0).sum(axis=1)
        
        # Score de qualité des interactions
        quality_cols = [
            'rating_moyen', 'score_modjo_composite', 'duree_moyenne_reponses'
        ]
        
        available_quality_cols = [col for col in quality_cols if col in df.columns]
        
        if len(available_quality_cols) >= 2:
            # Normaliser et combiner
            for col in available_quality_cols:
                if col in df.columns:
                    df[f'{col}_normalized'] = (df[col] - df[col].min()) / (df[col].max() - df[col].min() + 1e-8)
            
            normalized_cols = [f'{col}_normalized' for col in available_quality_cols]
            df['qualite_interactions_score'] = df[normalized_cols].fillna(0.5).mean(axis=1)
        
        # Maturité du prospect
        maturity_indicators = []
        
        if 'jours_depuis_premiere_soumission' in df.columns:
            maturity_indicators.append('jours_depuis_premiere_soumission')
        if 'jours_depuis_premier_appel' in df.columns:
            maturity_indicators.append('jours_depuis_premier_appel')
        if 'jours_depuis_premier_engagement' in df.columns:
            maturity_indicators.append('jours_depuis_premier_engagement')
        
        if maturity_indicators:
            df['maturite_prospect'] = df[maturity_indicators].fillna(0).max(axis=1)
        
        # Profil comportemental composite
        if 'profil_reponse' in df.columns and 'profil_engagement' in df.columns:
            df['profil_comportemental'] = (
                df['profil_reponse'].astype(str) + '_' + 
                df['profil_engagement'].astype(str)
            )
        
        logger.info("✅ Features de synthèse créées")
        
        return df
    
    def get_feature_importance_analysis(self) -> Dict:
        """Retourne l'analyse d'importance des features."""
        return {
            'metadata': self.feature_metadata,
            'synthesis_features': [
                'engagement_global_score',
                'qualite_interactions_score', 
                'maturite_prospect',
                'profil_comportemental'
            ],
            'high_impact_features': [
                'score_modjo_composite',
                'engagement_global_score',
                'rating_moyen',
                'nb_deals_associes',
                'maturite_prospect'
            ]
        }


if __name__ == "__main__":
    # Test rapide
    from google.cloud import bigquery
    
    client = bigquery.Client(project="datalake-sensei")
    engineer = AdvancedFeatureEngineer(client)
    
    # Test avec quelques contacts
    test_df = pd.DataFrame({
        'id_contact': ['1001', '1002', '1003'],
        'hubspotscore': [75, 45, 25]
    })
    
    result = engineer.create_advanced_features(test_df)
    print(f"Features créées: {len(result.columns)} colonnes")
    print(result.head())
