# 🧠 Sensei AI - Production ML Platform for B2B Sales Optimization

[![Python 3.11+](https://img.shields.io/badge/python-3.11+-blue.svg)](https://www.python.org/downloads/)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.100+-green.svg)](https://fastapi.tiangolo.com/)
[![Production Ready](https://img.shields.io/badge/Production-Ready-brightgreen.svg)](https://github.com/sensei-ai/sensei-ai)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

---

## 🎯 **Overview**

Sensei AI is a **production-ready machine learning platform** designed for B2B sales optimization. It combines advanced ML algorithms with a secure, scalable architecture to deliver real-time insights for conversion prediction, channel optimization, and NLP-powered conversation analysis.

### **🚀 Key Features**

- **🎯 Conversion Scoring**: ML-powered prospect scoring with advanced feature engineering
- **📞 Channel & Timing Optimization**: Multi-class prediction for optimal communication strategies  
- **🗣️ NLP Analysis**: Advanced transcript analysis from Modjo conversations
- **⚡ High Performance**: <100ms P95 latency, enterprise-grade scalability
- **🔒 Enterprise Security**: Read-only BigQuery access, GDPR compliance, comprehensive audit logging
- **🤖 Auto-Optimization**: Hyperparameter tuning with Optuna, automated model evaluation and replacement

---

## 🏗️ **Architecture**

### **Core Algorithms**

1. **Conversion Scoring Algorithm**
   - Predicts prospect conversion probability
   - Features from Typeform, HubSpot, and Modjo data
   - Advanced ensemble methods with hyperparameter optimization

2. **Channel & Timing Recommendation**
   - Optimizes communication channel selection
   - Predicts optimal contact timing
   - Multi-class classification with business constraints

3. **NLP Conversation Analysis**
   - Analyzes Modjo call transcriptions
   - Extracts conversation patterns and sentiment
   - Provides actionable insights for sales teams

### **Data Sources**

- **BigQuery Serving Layer**: `serving_layer.*` (read-only access)
  - Typeform responses and form analytics
  - HubSpot CRM data (contacts, deals, interactions)
  - Modjo call transcriptions and metadata

---

## 🚀 **Quick Start**

### **Prerequisites**
- Python 3.11+
- Docker 20.10+ (for deployment)
- Google Cloud access with BigQuery permissions
- 4GB RAM, 2 CPU cores minimum

### **Installation**

```bash
# Clone repository
git clone https://github.com/sensei-ai/sensei-ai.git
cd sensei-ai

# Install in development mode
pip install -e ".[dev]"

# Configure credentials
export GOOGLE_APPLICATION_CREDENTIALS=credentials/sensei-ai-service-account.json
export GCP_PROJECT_ID=datalake-sensei
export ENVIRONMENT=development
```

### **Training Models**

```bash
# Train all models
sensei train --all

# Train specific model
sensei train --model conversion --samples 10000

# Hyperparameter optimization
sensei optimize --model channel --trials 100
```

### **Running API Server**

```bash
# Development server
sensei serve --reload

# Production server
sensei serve --workers 4 --host 0.0.0.0 --port 8000
```

### **Running Tests**

```bash
# Full test suite
pytest

# Unit tests only
pytest tests/unit/

# Integration tests (requires BigQuery access)
pytest tests/integration/ -m bigquery
```

---

## 📁 **Project Structure**

```
sensei-ai/
├── 🐍 src/sensei/              # Core application code
│   ├── 🌐 api/                 # FastAPI server and endpoints
│   ├── 🤖 models/              # ML models (conversion, channel, nlp)
│   ├── 📊 data/                # BigQuery client and data access
│   ├── 🔧 features/            # Feature engineering pipeline
│   ├── 🛠️ utils/               # Utilities (logging, config, etc.)
│   └── 📋 cli.py               # Command-line interface
├── 🧪 tests/                   # Comprehensive test suite
│   ├── unit/                   # Unit tests
│   ├── integration/            # Integration tests
│   └── e2e/                    # End-to-end tests
├── 🐳 docker/                  # Docker configuration
├── 📚 docs/                    # Technical documentation
├── 🔐 credentials/             # GCP service account keys
├── ⚙️ config/                  # Configuration files
└── 📋 pyproject.toml           # Modern Python project configuration
```

---

## 🔧 **Configuration**

### **Environment Variables**

```bash
# Required
GOOGLE_APPLICATION_CREDENTIALS=credentials/sensei-ai-service-account.json
GCP_PROJECT_ID=datalake-sensei

# Optional
ENVIRONMENT=development|staging|production
LOG_LEVEL=INFO
API_HOST=0.0.0.0
API_PORT=8000
```

### **Model Configuration**

Models are configured via `config/models.yaml`:

```yaml
conversion:
  algorithm: "lightgbm"
  hyperparameters:
    num_leaves: 31
    learning_rate: 0.05
    feature_fraction: 0.9

channel:
  algorithm: "catboost"
  hyperparameters:
    iterations: 1000
    depth: 6
    learning_rate: 0.03

nlp:
  algorithm: "sentence_transformers"
  model_name: "all-MiniLM-L6-v2"
  clustering:
    algorithm: "hdbscan"
    min_cluster_size: 5
```

---

## 🧪 **Testing**

### **Test Categories**

- **Unit Tests**: Individual component testing
- **Integration Tests**: BigQuery and API integration
- **End-to-End Tests**: Complete pipeline validation
- **Performance Tests**: Latency and throughput validation

### **Running Tests**

```bash
# All tests
pytest

# Specific test categories
pytest -m unit
pytest -m integration
pytest -m "not slow"

# With coverage
pytest --cov=src/sensei --cov-report=html
```

---

## 🐳 **Deployment**

### **Docker**

```bash
# Build image
docker build -t sensei-ai:latest .

# Run container
docker run -p 8000:8000 \
  -e GOOGLE_APPLICATION_CREDENTIALS=/app/credentials/sensei-ai-service-account.json \
  -v $(pwd)/credentials:/app/credentials \
  sensei-ai:latest
```

### **Docker Compose**

```bash
# Start all services
docker-compose up -d

# Scale API servers
docker-compose up -d --scale api=3
```

---

## 📊 **Monitoring & Observability**

- **Metrics**: Prometheus metrics for model performance and API health
- **Logging**: Structured logging with correlation IDs
- **Tracing**: Request tracing for debugging and optimization
- **Alerts**: Automated alerts for model drift and performance degradation

---

## 🔒 **Security**

- **Read-Only Access**: BigQuery access limited to `serving_layer.*`
- **Data Privacy**: GDPR-compliant data handling and anonymization
- **Audit Logging**: Comprehensive audit trail for all operations
- **Secure Credentials**: Google Cloud service account with minimal permissions

---

## 📄 **License**

MIT License - see [LICENSE](LICENSE) for details.

---

## 🏆 **Production Status**

### **✅ Ready for Production**
- [x] **Enterprise Architecture** - Scalable, secure, and maintainable
- [x] **Advanced ML Pipeline** - State-of-the-art algorithms with auto-optimization
- [x] **Comprehensive Testing** - Unit, integration, and e2e test coverage
- [x] **Production Deployment** - Docker, monitoring, and observability
- [x] **Security & Compliance** - GDPR-compliant with enterprise security

**🚀 Sensei AI is production-ready for enterprise B2B sales optimization!**
