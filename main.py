#!/usr/bin/env python3
"""
Sensei AI - B2B Sales Optimization Platform

Main entry point for the application. Provides CLI commands for:
- Starting the API server
- Training ML models
- Running data pipelines
- System administration

Usage:
    python main.py serve                    # Start API server
    python main.py train conversion         # Train conversion model
    python main.py train channel-timing     # Train channel timing model
    python main.py train nlp                # Train NLP model
    python main.py pipeline extract         # Run data extraction
    python main.py admin create-user        # Create new user
    python main.py admin create-api-key     # Create API key
"""

import asyncio
import sys
import uvicorn
from pathlib import Path
from typing import Optional
import click

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.sensei.api import create_app
from src.sensei.models import get_model_registry
from src.sensei.features import get_feature_store
from src.sensei.utils import get_logger, get_config
from src.sensei.api.auth import create_api_key, auth_manager

logger = get_logger(__name__)


@click.group()
def cli():
    """Sensei AI - B2B Sales Optimization Platform"""
    pass


@cli.command()
@click.option('--host', default='0.0.0.0', help='Host to bind to')
@click.option('--port', default=8000, help='Port to bind to')
@click.option('--reload', is_flag=True, help='Enable auto-reload for development')
@click.option('--workers', default=1, help='Number of worker processes')
def serve(host: str, port: int, reload: bool, workers: int):
    """Start the API server."""
    config = get_config()
    
    logger.info(
        "Starting Sensei AI API server",
        host=host,
        port=port,
        environment=config.environment.value,
        reload=reload,
        workers=workers,
    )
    
    # Create FastAPI app
    app = create_app()
    
    # Configure uvicorn
    uvicorn_config = {
        "app": app,
        "host": host,
        "port": port,
        "reload": reload,
        "workers": workers if not reload else 1,  # Reload doesn't work with multiple workers
        "log_level": "info" if config.debug else "warning",
        "access_log": config.debug,
    }
    
    # Start server
    uvicorn.run(**uvicorn_config)


@cli.group()
def train():
    """Train ML models."""
    pass


@train.command()
@click.option('--max-samples', type=int, help='Maximum training samples')
@click.option('--output-dir', type=click.Path(), help='Output directory for model')
def conversion(max_samples: Optional[int], output_dir: Optional[str]):
    """Train conversion prediction model."""
    logger.info("Starting conversion model training", max_samples=max_samples)
    
    try:
        registry = get_model_registry()
        
        model = registry.train_model(
            model_name="conversion_predictor",
            model_type="conversion",
            max_samples=max_samples,
            output_dir=Path(output_dir) if output_dir else None,
        )
        
        logger.info(
            "Conversion model training completed",
            model_version=model.version,
            performance_metrics=model.metadata.performance_metrics,
        )
        
        click.echo(f"✅ Conversion model trained successfully!")
        click.echo(f"   Version: {model.version}")
        click.echo(f"   Accuracy: {model.metadata.performance_metrics.get('accuracy', 'N/A')}")
        
    except Exception as e:
        logger.error("Conversion model training failed", error=str(e), exc_info=True)
        click.echo(f"❌ Training failed: {e}")
        sys.exit(1)


@train.command()
@click.option('--max-samples', type=int, help='Maximum training samples')
@click.option('--output-dir', type=click.Path(), help='Output directory for model')
def channel_timing(max_samples: Optional[int], output_dir: Optional[str]):
    """Train channel and timing optimization model."""
    logger.info("Starting channel timing model training", max_samples=max_samples)
    
    try:
        registry = get_model_registry()
        
        model = registry.train_model(
            model_name="channel_timing_optimizer",
            model_type="channel_timing",
            max_samples=max_samples,
            output_dir=Path(output_dir) if output_dir else None,
        )
        
        logger.info(
            "Channel timing model training completed",
            model_version=model.version,
            performance_metrics=model.metadata.performance_metrics,
        )
        
        click.echo(f"✅ Channel timing model trained successfully!")
        click.echo(f"   Version: {model.version}")
        click.echo(f"   Accuracy: {model.metadata.performance_metrics.get('accuracy', 'N/A')}")
        
    except Exception as e:
        logger.error("Channel timing model training failed", error=str(e), exc_info=True)
        click.echo(f"❌ Training failed: {e}")
        sys.exit(1)


@train.command()
@click.option('--max-samples', type=int, help='Maximum training samples')
@click.option('--output-dir', type=click.Path(), help='Output directory for model')
def nlp(max_samples: Optional[int], output_dir: Optional[str]):
    """Train NLP conversation analysis model."""
    logger.info("Starting NLP model training", max_samples=max_samples)
    
    try:
        registry = get_model_registry()
        
        model = registry.train_model(
            model_name="conversation_analyzer",
            model_type="nlp",
            max_samples=max_samples,
            output_dir=Path(output_dir) if output_dir else None,
        )
        
        logger.info(
            "NLP model training completed",
            model_version=model.version,
            performance_metrics=model.metadata.performance_metrics,
        )
        
        click.echo(f"✅ NLP model trained successfully!")
        click.echo(f"   Version: {model.version}")
        
    except Exception as e:
        logger.error("NLP model training failed", error=str(e), exc_info=True)
        click.echo(f"❌ Training failed: {e}")
        sys.exit(1)


@cli.group()
def pipeline():
    """Data pipeline operations."""
    pass


@pipeline.command()
@click.option('--feature-type', type=click.Choice(['conversion', 'channel_timing', 'nlp']), 
              help='Type of features to extract')
@click.option('--limit', type=int, help='Limit number of records')
def extract(feature_type: Optional[str], limit: Optional[int]):
    """Extract features from data sources."""
    logger.info("Starting feature extraction", feature_type=feature_type, limit=limit)
    
    try:
        feature_store = get_feature_store()
        
        if feature_type == 'conversion' or feature_type is None:
            df, metadata = feature_store.get_conversion_features(limit=limit, use_cache=False)
            click.echo(f"✅ Extracted {len(df)} conversion features")
            click.echo(f"   Quality score: {metadata.data_quality_score}")
        
        if feature_type == 'channel_timing' or feature_type is None:
            df, metadata = feature_store.get_channel_timing_features(limit=limit, use_cache=False)
            click.echo(f"✅ Extracted {len(df)} channel timing features")
            click.echo(f"   Quality score: {metadata.data_quality_score}")
        
        if feature_type == 'nlp' or feature_type is None:
            df, metadata = feature_store.get_nlp_features(limit=limit, use_cache=False)
            click.echo(f"✅ Extracted {len(df)} NLP features")
            click.echo(f"   Quality score: {metadata.data_quality_score}")
        
    except Exception as e:
        logger.error("Feature extraction failed", error=str(e), exc_info=True)
        click.echo(f"❌ Extraction failed: {e}")
        sys.exit(1)


@cli.group()
def admin():
    """Administrative operations."""
    pass


@admin.command()
@click.option('--username', prompt=True, help='Username')
@click.option('--email', prompt=True, help='Email address')
@click.option('--roles', default='user', help='Comma-separated roles')
def create_user(username: str, email: str, roles: str):
    """Create a new user."""
    from src.sensei.api.auth import User
    from datetime import datetime
    
    role_list = [role.strip() for role in roles.split(',')]
    permissions = ['read'] if 'admin' not in role_list else ['*']
    
    user = User(
        user_id=username,
        username=username,
        email=email,
        roles=role_list,
        permissions=permissions,
        created_at=datetime.now(),
    )
    
    auth_manager.users[username] = user
    
    click.echo(f"✅ User '{username}' created successfully!")
    click.echo(f"   Email: {email}")
    click.echo(f"   Roles: {', '.join(role_list)}")


@admin.command()
@click.option('--user-id', prompt=True, help='User ID')
@click.option('--name', prompt=True, help='API key name')
@click.option('--permissions', default='read', help='Comma-separated permissions')
@click.option('--expires-days', type=int, help='Expiration in days')
def create_api_key(user_id: str, name: str, permissions: str, expires_days: Optional[int]):
    """Create a new API key."""
    permission_list = [perm.strip() for perm in permissions.split(',')]
    
    try:
        api_key = create_api_key(
            user_id=user_id,
            name=name,
            permissions=permission_list,
            expires_in_days=expires_days,
        )
        
        click.echo(f"✅ API key created successfully!")
        click.echo(f"   Key: {api_key}")
        click.echo(f"   User: {user_id}")
        click.echo(f"   Permissions: {', '.join(permission_list)}")
        click.echo(f"   ⚠️  Save this key securely - it won't be shown again!")
        
    except Exception as e:
        click.echo(f"❌ Failed to create API key: {e}")
        sys.exit(1)


@cli.command()
def health():
    """Check system health."""
    click.echo("🔍 Checking system health...")
    
    try:
        # Check BigQuery connection
        from src.sensei.data import get_bigquery_client
        bq_client = get_bigquery_client()
        validation_result = bq_client.validate_query("SELECT 1")
        
        if validation_result.get('valid', False):
            click.echo("✅ BigQuery connection: OK")
        else:
            click.echo("❌ BigQuery connection: FAILED")
        
        # Check model registry
        registry = get_model_registry()
        stats = registry.get_registry_stats()
        click.echo(f"✅ Model registry: {stats['total_models']} models, {stats['active_models']} active")
        
        # Check feature store
        feature_store = get_feature_store()
        click.echo("✅ Feature store: OK")
        
        click.echo("\n🎉 System health check completed!")
        
    except Exception as e:
        click.echo(f"❌ Health check failed: {e}")
        sys.exit(1)


@cli.command()
def version():
    """Show version information."""
    click.echo("Sensei AI - B2B Sales Optimization Platform")
    click.echo("Version: 1.0.0")
    click.echo("Built with: FastAPI, scikit-learn, BigQuery")


if __name__ == "__main__":
    cli()
