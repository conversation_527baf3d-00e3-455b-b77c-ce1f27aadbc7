# Sensei AI Configuration
# Copy this file to .env and update with your values

# Environment
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=INFO

# Google Cloud Platform
GCP_PROJECT_ID=datalake-sensei
GOOGLE_APPLICATION_CREDENTIALS=credentials/sensei-ai-service-account.json

# BigQuery
DATASET_ID=serving_layer
BQ_LOCATION=europe-west1
BQ_MAX_BYTES_BILLED=***********  # 20GB

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_WORKERS=1

# Model Configuration
MODEL_CACHE_SIZE_MB=1000
MODEL_TIMEOUT_SECONDS=30
MAX_MODEL_VERSIONS=3

# Training Configuration
SERVER_CAPACITY=medium
MAX_TRAINING_SAMPLES=10000
HYPEROPT_TRIALS=50
TRAINING_TIMEOUT_MINUTES=30

# Security
ALLOWED_READ_DATASETS=serving_layer
ALLOWED_WRITE_DATASETS=serving_layer_ml

# Monitoring
PROMETHEUS_PORT=9090
ENABLE_METRICS=true
ENABLE_TRACING=false
