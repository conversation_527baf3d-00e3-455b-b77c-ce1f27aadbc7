# Sensei AI - Development Makefile

.PHONY: help install install-dev test test-unit test-integration test-e2e lint format type-check security-check clean build docker-build docker-run serve train optimize evaluate status

# Default target
help: ## Show this help message
	@echo "Sensei AI - Development Commands"
	@echo "================================"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-20s\033[0m %s\n", $$1, $$2}'

# Installation
install: ## Install package in production mode
	pip install .

install-dev: ## Install package in development mode with all dependencies
	pip install -e ".[dev]"
	pre-commit install

# Testing
test: ## Run all tests
	pytest

test-unit: ## Run unit tests only
	pytest tests/unit/ -v

test-integration: ## Run integration tests only
	pytest tests/integration/ -v -m integration

test-e2e: ## Run end-to-end tests only
	pytest tests/e2e/ -v -m e2e

test-bigquery: ## Run BigQuery integration tests
	pytest tests/integration/ -v -m bigquery

test-coverage: ## Run tests with coverage report
	pytest --cov=src/sensei --cov-report=html --cov-report=term

# Code Quality
lint: ## Run linting checks
	ruff check src/ tests/
	black --check src/ tests/
	isort --check-only src/ tests/

format: ## Format code
	black src/ tests/
	isort src/ tests/
	ruff check --fix src/ tests/

type-check: ## Run type checking
	mypy src/sensei/

security-check: ## Run security checks
	bandit -r src/sensei/

quality-check: lint type-check security-check ## Run all quality checks

# Development
clean: ## Clean build artifacts and cache
	rm -rf build/
	rm -rf dist/
	rm -rf *.egg-info/
	rm -rf .pytest_cache/
	rm -rf .mypy_cache/
	rm -rf .ruff_cache/
	find . -type d -name __pycache__ -delete
	find . -type f -name "*.pyc" -delete

build: ## Build package
	python -m build

# Docker
docker-build: ## Build Docker image
	docker build -t sensei-ai:latest .

docker-run: ## Run Docker container
	docker run -p 8000:8000 \
		-e GOOGLE_APPLICATION_CREDENTIALS=/app/credentials/sensei-ai-service-account.json \
		-v $(PWD)/credentials:/app/credentials \
		sensei-ai:latest

docker-compose-up: ## Start services with docker-compose
	docker-compose up -d

docker-compose-down: ## Stop services with docker-compose
	docker-compose down

# Sensei AI Commands
serve: ## Start API server in development mode
	sensei serve --reload --log-level debug

serve-prod: ## Start API server in production mode
	sensei serve --workers 4 --host 0.0.0.0 --port 8000

train: ## Train all models
	sensei train --all

train-conversion: ## Train conversion model only
	sensei train --model conversion

train-channel: ## Train channel model only
	sensei train --model channel

train-nlp: ## Train NLP model only
	sensei train --model nlp

optimize: ## Optimize hyperparameters for all models
	sensei optimize --model conversion --trials 100
	sensei optimize --model channel --trials 100

evaluate: ## Evaluate all models
	sensei evaluate --all

status: ## Check system status
	sensei status

# Development workflow
dev-setup: install-dev ## Complete development setup
	@echo "Development environment setup complete!"
	@echo "Run 'make status' to check system health"

dev-test: format type-check test-unit ## Quick development test cycle

dev-full: format type-check security-check test ## Full development validation

# CI/CD
ci-test: ## CI test pipeline
	pytest --cov=src/sensei --cov-report=xml --cov-fail-under=80

ci-quality: ## CI quality pipeline
	black --check src/ tests/
	isort --check-only src/ tests/
	ruff check src/ tests/
	mypy src/sensei/
	bandit -r src/sensei/

ci-build: ## CI build pipeline
	python -m build
	docker build -t sensei-ai:latest .

# Documentation
docs-serve: ## Serve documentation locally
	mkdocs serve

docs-build: ## Build documentation
	mkdocs build

# Monitoring
logs: ## Show application logs
	tail -f logs/sensei.log

metrics: ## Show Prometheus metrics
	curl http://localhost:9090/metrics
