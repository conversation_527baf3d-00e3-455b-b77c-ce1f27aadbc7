../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/__init__.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/_compute_docstrings.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/_generated_version.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/acero.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/benchmark.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/cffi.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/compute.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/conftest.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/csv.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/cuda.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/dataset.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/feather.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/flight.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/fs.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/interchange/__init__.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/interchange/buffer.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/interchange/column.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/interchange/dataframe.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/interchange/from_dataframe.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/ipc.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/json.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/jvm.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/orc.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/pandas_compat.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/parquet/__init__.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/parquet/core.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/parquet/encryption.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/substrait.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/tests/__init__.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/tests/arrow_16597.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/tests/arrow_39313.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/tests/arrow_7980.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/tests/conftest.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/tests/interchange/__init__.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/tests/interchange/test_conversion.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/tests/interchange/test_interchange_spec.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/tests/pandas_examples.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/tests/pandas_threaded_import.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/tests/parquet/__init__.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/tests/parquet/common.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/tests/parquet/conftest.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/tests/parquet/encryption.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/tests/parquet/test_basic.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/tests/parquet/test_compliant_nested_type.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/tests/parquet/test_data_types.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/tests/parquet/test_dataset.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/tests/parquet/test_datetime.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/tests/parquet/test_encryption.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/tests/parquet/test_metadata.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/tests/parquet/test_pandas.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/tests/parquet/test_parquet_file.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/tests/parquet/test_parquet_writer.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/tests/read_record_batch.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/tests/strategies.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/tests/test_acero.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/tests/test_adhoc_memory_leak.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/tests/test_array.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/tests/test_builder.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/tests/test_cffi.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/tests/test_compute.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/tests/test_convert_builtin.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/tests/test_cpp_internals.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/tests/test_csv.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/tests/test_cuda.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/tests/test_cuda_numba_interop.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/tests/test_cython.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/tests/test_dataset.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/tests/test_dataset_encryption.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/tests/test_deprecations.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/tests/test_device.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/tests/test_dlpack.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/tests/test_exec_plan.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/tests/test_extension_type.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/tests/test_feather.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/tests/test_flight.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/tests/test_flight_async.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/tests/test_fs.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/tests/test_gandiva.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/tests/test_gdb.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/tests/test_io.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/tests/test_ipc.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/tests/test_json.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/tests/test_jvm.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/tests/test_memory.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/tests/test_misc.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/tests/test_orc.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/tests/test_pandas.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/tests/test_scalars.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/tests/test_schema.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/tests/test_sparse_tensor.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/tests/test_strategies.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/tests/test_substrait.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/tests/test_table.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/tests/test_tensor.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/tests/test_types.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/tests/test_udf.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/tests/test_util.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/tests/test_without_numpy.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/tests/util.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/tests/wsgi_examples.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/types.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/util.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/vendored/__init__.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/vendored/docscrape.cpython-39.pyc,,
../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Workspace/sensei-v1.0/sensei-AISuite-v1.0/venv/lib/python3.9/site-packages/pyarrow/vendored/version.cpython-39.pyc,,
pyarrow-21.0.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pyarrow-21.0.0.dist-info/METADATA,sha256=IYSjF1lYQHX9iRKo1M2az8XdV_mGG4oKyzTNLRRT5Wo,3303
pyarrow-21.0.0.dist-info/RECORD,,
pyarrow-21.0.0.dist-info/WHEEL,sha256=iU1P1G3Qdq5QfTJkFnAoqeKcOb1ztntp8QoS2svM1l0,107
pyarrow-21.0.0.dist-info/licenses/LICENSE.txt,sha256=gvX5sOZZLaf3kCL8kwrdEyp2xWcn0pgT-UBYFXorLRE,113409
pyarrow-21.0.0.dist-info/licenses/NOTICE.txt,sha256=yUZHDWsCTHf-69-2hr-SqChALA_8J8dpvKfYvvCOHbc,2916
pyarrow-21.0.0.dist-info/top_level.txt,sha256=Zuk_c1WeinXdMz20fXlEtGC67zfKOWuwU8adpEEU_nI,18
pyarrow/__init__.pxd,sha256=Wnar1phFqM_ZHnZmtbuqm6wJHsXlBoYKhV7Qmo2jUHA,2195
pyarrow/__init__.py,sha256=n1L-RP_QZT2T7qxHCibuEs98BUdgv8SRVJPbSpkQWqA,18456
pyarrow/_acero.cpython-39-darwin.so,sha256=Br4E9iFMQj19Ps5W_9RTiOiMuXaKcflMjai7NV5XHcM,334840
pyarrow/_acero.pxd,sha256=5ish_GgGWvit4ebhzoZil7b-m0r2RuG5JwYoxsH34FI,1440
pyarrow/_acero.pyx,sha256=hJBRFLgydYeNJWo_3gL1wn0vQgHuvexbi5HsEKNO8Dc,21539
pyarrow/_azurefs.cpython-39-darwin.so,sha256=4vsL-3p4MP0QhNSrlZju60ec_EHpifrEhmZs6_ewPZQ,156160
pyarrow/_azurefs.pyx,sha256=mlieD3Ze2LeiQFAaq4ykjLAW7jCUiEtK_brgX5dvGCo,9148
pyarrow/_compute.cpython-39-darwin.so,sha256=oe53of1GBA9I1eJAFJATROg0gdMDsm8XrnR4-gUzuOc,1203184
pyarrow/_compute.pxd,sha256=fhi87jkN6x4qG8JbAtRksx-4YTn3sFIw2zZxuK1flpM,2022
pyarrow/_compute.pyx,sha256=Sz1_-jJ_NPZLnVp06rncZluXfDVfS-ycz_1lBE7KxDo,112145
pyarrow/_compute_docstrings.py,sha256=7Vg8jt1aCsWrpTxsdqR7gY6M0faxXNX31c1RZdq9CFw,1707
pyarrow/_csv.cpython-39-darwin.so,sha256=s2N1VNqfqiaKBJBP4EsZvDRWgIPrd53ierxN9fNEbHg,364104
pyarrow/_csv.pxd,sha256=1Zk3Zpvvhy-Tb7c79Aqd4e7bBM21kc1JxWJkl02Y4DE,1638
pyarrow/_csv.pyx,sha256=Fo93LtIFCRTHRAjAPIFv6rUVee0m8qco9UXPYMf2Hzk,54678
pyarrow/_cuda.pxd,sha256=VzhM6j9dpNgrABlvFJKoMpRC0As55im-M3tgPTOuwEk,1922
pyarrow/_cuda.pyx,sha256=Lh9mMWoC2i1-9MOY6jSbYR-D43-NjVqzsItUuoAjZHA,35089
pyarrow/_dataset.cpython-39-darwin.so,sha256=SRbylhKWBZiSnWUGENxkX2j8OPTQhpF8HovjLHLI8hY,997392
pyarrow/_dataset.pxd,sha256=Ag9rUhoBySU6ba3wFLeuZyWMJnz9VkAf9TQEzWG4hUU,4944
pyarrow/_dataset.pyx,sha256=iUAjuWIgwgdaXblwfTu6h35QAcOFL_X4onONWqC1i-I,162765
pyarrow/_dataset_orc.cpython-39-darwin.so,sha256=ZrHdc27boOWfIcubDtXbe9CNxtZ-jXSOmXPgaGSIFwc,110032
pyarrow/_dataset_orc.pyx,sha256=JSFoRI0pfHtL2jeIuPg5TJHodcfuCNYmj_iEZ4xY87w,1499
pyarrow/_dataset_parquet.cpython-39-darwin.so,sha256=-O0QvDrgaCaUQxuHYs46Gt8Azot5LkI47z7R3iArXoU,345176
pyarrow/_dataset_parquet.pxd,sha256=y-3iKehyB_eB_oeqjtt4aQRbUpVGVN1oUMFGIY13brE,1572
pyarrow/_dataset_parquet.pyx,sha256=6SBcX1-kfZz1F_p8iDcmG_a_5bjT6iEmsgDxkrhyCt8,41503
pyarrow/_dataset_parquet_encryption.cpython-39-darwin.so,sha256=qvhUlCdD2hFN12F1_XBuve9kBIWjMtQqD4VjNh9y-H4,152864
pyarrow/_dataset_parquet_encryption.pyx,sha256=p7LDNUsp3jMVcWDcbOFp8a3CYJjASVPI_tfATpY-ePg,7229
pyarrow/_dlpack.pxi,sha256=clw0FkGoyZMEtUU8zPpO_DMtl2X-27kb2UtyhQuIc1s,1832
pyarrow/_feather.cpython-39-darwin.so,sha256=YlZXxK2QoU8Ie3NGzS9rlZomXub7c0SOlkLvJ8lgvgs,133168
pyarrow/_feather.pyx,sha256=DWQI4U0uAWE1ZYUwPreBPJg1TGLEGmF3wPEIRL-PhPw,3773
pyarrow/_flight.cpython-39-darwin.so,sha256=WM5COJliEOEir2phBU6ew9C6NYnW5eO3DDcVent426Y,1190608
pyarrow/_flight.pyx,sha256=fLwzuEeITs7ZFBcwsOn3K49liPnk1muU3fJc3CSZWek,113567
pyarrow/_fs.cpython-39-darwin.so,sha256=a6nWfUJ74wk8ftOXEr6Kf10psYRSRGGVX4GTS1IXIYY,443464
pyarrow/_fs.pxd,sha256=SmHS31eyYU7VUZlVuP613HKgpd7bENnQGApvX_g2Lfw,2439
pyarrow/_fs.pyx,sha256=pOTlyjrqmthXYVE5wv9VM4L6-huvaeIKH-NXVW1Qdrg,53253
pyarrow/_gcsfs.cpython-39-darwin.so,sha256=gRIxnoZy2hbEWvY1ZbyAIivlm_P3DRMAlrjYml_e4Ps,155032
pyarrow/_gcsfs.pyx,sha256=sooI3ztzEQXVVKcahgin0UGm_6Sgy-uwfcMrACP690w,9046
pyarrow/_generated_version.py,sha256=Xb_1d6H6zUb71cBCHXqvfVdYtB18qakRlahhdUYCw2c,513
pyarrow/_hdfs.cpython-39-darwin.so,sha256=BCEDCeSasHvVLGC9MWYCaMjHm4b-tf72xFzl5gmslhY,156712
pyarrow/_hdfs.pyx,sha256=QVkMqzz2iGoipaNufk0GPZRzjy1JKJzuuqqk88MdYA4,5802
pyarrow/_json.cpython-39-darwin.so,sha256=UhNajz3ulQUycTvdex7CEsAu9XGbILYTUUhbpNXjbzE,151048
pyarrow/_json.pxd,sha256=tECTP14M12-b_ja5QI3snQbd0uWPWmmC9FwkWq23Vg0,1206
pyarrow/_json.pyx,sha256=VCdkaxuO0Q0SzAp7zxqoG2e5GImXp1ZERYMM6jG_5EI,12532
pyarrow/_orc.cpython-39-darwin.so,sha256=bVZO91DqDR22ueig2OA_Ot2Km2gKffBX33NPTKi_JgI,220728
pyarrow/_orc.pxd,sha256=6hL0cq1RufqQD-B_bV3ne1rhu2g-h4rDOFNQsSb6qps,5689
pyarrow/_orc.pyx,sha256=Pn7r4dzagWaqMf8rymbXBIWisxonBaStZgXCi7pfrZI,15556
pyarrow/_parquet.cpython-39-darwin.so,sha256=Du1brEChxR-I-CsGzZV4N7LMCR_IoWkWgPq9xHeym4Y,574224
pyarrow/_parquet.pxd,sha256=KxXP0bGyyy9SKJBOaRhpll5XtoObPhxEWHGayPFDA0c,4888
pyarrow/_parquet.pyx,sha256=cMlWKW4H3wOOrFR39grhgI_ynz5g9TIWhbPdQdXedRc,79605
pyarrow/_parquet_encryption.cpython-39-darwin.so,sha256=kFa-bg06hvH1nAoURSWM_xbDcYYEA3PGzycK4D5-tcw,275160
pyarrow/_parquet_encryption.pxd,sha256=1vQnkyS1rLrSNMlmuW62PxkOmCsYpzC60L9mqD0_LYc,2586
pyarrow/_parquet_encryption.pyx,sha256=c1Ep8WY_00N58b4Ylg79ib8xmRagtYRGkiFa1nRrQ0c,19078
pyarrow/_pyarrow_cpp_tests.cpython-39-darwin.so,sha256=oWnqJIdKsC7e7U_en9R65-nYGr0e6oY3-sUZduYlxys,127256
pyarrow/_pyarrow_cpp_tests.pxd,sha256=nPyRmNtFbOUvSXCwegAApQFfh8UI_K9Hq5dN4oPAxdo,1199
pyarrow/_pyarrow_cpp_tests.pyx,sha256=gLeMzB9RWodZgXEpipX65_0aqWu12SjMld0JZmZVRP0,1753
pyarrow/_s3fs.cpython-39-darwin.so,sha256=9aEjvVQnUv-pNPix8sklD0HOhuzAqUTDFOHhb0GcnmM,204120
pyarrow/_s3fs.pyx,sha256=DINjYPLyBrMeqdbd9Z5fq0D7iYdKCr4E047EFZLJtyA,20595
pyarrow/_substrait.cpython-39-darwin.so,sha256=-lTVtLzHEP0MV_nLmcrSfU3sDjvUGB63xNApwogGzOw,230464
pyarrow/_substrait.pyx,sha256=fZT496mzp1ssSKIzqtmPeGBKeYq5f0vm8cWlWqaC408,15666
pyarrow/acero.py,sha256=yus6kVAtfYVoSenfPZPHgmeEmnaySb5UCM1lVMkzF1M,15743
pyarrow/array.pxi,sha256=oHnaVVhQXlEuh61EJr6KFlrUIo-CDhzp48o4Gr7USJo,158784
pyarrow/benchmark.pxi,sha256=DYXdu-jMSH7XcTohbc8x8NiKRLtpX9IULfY20ohkffA,869
pyarrow/benchmark.py,sha256=k9Z3yQyoojpYz4lTA6DkCfqT6fPG3N2fJtsHKjpbYFo,856
pyarrow/builder.pxi,sha256=9QE4KAiA4JpA7-2JLgX3xo32jRtuWZ3YqC-T9GzUVDc,4634
pyarrow/cffi.py,sha256=hEcrPH9KeG6NES3ZCpSbOVYhOgDOuBB_2LgMMucgw-8,2396
pyarrow/compat.pxi,sha256=Sq5c3CKq0uj5aDyOoHHkPEO_VsSpZ90JRaL2rAKHk5I,1920
pyarrow/compute.py,sha256=5oNguGEC7lG7E6n0xAMBYeYxC8D9SKgME4Yu6ESp4MY,24328
pyarrow/config.pxi,sha256=E6QOFjdlw3H1a5BOAevYNJJEmmm6FblfaaeyspnWBWw,3092
pyarrow/conftest.py,sha256=54Oim0WylDduwtVx7l7fTKSQsJyPUbP_duPSYicHXs8,9891
pyarrow/csv.py,sha256=S6tm31Bra9HPf9IsYwBLltZBLMvNzypWfeCLySsjmds,974
pyarrow/cuda.py,sha256=j--8HcBAm5Ib-kbhK4d2M6SVQmDWkr7Mt5fnwU2LzdQ,1087
pyarrow/dataset.py,sha256=QD7W7qtf3hKN4YTII7h4sEAWk0kDKkPhzSigB7fE4GE,40506
pyarrow/device.pxi,sha256=kx9WnmI2bAKgt9txyGVQWackkRj9NPZLsE5q5H1RFgU,5563
pyarrow/error.pxi,sha256=Wj7-NGUfdvlEwAwd8Ta_JqRC8IUOUpm_PmpvizCFvfY,8909
pyarrow/feather.py,sha256=fOJRqGWBWrVchVSwruTcbNqN_KHDpueQ4xdoaTaEVm8,9943
pyarrow/flight.py,sha256=HLB04A0SZ35MZJumPIuBu5I2dpetjEc-CGMEdjQeQRQ,2177
pyarrow/fs.py,sha256=I3BSELuMKGDJ_jxBn3KtzPtnOVby-PLp9KesyXHQC_w,14965
pyarrow/gandiva.pyx,sha256=sNXorsDoKVbr7iI7fwXmQZxorZA77WyCRiJtSdF_Ev8,24280
pyarrow/include/arrow/acero/accumulation_queue.h,sha256=fz0okH6sZqcSpBl2w3vOU9wSa9QlOIAXzikmb3mINPE,5985
pyarrow/include/arrow/acero/aggregate_node.h,sha256=9fhmBQGUphsqLd7bNmiOJaEg5NJxVyWA5RGs9UaTxR0,2201
pyarrow/include/arrow/acero/api.h,sha256=fRuKEHbKDYWRCwSHLc7vSD-6mQavyOsztluCR7evFCk,1151
pyarrow/include/arrow/acero/asof_join_node.h,sha256=Ko6r1wDjxg01FE9-xKkttx7WzCAzf43GxbpvGHgKZp8,1490
pyarrow/include/arrow/acero/backpressure_handler.h,sha256=CsSWRenrtbZYiNnf-cdYCgMLmu5KUAPUKNKMDWttoD4,2810
pyarrow/include/arrow/acero/benchmark_util.h,sha256=T5bNabF1TDAp28S7V_vt_VIDn6l5Be0zOVCHhcTcFf8,1943
pyarrow/include/arrow/acero/bloom_filter.h,sha256=bFzzAzQrs9ePp2tCPQIuk1Oa9gG_Nyp72M_HM0dhakM,11978
pyarrow/include/arrow/acero/exec_plan.h,sha256=U0KA3tnNvVb75G0XQFLVbGzXCGdddGyRhW3zMa8oWJc,35909
pyarrow/include/arrow/acero/hash_join.h,sha256=zjljUjH2AQlI54Vz2_7hvJIAjTgaT0BPC_LVD7xNd2k,3022
pyarrow/include/arrow/acero/hash_join_dict.h,sha256=_BKJmK3Z_KdJuYHh4KQCuT_1rXlUohrtEgGLtEJ4fgQ,15360
pyarrow/include/arrow/acero/hash_join_node.h,sha256=FXT-aeXL7nNTuV75f9oXgdGyqMK_72GnqGUm9cmBnko,4378
pyarrow/include/arrow/acero/map_node.h,sha256=Bd1HcW0N5azoIVth2ATeHxgTKd9XmmEkz42YBNw5eK0,2628
pyarrow/include/arrow/acero/options.h,sha256=6OVAi5i3h3pD64Y2KUO6k4vEnkxDvOShoDNEWNQo4e8,37394
pyarrow/include/arrow/acero/order_by_impl.h,sha256=dQqplP-AZWPZRKio8LmTjYWlCYz9VmW-usUrtaLpd_w,1691
pyarrow/include/arrow/acero/partition_util.h,sha256=xS8hayuTRimyrOHzf1xRm1IVjkyIeVCGXY78B2rJaJc,7437
pyarrow/include/arrow/acero/query_context.h,sha256=D364aGRS3uWe8lgYqCNRjVvs5sKetLOOXzACdp5GZeg,6212
pyarrow/include/arrow/acero/schema_util.h,sha256=KA_hV2xy2TRccMyksSzQrdH9_rdGo3tQyHOIvrWWYBQ,7961
pyarrow/include/arrow/acero/task_util.h,sha256=6pqILuYfcVwt9HqVhRfXFVJoOC-Q_dtk8mQ5SxjgwbY,3706
pyarrow/include/arrow/acero/test_nodes.h,sha256=xKeLWZZC8iokveVXPjseO1MOvWMcby-0xiMISy0qw8E,2877
pyarrow/include/arrow/acero/time_series_util.h,sha256=W9yzoaTGkB2jtYm8w2CYknSw1EjMbsdTfmEuuL2zMtk,1210
pyarrow/include/arrow/acero/tpch_node.h,sha256=l3zocxHTfGmXTjywJxwoXCIk9tjzURgWdYKSgSk8DAQ,2671
pyarrow/include/arrow/acero/type_fwd.h,sha256=4zLhtLJf_7MSXgrhQIZVGeLxjT7JrEDAn9yW75DTFlc,1103
pyarrow/include/arrow/acero/util.h,sha256=byhMEj5XoAUy-93AjLrx_p9_iUZdYn5uJ_cDkCJQt5Q,6121
pyarrow/include/arrow/acero/visibility.h,sha256=E-4G2O4F2YabXnFNJYnsI2VbVoKBtO7AXqh_SPuJi6k,1616
pyarrow/include/arrow/adapters/orc/adapter.h,sha256=G5SSGGYMSREILC43kqL5fqo94c4tKgukitO15m217tY,11031
pyarrow/include/arrow/adapters/orc/options.h,sha256=FMxda5YSskRrB6h9FvcAuMxl5qdavWrNYHPlanjtk48,3696
pyarrow/include/arrow/adapters/tensorflow/convert.h,sha256=ZGFAodnwTJK0ZoXfgYJdjgi_F4vfEhI9E87zejxVb6E,3465
pyarrow/include/arrow/api.h,sha256=Gs6HiRBYU5N7-a79hjTl9WMSda551XdUKpWthFY2v1s,2491
pyarrow/include/arrow/array.h,sha256=P5oW6hvD2j97bLaSTE4_UHuV6Y38DTwJVww3Eb3xdTQ,1981
pyarrow/include/arrow/array/array_base.h,sha256=kq08hubpDlrIVgDRhL4MEqPyaEftgmoznQ91ZVrLnAQ,12371
pyarrow/include/arrow/array/array_binary.h,sha256=JvtB8DoR0_tqfSFS_9nMRrJ39lt1cTm5yXh-DLkhqjU,11247
pyarrow/include/arrow/array/array_decimal.h,sha256=xRfrZ1IFO09EmkHEolCwrJ4lsXjLo5DXdfH5_v2gSyw,3105
pyarrow/include/arrow/array/array_dict.h,sha256=6AMbSnZoMj-nhQhZhG4RNnxy9VVPk2DvZjVblwIUhgY,7611
pyarrow/include/arrow/array/array_nested.h,sha256=xySiF5b1ab97GifKMx6FuYZWb2_6e3YvSMfOORGe3J4,37605
pyarrow/include/arrow/array/array_primitive.h,sha256=_U8_pTHg-aHY5jaSFjHZ4hqRPXdYiyKTpr2y3OcJcdg,8184
pyarrow/include/arrow/array/array_run_end.h,sha256=4zs3tcUrIgDOhSEOywJ1vGY2lsH-5QuEBn87mxnDbi8,5101
pyarrow/include/arrow/array/builder_adaptive.h,sha256=92DpiIZDXSI_yOrMftj7P60zlCLjNmwfGM5ubdbXWM4,6861
pyarrow/include/arrow/array/builder_base.h,sha256=CP9kS8pDFd4XyJQdgIlBp3pTIX9mND1Lvh85re4IC8w,13723
pyarrow/include/arrow/array/builder_binary.h,sha256=cfrm-LnXOZ73pQpn_kXSXw0Bh252DpsfP4KGHlPNy3g,33744
pyarrow/include/arrow/array/builder_decimal.h,sha256=DFxyFlpzWRZS9zdBhsjII5fFUOMY9bXHn3EIrIvmOMo,5051
pyarrow/include/arrow/array/builder_dict.h,sha256=FZjvCRIDmVuwmzx_HCcDK6ZjNoZKCEsSV-fGI0K974Y,27899
pyarrow/include/arrow/array/builder_nested.h,sha256=1In_M8pjkaqTuvNZlbGXWGID33CW2eBhy4in0oZsavA,31231
pyarrow/include/arrow/array/builder_primitive.h,sha256=g3FX4lVKY_sZBu1xuJOQidZOxSWq1mvGbcgPnr53dTc,22009
pyarrow/include/arrow/array/builder_run_end.h,sha256=SZIdsUKK1qAc9pdonPGf0A_aikZHcxxzicezRGR5hLs,11416
pyarrow/include/arrow/array/builder_time.h,sha256=8M2ifZnDgujSItXKsevyBdtM6Iky3ImyeIdAqZV3fec,2548
pyarrow/include/arrow/array/builder_union.h,sha256=8BF532sAMc7JxWIbSN-yX6Z9fqY9jmmsIa054DPvbWE,10144
pyarrow/include/arrow/array/concatenate.h,sha256=wBy-CBTz9MeRCmcnfXGvkXnvSRApvPOcfCf64A42ys8,2059
pyarrow/include/arrow/array/data.h,sha256=gZiu0DNOH1W3mHRfbC7lko13Jcre35mKBqM_V3Rxjmo,28943
pyarrow/include/arrow/array/diff.h,sha256=bYNKy2oLAxtt6VYDWvCfq2bnJTVNjG5KMTsGl-gT_kM,3344
pyarrow/include/arrow/array/statistics.h,sha256=5oGsx7kK0V9Nodo86J4Fzob8A_-EYChnbWrr9ZjSPn0,5633
pyarrow/include/arrow/array/util.h,sha256=qVHvCaVlALz8WJwAjyMwsBm5J2iN89CSgj7NpmmqlkI,3652
pyarrow/include/arrow/array/validate.h,sha256=JdDb3XJg4TmAfpv_zgu2ITfL2H9no10TQit-HPj9Myw,1710
pyarrow/include/arrow/buffer.h,sha256=aDZgA8fFnizg61OPvgpWxftsGRAGNd-EW2BMjE233ZA,23226
pyarrow/include/arrow/buffer_builder.h,sha256=IKAiLxeeKgZ_O3hjyJhhEUOWpcpCcEY18jIk6pad1Jg,17475
pyarrow/include/arrow/builder.h,sha256=mBxMko271lJ7Xbku0hCixj943Yx-d2i4Q5Hm2WfwiGM,1546
pyarrow/include/arrow/c/abi.h,sha256=GCjxzmA2jNWQ0ZWVkQ-hnbCbMHKPikO1-JkzYj96cnY,20318
pyarrow/include/arrow/c/bridge.h,sha256=6e6ZoIsG-pE8FQ5aU4nRJ71vVL3U9k697g7PYKDsVz4,21789
pyarrow/include/arrow/c/dlpack.h,sha256=IXIuMdfqbHJ_AdT_25tzp0z48mWC65w4xZMjTP3yl7Q,1983
pyarrow/include/arrow/c/dlpack_abi.h,sha256=mjp9WWq8qv6gkGirT4y0o3BL_ZI9VyHQpJ5aEpPFetI,9920
pyarrow/include/arrow/c/helpers.h,sha256=f0Q519PwoliFHpxsHp-QvbP6fpVMN2Ha35Tk-RBK6Ws,6279
pyarrow/include/arrow/chunk_resolver.h,sha256=oVm7DpbWRmjJhexj_h6BtispCBxFZPIHK1kl9goBGG8,12841
pyarrow/include/arrow/chunked_array.h,sha256=z6LA9OB3uhtmn7ZZe5wfi3Am3icVQ-L_e8s3KEMuq18,10647
pyarrow/include/arrow/compare.h,sha256=ofJ_QNT67SZWf1zmEKpMkbQiTp13-DYOzcJ1zrSAf4M,6894
pyarrow/include/arrow/compute/api.h,sha256=6z6HdNH6S0Amop2GUfrsifxOvgupfTqFDZf76YREt-4,2139
pyarrow/include/arrow/compute/api_aggregate.h,sha256=Dd659O3TUttUqwV0LHQivn4Wt04y5zIn-1M8JZCC0qQ,21945
pyarrow/include/arrow/compute/api_scalar.h,sha256=tMwpFYepJemj0m0aEoM6KWk5tbzzjGVdsASxAe5EpAo,70099
pyarrow/include/arrow/compute/api_vector.h,sha256=xn5R28HHMMT_vT5LaDmKY8ueUVD1MRKP0PB7HSUy8Xc,34508
pyarrow/include/arrow/compute/cast.h,sha256=Xw9j03AIAMU_hZiqk9d2ZD4xTmESkfXaDsuZkiTypLs,4245
pyarrow/include/arrow/compute/exec.h,sha256=2baXvq1FWwyfQfTV4r6qhF818LEYOxyn_DiTqE5AMCk,17975
pyarrow/include/arrow/compute/expression.h,sha256=LBwjf1LTtfCfTT3RGUq_3PI_V0LRf2HxtZ-Ovqslw-c,11164
pyarrow/include/arrow/compute/function.h,sha256=krTXaLowvT1cKhecs70urPQcx74vQCJ4jswtBE4Xs5A,16345
pyarrow/include/arrow/compute/function_options.h,sha256=Q9rjkXPrU9-Xi64_fMLPbBbW_byhjJFsvHppP1CumdA,3088
pyarrow/include/arrow/compute/initialize.h,sha256=ms8mtCSHTaStTrWdapbNyNSpM0PO4ox32O9colbBR7k,1193
pyarrow/include/arrow/compute/kernel.h,sha256=ywsxF87w2eI4li8be7Wiua5bXp0NYhMb7LS8IzPFO3U,31406
pyarrow/include/arrow/compute/ordering.h,sha256=8Vw3VzDi1mGgVwKGQZakz9TVj0A40wxcL13EvuqNVjU,4129
pyarrow/include/arrow/compute/registry.h,sha256=x7LHiaNEVvZ0VUssZFsasB52Z1AxRflkdI5tR1hhzqc,4837
pyarrow/include/arrow/compute/row/grouper.h,sha256=ID9sFjFawulQC5T_f-TQttvohhsSAmNkHIj1iXzDY-0,7520
pyarrow/include/arrow/compute/type_fwd.h,sha256=tiG7MrLEfXAPF53JKlnFdJ7kzjhNP9a_xCb2Ept3ozc,1555
pyarrow/include/arrow/compute/util.h,sha256=l1y6P1hHsI-mQ7lB6KNn_8AzYCxCxuhaSBOOG_wqjcs,9376
pyarrow/include/arrow/compute/visibility.h,sha256=_h6gB6GG5YSF3aak6oAerylqtJvjhTN9Tq8MZccpO0M,1597
pyarrow/include/arrow/config.h,sha256=8liyKI0CJO0G-Fz5I--QjIAwh0m4hosfyAOwvVVs0sU,3044
pyarrow/include/arrow/csv/api.h,sha256=LbwWhPyIsi_73hvsSr77RNR9uUxrVyXM__hp7QcSom0,907
pyarrow/include/arrow/csv/chunker.h,sha256=nTs8hdy4D3Nz3oZWm2JMuA02noY_0pWRYWq_RptqzHY,1171
pyarrow/include/arrow/csv/column_builder.h,sha256=7oa9YCg2Uc2mB7ExHIyYIvbdt555qLXiU0y4FepkISU,2890
pyarrow/include/arrow/csv/column_decoder.h,sha256=10idcPJE2V_TbvgjzPqmFy1dd_qSGWvu9eDkenTuCz0,2358
pyarrow/include/arrow/csv/converter.h,sha256=cjtnz_hZFxm_dWjAMjr1iqqk1egXI2Yb8Bd0xC8md5E,2789
pyarrow/include/arrow/csv/invalid_row.h,sha256=gTHjEbjkpee6syLGA8hFY7spx1ROMJmtMcwhXv21x5Q,1889
pyarrow/include/arrow/csv/options.h,sha256=_HkjSoiAPW77z5AHVVnTa452y1KfJgnXWXz2NoPPAYw,7980
pyarrow/include/arrow/csv/parser.h,sha256=8PplRh3Qxckk8VPyM70P_f1MBb4WMGnNVpoeJ9kOdHU,8616
pyarrow/include/arrow/csv/reader.h,sha256=416pt3yNQsgn4RhIyRMsmSJmvv1sw3ouQotubXG91gQ,4606
pyarrow/include/arrow/csv/test_common.h,sha256=uEYzw8EROvd1QMBQ98d4MaZ7BqMlw2e0flAyz-du0Z4,1972
pyarrow/include/arrow/csv/type_fwd.h,sha256=ptVbengmY_a7Yz1w0SKmKL16yyw9yEeym0Q0cnRCSV4,984
pyarrow/include/arrow/csv/writer.h,sha256=Y1zErZ5H1r2QzjAta3TXpFrdl2btoardCF8USCAGtGg,3549
pyarrow/include/arrow/dataset/api.h,sha256=p7i-bncJLhmfBkfjJWS7684vD9Lke1m6tb7HQq7Tpn4,1322
pyarrow/include/arrow/dataset/dataset.h,sha256=NsXdSFYrO4BpJSEV2e4yJRrN-hdLxMJepwAyyqFWt-0,20327
pyarrow/include/arrow/dataset/dataset_writer.h,sha256=TQV75b_UigfGjIpBnPk8teOncM5WroKfKV15oicBRRY,4589
pyarrow/include/arrow/dataset/discovery.h,sha256=x7-5NBAyEeQWGlWanJDLZAoWksKiMwM96tlDx_M6n5c,11236
pyarrow/include/arrow/dataset/file_base.h,sha256=nUMWLbhh7lt5c3iOw5edAtcy4jrCzpBUX5JZ29FW0Tk,20385
pyarrow/include/arrow/dataset/file_csv.h,sha256=7PlvQW_2FJ5RRN-VH4-OBw5cZ6nkd0KE0sj1TQvCZeo,5016
pyarrow/include/arrow/dataset/file_ipc.h,sha256=6-btvXhflZsAH90T3wMkwzZkte6T4ixzeCEUn_5uYW8,4083
pyarrow/include/arrow/dataset/file_json.h,sha256=sPjOeMOtbZZbvOivnOdb4MvYKHltpTnY8fONkhB9PZs,3523
pyarrow/include/arrow/dataset/file_orc.h,sha256=P7nAD9nacVngDEjH8ChQRt0AQmDg4Z1wBx360LDOoSg,2452
pyarrow/include/arrow/dataset/file_parquet.h,sha256=_mhpCrrLGn9XPKSt9lsdbG4wosPOkMiqkjXDz-2D_So,16935
pyarrow/include/arrow/dataset/parquet_encryption_config.h,sha256=Upo0k5MijZaMaRZjPp5Xg8TRt1p8Zwh2c2tdimjVe1A,3425
pyarrow/include/arrow/dataset/partition.h,sha256=3wrNekD_-fPO1YW91Za-T4muCfQeAX7SZRIcsCN_czI,16815
pyarrow/include/arrow/dataset/plan.h,sha256=IjuR9K2sWD85_2HpVVoJ-3YUCq--UPblHU46exX5qRg,1181
pyarrow/include/arrow/dataset/projector.h,sha256=KfZijq09Ht0Z2cJHsrjg-sE3SiZ4TKainflReK-39cg,1135
pyarrow/include/arrow/dataset/scanner.h,sha256=qoqJSR2er707AlKLij_PS6GDKph82YdHi0iu5UmEA1Q,25917
pyarrow/include/arrow/dataset/type_fwd.h,sha256=YOUSRwdNAlXJ7meFLolpAFQ_mSlObs2F81zcOy0DoI4,3170
pyarrow/include/arrow/dataset/visibility.h,sha256=ckmf_sEI0WBo4W7DIgH1QrOq82skOHtoksl9B3yYvzU,1586
pyarrow/include/arrow/datum.h,sha256=XYaZ_URrAtVqHMq-_2YtXk_ETeQ4yZWLVAnsi-k2Mac,11511
pyarrow/include/arrow/device.h,sha256=mLz99tb74VdjxXtKt6RZCYKJQ8TYz93uaCFJ1ZiItMw,15344
pyarrow/include/arrow/device_allocation_type_set.h,sha256=ynoZ-XyFlOAjh01PU-R11mE_EOxuw3xzc94v5OXa0u4,3306
pyarrow/include/arrow/engine/api.h,sha256=ORM0M5KQeurjEG8Eoa5IeV_ZgKBRPlWyicyv3ORWkAY,886
pyarrow/include/arrow/engine/substrait/api.h,sha256=W9NB1RAm0ZVxztRXYA-GD7H8XLQNXFoYT7TdGFHoNTE,1079
pyarrow/include/arrow/engine/substrait/extension_set.h,sha256=FE6cceycuQv7CCe_Fl4t6tIMRyfoJfWClUhSvHgcm90,21552
pyarrow/include/arrow/engine/substrait/extension_types.h,sha256=x5ZIuynNh6WFt3wRjW--zUsuC3SeDLk1qRg9_xhswWM,3075
pyarrow/include/arrow/engine/substrait/options.h,sha256=dtvUty_zoDmcFwVflppiDzelYkeOhCO74uRF6izQSzk,5820
pyarrow/include/arrow/engine/substrait/relation.h,sha256=V3VKFlDdE61e1OS8LbJiwvm5w0uq5bzBLhKqmgmKaws,2385
pyarrow/include/arrow/engine/substrait/serde.h,sha256=mjxfuFo4aPhCiwefpKAJMIlknF4UOHSr6gWU__1SwCc,16528
pyarrow/include/arrow/engine/substrait/test_plan_builder.h,sha256=REFa79D1AOIIjp2Iez73iw5gEnzG9Rac9t8WwiGLsuI,3003
pyarrow/include/arrow/engine/substrait/test_util.h,sha256=IHZeYrk50Sx9anJfC25DWP6XesItKEywDWUqvUJcjEQ,1517
pyarrow/include/arrow/engine/substrait/type_fwd.h,sha256=P9YRjAQpSgoIjDC0siYyxoQzcPVo3r9y85qjiMtudBs,1028
pyarrow/include/arrow/engine/substrait/util.h,sha256=_dRiQBaIMWNbsYG7kuXhs3dMk4dI63-pM0uSxYPOvgE,3570
pyarrow/include/arrow/engine/substrait/visibility.h,sha256=GRzH6U-UCPT8d60cywOkFfcanPSgiZKCDP6X2rIpbMs,1740
pyarrow/include/arrow/extension/bool8.h,sha256=VsHTtVyrqk6UKgvifad7LouuieoAZuZs_uVvegdGq4Q,2145
pyarrow/include/arrow/extension/fixed_shape_tensor.h,sha256=VOqvTSnwDIvnhbstYX5nnqWfhtZ7MaD-lSF89BEqlhE,5610
pyarrow/include/arrow/extension/json.h,sha256=bNh6R9aP3MK-rbTON8IzVn7FIt9RGMmUHGpQkFGfOMw,2026
pyarrow/include/arrow/extension/opaque.h,sha256=uMVqSScey_13Ho6V86vfkuoByZni9ufh5BGKgX4bTZk,2920
pyarrow/include/arrow/extension/uuid.h,sha256=DBzTD83Mh3oacfZzfU6fQuGC87gvtnGGaDue8s9M0Ws,2364
pyarrow/include/arrow/extension_type.h,sha256=YUvUzPz-0QiG-jOjs__TfyPEpsHWGkGO5PMVG1vMjkY,6639
pyarrow/include/arrow/filesystem/api.h,sha256=Xgy2GOZtBVwDjTaXPDyPPlS9Bwt9gjWXm5I_QbyRbFo,1383
pyarrow/include/arrow/filesystem/azurefs.h,sha256=M-7jR8AqyPu8uTXbw4INLb9RsZKrPmZlPO7T7MskQ_8,15299
pyarrow/include/arrow/filesystem/filesystem.h,sha256=tjRP-ZEWzES6bMfmDQ2MwnYr_3OGV5ZyfbZr3JqWZXM,29604
pyarrow/include/arrow/filesystem/filesystem_library.h,sha256=axaof-G9GxBjzXhRIt4azB7HB8VJ49MtGYsL7pSO0A0,1725
pyarrow/include/arrow/filesystem/gcsfs.h,sha256=5HSHqA9a1T2zVnZO1rNZKnXjLVyq1aAm6DRmQmg_v5w,10372
pyarrow/include/arrow/filesystem/hdfs.h,sha256=Jn91pjfk6RMx-MuAWsEAKLTyKQ7bDPNA5jMEVzafSgc,4133
pyarrow/include/arrow/filesystem/localfs.h,sha256=eIhPrpABheQz21WE845ULleTk83e4EtJnES4jALW6mM,4972
pyarrow/include/arrow/filesystem/mockfs.h,sha256=kohu7s9s9xtd75sGTE2K_rsHW89swDOtSSSFxBixMcc,4768
pyarrow/include/arrow/filesystem/path_util.h,sha256=hrDVHk4F9M7oGABB4x2wKfQMjSlSAIS0IaLVv2jHrl4,5698
pyarrow/include/arrow/filesystem/s3_test_util.h,sha256=22B600fr84Gq4rGALdidIP0IzDs9G3aOqkLN4y-6xrI,2962
pyarrow/include/arrow/filesystem/s3fs.h,sha256=q79pFbxFmsfA30LE78bOh6hj5Iszh01Vtw8LUpIa6T8,18221
pyarrow/include/arrow/filesystem/test_util.h,sha256=IcdXbz8kXaZDvUPN6Oc77UpWI2Psv1f_CKZxB3UGg-I,11738
pyarrow/include/arrow/filesystem/type_fwd.h,sha256=zztDER55Wbt4rVnkd-ReeDO-YnrpemftFeFtZ7ZGidY,1462
pyarrow/include/arrow/flight/api.h,sha256=YotLTQn-KCl6y5BIg8coEFZ9n7PMtJ02ly7Pc5gmX7U,1257
pyarrow/include/arrow/flight/client.h,sha256=NtFquWOaafBcmdIB4en9ua5xSEJaCBkC1ZHhAU_Gg60,17798
pyarrow/include/arrow/flight/client_auth.h,sha256=a3Dkm_jPOuqzNsDA4eejuMUwCEBMavM8uS7w81ihbRY,2216
pyarrow/include/arrow/flight/client_cookie_middleware.h,sha256=5zkCP2SxMFQuTX8N9NHxOve5J_ef2rFO6-xY4Tfnygk,1204
pyarrow/include/arrow/flight/client_middleware.h,sha256=aAZwCahuiBhP85iMPe7xNWvidBR9KeHGto2YAqJioI4,2948
pyarrow/include/arrow/flight/client_tracing_middleware.h,sha256=d0sTmUOfq5M9FMliIKK-flJkR6-7r69NjU2TpxhfqWo,1217
pyarrow/include/arrow/flight/middleware.h,sha256=JPQd8JnIVcwjTH6yOBck4BWR-WV95fpnAdhHyEYvfKE,2254
pyarrow/include/arrow/flight/otel_logging.h,sha256=riS9sZM2C3mH6VMbESizJ6lGmudqdJhfdCY9_cJJqMA,1139
pyarrow/include/arrow/flight/platform.h,sha256=1ZfzVaollAZosGyH_1JvzEA8iNR0hi9cUGz5eyLT1zc,1209
pyarrow/include/arrow/flight/server.h,sha256=GAcV0-THuBuj-bXfwqYrZ1P2bwZgKQSJLbu8ToltRvU,13185
pyarrow/include/arrow/flight/server_auth.h,sha256=xXkot_fmnEZ0yXHEBmiezAQCxM7vQem1Vo1V0vEwcW4,4457
pyarrow/include/arrow/flight/server_middleware.h,sha256=kRXm15c1mjPduoS7N61m0TG8nGIhN_ielsjK9yFlsgs,3155
pyarrow/include/arrow/flight/server_tracing_middleware.h,sha256=zR0FFZYGwAAqhzVhPVDjyXfZda9zmLteqauwA5dgR_w,2186
pyarrow/include/arrow/flight/test_auth_handlers.h,sha256=XkvMWucv9GQjlt2ttvYxshym4kUubUdMh-timlQIt1I,3315
pyarrow/include/arrow/flight/test_definitions.h,sha256=U_HhBnNdNZmojraYzICBmBvuef0bTT5eYzFXp8iGFzc,13110
pyarrow/include/arrow/flight/test_flight_server.h,sha256=SbRhZP0U4ILnbg7lYQvGeXmvPM_B6bai12FTM_HD4RQ,3930
pyarrow/include/arrow/flight/test_util.h,sha256=hDkQ0aNzthrvG8-7KgBXzs0Rt0iFYxwAGbWjzhzrpuw,7152
pyarrow/include/arrow/flight/transport.h,sha256=ZDXc-f8o00TFWESwsGU1My7rR9OfM3X7OZjDcGXTwIA,12181
pyarrow/include/arrow/flight/transport_server.h,sha256=iVdXmrb2pemh4o6BxwvB7OZAV4UeoWrbhe4ePZ5Pi4s,5268
pyarrow/include/arrow/flight/type_fwd.h,sha256=tQFAM3QNKPdzB4VqUGdEUFjNPYXVZLApwGnSus2GQx8,1797
pyarrow/include/arrow/flight/types.h,sha256=yVDnqhugA9OqhlIT4fCVzplFUKGvCL9CrD4EM6MCbmY,47269
pyarrow/include/arrow/flight/types_async.h,sha256=3nIQqwCYO4Ir3Mt2bG7BNntXxuNHYQNNpz-Yl3EaFTQ,2599
pyarrow/include/arrow/flight/visibility.h,sha256=N1k74cwyRvOaYFa_tCjdgUjiSdPBhmy20UuVGu0wTg0,1596
pyarrow/include/arrow/io/api.h,sha256=Pn4jZSTsLW8MAlMyXUokmJdupX54u154GYI5AvD5ByA,996
pyarrow/include/arrow/io/buffered.h,sha256=PHaMwCMaXu3vAjhDpZMC2xt5hKooJXVeMffhoDzTxNc,5912
pyarrow/include/arrow/io/caching.h,sha256=AAjoyKwQ06m2XiglFS6Ch_cdg2p4-wkA7GakGI_eX1E,6708
pyarrow/include/arrow/io/compressed.h,sha256=3JxIOo1q8VhjIErfwVM5ZLVkwwQKXd-FT5517j58etA,3774
pyarrow/include/arrow/io/concurrency.h,sha256=SmIr0OWCgMUR3j9ngVbjMJhWOUrU15jQf_jz2rUw7r4,7934
pyarrow/include/arrow/io/file.h,sha256=-ZEklW1Q0sj3pYCQLQ1ebirKd3s2GI3vUEIszFr8mVU,7625
pyarrow/include/arrow/io/hdfs.h,sha256=2s3f49ggAYgSCsX5SoqnomwsXd24_IZhW-VSBJclqTg,8559
pyarrow/include/arrow/io/interfaces.h,sha256=QIBHTJUobEkwcqnKMT_GEKu5ArzpeGmK-8v7z4qGHIQ,13428
pyarrow/include/arrow/io/memory.h,sha256=SY535DEFEOIXojttrsGmMtK5PClwGx5sHK8E_TlCGCQ,6321
pyarrow/include/arrow/io/mman.h,sha256=qoLBAGFcvpYTy96Ga7FNWDJKT3uhxpFAF3hbXIaDSiY,4111
pyarrow/include/arrow/io/slow.h,sha256=8-ZjQJq49EQJ4esQ6qHHjlKCeZNg4BSND7ire-ZtLYQ,3942
pyarrow/include/arrow/io/stdio.h,sha256=dqMTHoJbmiXcyNa2fN60tSWQsx0GPphZVCLdGiZNt8I,2095
pyarrow/include/arrow/io/test_common.h,sha256=SF59odb0E3rtGju5jABsQzZcUi9gl5TwHuKg4f0EQ5Y,2146
pyarrow/include/arrow/io/transform.h,sha256=W9XWonw69VymQAaQptfW7jD-6ry7VCpfPXlkB7aZzOE,1890
pyarrow/include/arrow/io/type_fwd.h,sha256=Pi7EFpFvBXsFN1xKOyZjTSP95xNDs6W5hxb5GucoVVE,2315
pyarrow/include/arrow/ipc/api.h,sha256=sbbb-uYHZtqE59AUlwNnAzUkP41h3Et9Rnu20gNKm9w,972
pyarrow/include/arrow/ipc/dictionary.h,sha256=UTjZPIG8mLZOk9IW2QnR9RZGr1npexZOp103fv-O70E,6104
pyarrow/include/arrow/ipc/feather.h,sha256=uCnxwO7eUH18kJ-lWz9IWwSj6AjfejqqLdoifJ-UBDo,4918
pyarrow/include/arrow/ipc/message.h,sha256=KtMCbIC2J4-5iyPG5Sijqu_MALxiuKWBYZhGnw0jxOQ,20011
pyarrow/include/arrow/ipc/options.h,sha256=rAnPbL2wXRg4tZpnC3FNkxWxPdhRR5xUM2tYkc1crZ4,7900
pyarrow/include/arrow/ipc/reader.h,sha256=NqdrqqAEItO1ecYUINRO7-qhKlYy-CHSJKGI2hdXlRQ,24106
pyarrow/include/arrow/ipc/test_common.h,sha256=kd6-NmmaT__0cDYIwes8lnOqbQDSqFZn8eg2N-SHfE4,6559
pyarrow/include/arrow/ipc/type_fwd.h,sha256=Ty8ET7nLI4JJeTqDMyP0pEH9QVj9xs7BpJkZrnrpaPY,1440
pyarrow/include/arrow/ipc/util.h,sha256=wTkfC9YFKZlAAjyzlmQVZcW90oOj_JatjDN4qz0IxHg,1414
pyarrow/include/arrow/ipc/writer.h,sha256=hum8E_orkG_X38vgyfyKhGbyvcLJ3AkXEykyBjAXIYg,18870
pyarrow/include/arrow/json/api.h,sha256=XRW1fP43zVqwy1yabaKctNK9MDZqnxkoHDH1fx5B3Y4,879
pyarrow/include/arrow/json/chunked_builder.h,sha256=DDuMwrImMECw6Mhfncn2xMOjkFcKUV1O1597_fSFSAs,2365
pyarrow/include/arrow/json/chunker.h,sha256=dkZOcxsF1Q3ek58P7IoA8f3lQyBQpFvGSFeynNV2Olc,1119
pyarrow/include/arrow/json/converter.h,sha256=3lXsP3BSdpLPIkFAJnYW9vP8BbX3neVYR_W0zFKClQ0,3134
pyarrow/include/arrow/json/from_string.h,sha256=y--AK08e-EivbrilSGU6xtPH1VpqBNZIYvCMUT2_V_E,4106
pyarrow/include/arrow/json/object_parser.h,sha256=Y_6Oceya06aUyeo-1k047dm2-JUMJa2_w9iyZ-goIRQ,1627
pyarrow/include/arrow/json/object_writer.h,sha256=UrIrjCkIz7Q5n_FpV5NNPD96gHHdTkvTJaekuGBHwTo,1428
pyarrow/include/arrow/json/options.h,sha256=EypQgDwLZQbrPnAh45nSPfpGGYrxvLgfp1eAG_l0p3Q,2227
pyarrow/include/arrow/json/parser.h,sha256=3oIzO5kUs2Takc7t_d5mH7bp1uIcc1M-qbuHmPoSI34,3383
pyarrow/include/arrow/json/rapidjson_defs.h,sha256=lBJlfuYWIeQQ8awPd3bk4jJc81efr_KzKwG8Klw7t1s,1474
pyarrow/include/arrow/json/reader.h,sha256=KNO9dCyc2RZs7WxUSEW7bpCYBh_h1C3U52YHYxBnP0M,5212
pyarrow/include/arrow/json/test_common.h,sha256=YiiY_jswpp7Nu6IW1Y2lBhqWSFRoNaNEy1jHd5qkYHQ,10874
pyarrow/include/arrow/json/type_fwd.h,sha256=o9aigB5losknJFFei1k25pDVYZgkC2elmRMX1C6aTjo,942
pyarrow/include/arrow/memory_pool.h,sha256=d5uMlmRp4C3TrvLj47-95Lqq80Vf1F04FC5zQcRjt90,11392
pyarrow/include/arrow/memory_pool_test.h,sha256=qv7csk6hZiO2ELFF-1yukpppjETDDX0nuBFBbPFHtMU,3350
pyarrow/include/arrow/pretty_print.h,sha256=Z36O7Rp3wp8gCG51YHh-x8ytrrENDpFZSPmSSXs9TAo,5716
pyarrow/include/arrow/python/api.h,sha256=2XyYOFAmorPGXrTVYZkiKL67nuH8w1ha6D1reU7K-Vs,1179
pyarrow/include/arrow/python/arrow_to_pandas.h,sha256=jUBEUMKXw70oJdMlgkSf6HitaNweQcc7hxI75_C9WSI,5561
pyarrow/include/arrow/python/async.h,sha256=C0f8YYmgwBGgDau4xEFsdjukiZB4YvpylETHEZryHOo,2352
pyarrow/include/arrow/python/benchmark.h,sha256=f-kzyMOlPKDse2bcLWhyMrDEMZrG_JHAPpDJgGW0bXU,1192
pyarrow/include/arrow/python/common.h,sha256=9IQC__JUqTRXv5MfAqKM6ivMwuQ-uMZKEguGDEODkiA,14389
pyarrow/include/arrow/python/csv.h,sha256=QxU3B-Hv_RsoEcMGS9-1434ugouL2ygC64Lq6FgviNM,1397
pyarrow/include/arrow/python/datetime.h,sha256=Bny_THGi2tyUeHxcOuw01O7hNE8B_gave5ABAZQtwTQ,7931
pyarrow/include/arrow/python/decimal.h,sha256=K9ScmzSa7XCuuwpXKnq0GPbRDTuRU5yXfWr9zGtOchc,6362
pyarrow/include/arrow/python/extension_type.h,sha256=0gzb42y_mbw4fsYs3u8cwPFLBRlG-kkHQLgbvGtrY0U,3181
pyarrow/include/arrow/python/filesystem.h,sha256=FG0AcLekqaDf9IQPqKixAfIcY_ZLgIKP5NvvXdtBVUM,5126
pyarrow/include/arrow/python/flight.h,sha256=t4ZD8gWaubbO_mBDv0XZ58TCo1VQl0hzHn1_7w4U64I,14450
pyarrow/include/arrow/python/gdb.h,sha256=H-qvM-nU8a_3Z5tk8PvppTwQtBMSZhQKQIVgRAsRfFg,972
pyarrow/include/arrow/python/helpers.h,sha256=Q3TmwyffFpVYQEp55ZoWTDDhnte7zQKiXylsfbEMCuI,5479
pyarrow/include/arrow/python/inference.h,sha256=FUFvB4Zy7V-tueXdmbDcqTeLK4xj5GZEeRW5yhiJlsU,2038
pyarrow/include/arrow/python/io.h,sha256=4jGnodpSUlnVqAVh9fWId7H4WldlLPkXyroABpdaW6w,3858
pyarrow/include/arrow/python/ipc.h,sha256=SZbw6jCCqLiLNCY3k632GmwHeD_r_xrDS0dhqV49VhY,2259
pyarrow/include/arrow/python/iterators.h,sha256=Ugfm3JvetAH0l-oAjjpZfhrUBqRimVMaw4-xusvqLSg,7327
pyarrow/include/arrow/python/lib.h,sha256=d-KUXKrlZP_EDmsH5qwVfJS-IPiyHt8qi-KcbJ2BYfQ,4562
pyarrow/include/arrow/python/lib_api.h,sha256=7GafxqOe7WVEyVic3V4vcEqTRTxehjKSXH6MMyXtqpY,19794
pyarrow/include/arrow/python/numpy_convert.h,sha256=y13eHwfe1lJKzadoTr2-GyX6xPsE6Z7FN31s7PN-2Rk,4870
pyarrow/include/arrow/python/numpy_init.h,sha256=FniVHP7W2YBlenoMYhQrODvoqqvDMSls2JANGtNPQts,999
pyarrow/include/arrow/python/numpy_interop.h,sha256=rI6ek8JTOYtjo7gEADSDBS6QuAOHa2A0YQPZ2GeypFw,3418
pyarrow/include/arrow/python/numpy_to_arrow.h,sha256=z9KapsuoOSpWILPt9bea7GR4BL6AQ28T6DUO0mSkh3k,2760
pyarrow/include/arrow/python/parquet_encryption.h,sha256=Mc8tZ8gIfkH0AckNiIOt6hesP_MVKeKhcytT24ZOLdQ,4861
pyarrow/include/arrow/python/platform.h,sha256=XYS5IqiMUejxN2COzu60Zs8b_wAaGTBw4M-zKVqqs5U,1422
pyarrow/include/arrow/python/pyarrow.h,sha256=TK3BtD9n3QKOQ9dX3LXbQc0hu9alWcufV0O93iQW7B0,2761
pyarrow/include/arrow/python/pyarrow_api.h,sha256=7l0G4-_m9yALYoifsY8Z6qh3HHD0PgkpVSgCn_JaGU4,867
pyarrow/include/arrow/python/pyarrow_lib.h,sha256=-70_Ckj3_0ImlzaXSJOE_d3w9pGM66lXiGPyln9c96Y,863
pyarrow/include/arrow/python/python_test.h,sha256=ea32mM20uHySlygi9MtVxr26O-ydTZHCUQIlxaIMjT4,1195
pyarrow/include/arrow/python/python_to_arrow.h,sha256=BoVytf6P7PBYXyznchElKZSFvEsFyimB-tLFdw0AUNo,2521
pyarrow/include/arrow/python/type_traits.h,sha256=U7dW4sajVVmjXALsP-B8ZZwI4GgIUG3t-rbp2fwrHWI,10236
pyarrow/include/arrow/python/udf.h,sha256=de3R8PhNJO5lT9oCqRxe8e2_SE3jBpHOkwbNqCrlgjQ,3104
pyarrow/include/arrow/python/util.h,sha256=sP2CEGG7NNqwjrEeNKB-VFqkKxn5P2Tsc0jPUMXQuLg,1732
pyarrow/include/arrow/python/vendored/pythoncapi_compat.h,sha256=bzMnlHTCfjk5DQRIxwytunYh5aQxU3iSElaaDyNnAY8,40900
pyarrow/include/arrow/python/visibility.h,sha256=hwJw5sGrWJckQkNaAuLe4Tf-VDjQbXknyzNOVgZI3FI,1381
pyarrow/include/arrow/record_batch.h,sha256=aeGk0XZ4_C4Nq4d0rby8qyKoJ58fnm_SBBSbSBc3jVE,18517
pyarrow/include/arrow/result.h,sha256=8LGFG6EzpkVOCxU8v5usykO3DxuEhJD63pY72d78C8I,18152
pyarrow/include/arrow/scalar.h,sha256=7SguSvJ4wohjqV-FSKboC1pgaFVB09dNfkMz36JjcM8,36543
pyarrow/include/arrow/sparse_tensor.h,sha256=dd6eQmCjfCmmI76hgsC37R-qPJ11IMhafVaxSo2XJFs,25205
pyarrow/include/arrow/status.h,sha256=r9B963ikSEuSQpbnuGBV-pl1Dtfi5Co0gFd7hI0AMUE,17392
pyarrow/include/arrow/stl.h,sha256=MtEWXG3x1YYNi7xPdDOGVRqpnLVvNQFiE_ZJAsPFJ1g,19343
pyarrow/include/arrow/stl_allocator.h,sha256=TBbvjbuQIH9y88FI2SaqAL7pOIt3wZ1xMKwXqeKNiJE,4956
pyarrow/include/arrow/stl_iterator.h,sha256=RelNQrADHupKWTuFBCCkqVlyuGHXU3yB6gcsDpQpra8,9953
pyarrow/include/arrow/table.h,sha256=fk3grDPFynBxTyN1RHtscyeGOWj_sZn1hBYdTo-tBQQ,14689
pyarrow/include/arrow/table_builder.h,sha256=LRcLCL2iUrj6vF4f9AjPswVjqtqlMw7z_8VBAfUJeCo,3763
pyarrow/include/arrow/tensor.h,sha256=t5qdeufYoYG1Tl4DCiDzeZkOhyiQQPOGbMKCLa0u7WM,9089
pyarrow/include/arrow/tensor/converter.h,sha256=RZq0Try_kiZ085_d_CvhewMsd57InGb2TCeiveaf-Oo,2891
pyarrow/include/arrow/testing/async_test_util.h,sha256=IrHWfPeIyhrgeTGHUPLt92LdsofmFX6khjngWsZv3dY,2262
pyarrow/include/arrow/testing/builder.h,sha256=4x0bWOedaVomWU0m7dF99irOv3flR-_p-IMofTDZtwo,8556
pyarrow/include/arrow/testing/executor_util.h,sha256=38_rF-V_9zF1ttJMspkPiI-34VU1RDjg1ADBS8lUFHk,1885
pyarrow/include/arrow/testing/extension_type.h,sha256=2xHmtD1bnK-u8Or38keTBvDuXhgKvsVyiiO-exlWHXg,8113
pyarrow/include/arrow/testing/fixed_width_test_util.h,sha256=g6yB7RkziU7HEhNJnxOhkn2nE5HeXaFX3tbBX3q9_sE,3091
pyarrow/include/arrow/testing/future_util.h,sha256=qIhi417OGMWSMUSDHjkGTYd-ihZbqw8ZSIRwJ01vbKg,6246
pyarrow/include/arrow/testing/generator.h,sha256=zwEI_ZS-nxOIoYZUW_X9VE0wg5ILk8PzBpeo6kqXUDM,13525
pyarrow/include/arrow/testing/gtest_compat.h,sha256=0NqH39my7m1FMpsrQYnxQx4bdEE10SCXZaysN6yjQFA,1311
pyarrow/include/arrow/testing/gtest_util.h,sha256=s6hzot0wcsaVvajwRlBYmWGUjSvJ13o8AlNGLhEtPr4,24381
pyarrow/include/arrow/testing/matchers.h,sha256=Z6jJW4fGlvtpc7oWpPlx5ynLk2tnLwF-dhv5fQIcvqc,16832
pyarrow/include/arrow/testing/math.h,sha256=YRoNVZVreF1j8G1G8oaaFzeK7VGYA2Q25j10gFN_hTA,1210
pyarrow/include/arrow/testing/process.h,sha256=AzPW3Lh2R4sTm-RUUi4Od3aSba9zoLcS_zHBxztv4zI,1372
pyarrow/include/arrow/testing/random.h,sha256=UMxioQORvoZOsodZM6T-ujza5WuYKwAndbvnOImDsqQ,37046
pyarrow/include/arrow/testing/uniform_real.h,sha256=-G_2J9cvevoCtB55vsCsWtJkMUHLIMyOwdT6G8ZW45Y,2970
pyarrow/include/arrow/testing/util.h,sha256=xECPSXF8LbS9FWDLIivrJMXpjMU0Tf-vZ6YMpto3mYQ,5582
pyarrow/include/arrow/testing/visibility.h,sha256=-wjc00QIhygXJa7tknbIL685AQ1wnyCPr-EtVzkzmq0,1606
pyarrow/include/arrow/type.h,sha256=7VaBkMx1Ot4wgXtcUVunB5ZC4txMagh71bopSFbwy08,96952
pyarrow/include/arrow/type_fwd.h,sha256=X-drEwMCZXh5xt83ilfNvRK7f5nyEXZnd2ST4NUAffs,23504
pyarrow/include/arrow/type_traits.h,sha256=tuVBDqrTfRTPnPEJyGQgaRF9Y_xK_hYDEppp8mb7H78,55934
pyarrow/include/arrow/util/algorithm.h,sha256=045EVzsC9rThlRVFaCoBmmtWZmFy5y28PR9yapn9sXY,1229
pyarrow/include/arrow/util/align_util.h,sha256=DG2L24KReTiU8nFpXLigbflkKouKWTPUf6osQs6mxiY,10669
pyarrow/include/arrow/util/aligned_storage.h,sha256=e0pCTBZ9hn9VAVTnENubbEii_witF73xUeBZNIQd73I,4254
pyarrow/include/arrow/util/async_generator.h,sha256=avVH3JPaDk_vrEh8uhPLopVN3LSn-29yst23GoptZR8,78200
pyarrow/include/arrow/util/async_generator_fwd.h,sha256=Q8sueYZWwdxkaVDabuglHM3XuP--NerYgq8UxsVHgFY,1728
pyarrow/include/arrow/util/async_util.h,sha256=1nnAJZ22iK7wSzmvZDo3PMhuWqJIt2qKdlXzTyhoCK4,19759
pyarrow/include/arrow/util/base64.h,sha256=qzcBE98cg8Tx5iPJAvQ4Pdf2yc6R2r-4yGJS1_DEIeY,1095
pyarrow/include/arrow/util/basic_decimal.h,sha256=Q2zfksUdYc92TL8po81I_aQOmwHTwZGPjqXvJM7cgek,33569
pyarrow/include/arrow/util/benchmark_util.h,sha256=SG3gfwE-wGNZAwpL3TvffnSiZGM2cztV5xRBnbqy2Mw,7641
pyarrow/include/arrow/util/binary_view_util.h,sha256=-sFAQX9cnfWmmZJo8stFX5vkJki7T2UloAvDzYO0MN8,4625
pyarrow/include/arrow/util/bit_block_counter.h,sha256=iSIemzizxVokwC0Ze6SjSi-al_nrP2ViXF6JPoIVUWc,20162
pyarrow/include/arrow/util/bit_run_reader.h,sha256=TwXyYJiBQLXm6QB57F-AJDG3ofE7fYa3FhDWNsHFfio,17196
pyarrow/include/arrow/util/bit_util.h,sha256=S0TbReZet8MpPFZk9wjfYzfKpkBquthkkFk2QtxzB7U,12108
pyarrow/include/arrow/util/bitmap.h,sha256=eGjGlh6s4Dubhfrk-Kp931YgR8KyaHNcxbTW1rqtqbI,17459
pyarrow/include/arrow/util/bitmap_builders.h,sha256=28rjB9AGwG-VMzuen9FARssdMul0RJx2ZmPFQL9SV1E,1596
pyarrow/include/arrow/util/bitmap_generate.h,sha256=m6ZsNwx1GhsEktQr63NxXHQkX2B7Nti011XYsPg2xfo,3661
pyarrow/include/arrow/util/bitmap_ops.h,sha256=fPPD_cvXebYLrAeQOCCYydU1AwF0faN5DwQKV8op-Es,10877
pyarrow/include/arrow/util/bitmap_reader.h,sha256=pLrMDWhVo-Qb3V1mLASAz_aI6QZxDHRr37EtqxqGd9E,8353
pyarrow/include/arrow/util/bitmap_visit.h,sha256=myn8k66VrvZnL6R6VW6IDPTfO68VxjbJ8Up5IuSjFL4,3470
pyarrow/include/arrow/util/bitmap_writer.h,sha256=a4goXhLlY0qcfvYxbfbGD_HZ8Au1wFcbV1tVF3BPaXs,9383
pyarrow/include/arrow/util/byte_size.h,sha256=Pd2c_3a0IeSOUevhPIlXNkDmgoB06g4c9YCsuRwwSKM,3997
pyarrow/include/arrow/util/cancel.h,sha256=SgAy5S0TBISnCypp4R_OI-jPUQ997qtsLOSdXo8l5bM,3659
pyarrow/include/arrow/util/checked_cast.h,sha256=SR9Qg8NuLSBJw2w1UfgeGvCfT8k7wrbN7BzADQOZfAU,2076
pyarrow/include/arrow/util/compare.h,sha256=OLrSSyllkY4Sv00IK-37A2d68gr4OwnWJsxn1aF9xTU,1982
pyarrow/include/arrow/util/compression.h,sha256=fvlURoWJsgO8Hr6Xs_VNaqiOatmIGn9ktVUkYv7pIu4,8427
pyarrow/include/arrow/util/concurrent_map.h,sha256=wMi9WDHfRuJ_aSFgcJPpsVwGJ9vIJ5agaZ3rVUlwGe4,1775
pyarrow/include/arrow/util/config.h,sha256=_nhlf9cICgz-hvfFG3U2jrzQEVUHH8XcZ9CaRSIN-RA,2255
pyarrow/include/arrow/util/converter.h,sha256=PILfos6VlnLK6fOFMfLIUhiKl3o1dJo9T4HJXeR7V5E,14637
pyarrow/include/arrow/util/cpu_info.h,sha256=MqLdJabBZkzDjiScaQ7if9dmoAGvXT2QavGoGkho3lU,3964
pyarrow/include/arrow/util/crc32.h,sha256=4gN0M-SRnxaGKci2ATPbMWZG2TG3YULXjaTpadV0Udk,1337
pyarrow/include/arrow/util/debug.h,sha256=CPB_oDOuZ_u89e9wM8bGn88mGvClgfa7UDxDph6v9sY,971
pyarrow/include/arrow/util/decimal.h,sha256=cEQRH5x5kN_MAsWNOPCLjz57QjW4ErfppMHG4b-0EOA,20291
pyarrow/include/arrow/util/delimiting.h,sha256=JYe9YcWMeFT_ISuojx_VgVqOYLvZ2TiiR2sNn-WdeBQ,7317
pyarrow/include/arrow/util/endian.h,sha256=FC0BSwbwhLwAEYrvkHADyDYzXr7kEFa-N_KTicHKeaA,8191
pyarrow/include/arrow/util/float16.h,sha256=S9iXXYPRRU0zGz8ExfLkG8Mh1HqdgZlntCdWztn_Kag,7403
pyarrow/include/arrow/util/formatting.h,sha256=3D0xE7sTcJR6XEUb12LKSqL-A3bX_Pyp1oVkPScjCwQ,22516
pyarrow/include/arrow/util/functional.h,sha256=4ljKXSWX3G_lBT2BfLXuG44pzZwVKeaojpLWCniqKyc,5612
pyarrow/include/arrow/util/future.h,sha256=tsSVDEH2dhXKyvIKl6R9BVBolpPdZXoRRf2-YRbtdxg,32296
pyarrow/include/arrow/util/hash_util.h,sha256=CjiNVPUJPxXvVJy7ys79aIb7YB6Bm-5nTJAR4DHsxcs,1918
pyarrow/include/arrow/util/hashing.h,sha256=2rIuTiAhMVsIt52fc9RR6vVM9NNfzeFJPsAPWCd6vX8,34224
pyarrow/include/arrow/util/int_util.h,sha256=zTOAq57M4pUe469WpnW6I5hNtxe3vGRHlZWhngA1DzM,4859
pyarrow/include/arrow/util/int_util_overflow.h,sha256=AtvkG7v3-1gVzW5SrFrdVkYuXFtT76_nxrKtzIbz_9U,4895
pyarrow/include/arrow/util/io_util.h,sha256=U6VTCh0yKUmYPaw2oG-CllJd4J02Gce6b0qTfqFi9E4,13709
pyarrow/include/arrow/util/iterator.h,sha256=FymwfXvx9akIj1hAvLgrFQ01BWJbvAv4xx06TNDc9dc,18345
pyarrow/include/arrow/util/key_value_metadata.h,sha256=wjU6uQGcSmy-YFqMs6rwLP7E4X-0IFBjPrWZstistzQ,3590
pyarrow/include/arrow/util/launder.h,sha256=C3rNBRh4reuUp8YuRdGQU95WPc8vl4bAY-z5LXgDiuA,1046
pyarrow/include/arrow/util/list_util.h,sha256=_OmtsDqe-mnZ_7tVWxB2yHdgCJhpiME_RP3nXHzKbdI,2028
pyarrow/include/arrow/util/logger.h,sha256=p9i4dNgne36LWpFmNSYBYgTQ4kFSao20dJ40LgRRZKQ,6693
pyarrow/include/arrow/util/logging.h,sha256=63wlnOug1zSd1ur7caDp0F0z2IG7QjHbUFGChE8f-nA,9139
pyarrow/include/arrow/util/macros.h,sha256=dqnFiDUrFUyqHyNP4xEr54WgaAEXX8gE4ZG7-i3nfZQ,9336
pyarrow/include/arrow/util/math_constants.h,sha256=2sfWoVc8syHz8X26XgBmejzXStl7hmvKiOh9622oUZA,1112
pyarrow/include/arrow/util/mutex.h,sha256=n4bsrHK2Q8zbYsQEyNaFqNu__vvqgwo1AfrLLCxfkpU,2554
pyarrow/include/arrow/util/parallel.h,sha256=_43wTVSrtJwOkRt3KJcJF1DyYHwhoWbWPsrAswftP-Q,3817
pyarrow/include/arrow/util/pcg_random.h,sha256=nbXowfCJFiy4GjVfF9I8VvB6fxkyR5zNB1FKdnFsYTQ,1252
pyarrow/include/arrow/util/prefetch.h,sha256=vaE4FPdscbtO0cPbzl8F1PzB1NDO18ytYlEmZCHDjHs,1251
pyarrow/include/arrow/util/queue.h,sha256=X9vRZQX3YL_a2Lzwe-zcNNHguR7FoGYmD-Q0THqsCBM,1017
pyarrow/include/arrow/util/range.h,sha256=yhe5pJiZIiLUO8tYr408Y9yEsFrFd7FrBMeTL2hAOKY,8526
pyarrow/include/arrow/util/ree_util.h,sha256=NO71naM7twCfCrX52vPmWoNhL90XP4PFEdc6AI9-OCs,22397
pyarrow/include/arrow/util/regex.h,sha256=Tj92CttOh2HxS0EKQ_9-sxMBAsQrDOUKNP0ngIJFdP8,1742
pyarrow/include/arrow/util/rows_to_batches.h,sha256=PZNoLeMCfJJdeHVvUny0UHc5AtS0hctUCi7zUztJpeE,7120
pyarrow/include/arrow/util/secure_string.h,sha256=1SbYlA_HdgJaqmKRxZ1RYX-Hb6pnV-1RGZHGtcMWG-c,2485
pyarrow/include/arrow/util/simd.h,sha256=PpKm-aWpZYIYP0NnyGrQceOO9m3_7JbN4uro0IhIT9w,1679
pyarrow/include/arrow/util/small_vector.h,sha256=dDNNMFpNdtIbxLP3L-h_bv3A8raYv4IVuyLEzUVMgck,14421
pyarrow/include/arrow/util/span.h,sha256=1edvehJ567sQfZn4gGvn4kcVpLAJU9raXHbqE_dDpZQ,4298
pyarrow/include/arrow/util/string.h,sha256=hYtg4d3kGQBHdd0vGuKJTlVeueCCgfyD3iq-feMA3p8,5756
pyarrow/include/arrow/util/string_util.h,sha256=0IUIcjSf2zMP8rVoVx5y1satijO7cr0PvOgG32d0lFA,2364
pyarrow/include/arrow/util/task_group.h,sha256=fI330NoJT8u84AEUA6pSxWrE7UBKn2LaM4DfPFoalqA,4362
pyarrow/include/arrow/util/test_common.h,sha256=ZniLT8TvAUdCE2T2YrtlDKdwDNPBpT5e9V1EiPHH9LU,2837
pyarrow/include/arrow/util/thread_pool.h,sha256=4ztLwkJHQJQmTmqwy8IGDmAo8X4N-o3qi6f91agzkkQ,24426
pyarrow/include/arrow/util/time.h,sha256=4Xi8JzaYlWFxVaenmCJ7orMgu4cuKELvbtMiszuJHUA,2988
pyarrow/include/arrow/util/tracing.h,sha256=sVfC_Rj2gwkWKVSKT0l0FOO5c2EGsfYwlkZX4d9ncxA,1286
pyarrow/include/arrow/util/type_fwd.h,sha256=JeZ1muIiVKxHcAXzBikMCmk_7SxFz_IqmzuT6yY1F-A,1803
pyarrow/include/arrow/util/type_traits.h,sha256=iRrIf4YhuMOGL5DJWIJoV2bUEhZRHWuce8TXm-JMkns,2155
pyarrow/include/arrow/util/ubsan.h,sha256=m18PxnY-B0x4twewjyNt2UUy5qHntiolMnWC0JkkTmM,2817
pyarrow/include/arrow/util/union_util.h,sha256=PSssBiw-v-PDen_q75c6OkNO5PwyIPhGbf9PMJj7P2M,1211
pyarrow/include/arrow/util/unreachable.h,sha256=O1TG4ozCYT3_xvDpJouKWrlFADIEpIemQ28y4DqIwu4,1070
pyarrow/include/arrow/util/uri.h,sha256=D24zebazFcrKGt7iGpkcGQ87DuF-2AbjPKVkDlq9Nuk,3886
pyarrow/include/arrow/util/utf8.h,sha256=flGZ786kHo33Xg_zw0zVA9GAT8jYdPUHTVhIPHGjOj8,2031
pyarrow/include/arrow/util/value_parsing.h,sha256=l1dhC7wy07po_cb1-Eo58333nBcyNOPYOtaVJbdXpxA,30098
pyarrow/include/arrow/util/vector.h,sha256=w1lxZG3CU0gq2ZrByeU8QX2A0JeTtooGdaZONUsVlfs,5697
pyarrow/include/arrow/util/visibility.h,sha256=DFEdl8TCr30r3b7vlpgzJIiA5NsK7eW9UmeL47PgcLk,2835
pyarrow/include/arrow/util/windows_compatibility.h,sha256=Chme9fWRqYRzfIbLw7V_yeiIWd3F4dFeG6ImHHr4Xqw,1255
pyarrow/include/arrow/util/windows_fixup.h,sha256=hjoh6zvB8u8OVUQqLtdcrmohMzoAoLy6XJFLxcfFhK0,1435
pyarrow/include/arrow/vendored/ProducerConsumerQueue.h,sha256=Bz1ks3NDgXXLfT8TMUkE38RpMOSwKRRtwU1e37Y1CUw,6101
pyarrow/include/arrow/vendored/datetime.h,sha256=tsFbz8LKBFzRzTEOAKZyWRbdFLfnCnZRCK9Tyi1PANs,1103
pyarrow/include/arrow/vendored/datetime/date.h,sha256=fa_tmkMHwmx9vZHjH5lZVhZFCYEcHi5xmueW3F1tDE4,237808
pyarrow/include/arrow/vendored/datetime/ios.h,sha256=Qnu0iuy2-ein9KkVoSL1t71_W_VFZkdjDVsOnYTnP38,1641
pyarrow/include/arrow/vendored/datetime/tz.h,sha256=m5JJv7LE7Vukp8h50r90sCfbOSAD2bMVIVQUUxNZeDQ,85347
pyarrow/include/arrow/vendored/datetime/tz_private.h,sha256=pDkKXYdzfzQ5uh-jcUhURBLqHo00t0UnlimUdiM53Cs,10706
pyarrow/include/arrow/vendored/datetime/visibility.h,sha256=VCGKzhQOgL1zwGXKl_7lLULfSy0OsPt8FLWHwA4sOtU,1002
pyarrow/include/arrow/vendored/double-conversion/bignum-dtoa.h,sha256=imGhcg0RywMsFNMYTqp6rlXw2HZCIAla8SC_n92gCqE,4358
pyarrow/include/arrow/vendored/double-conversion/bignum.h,sha256=RnQ2CPL8Pt6fVCGh_8VDF11e_GyrrwO0IH0uMnTcsEs,5949
pyarrow/include/arrow/vendored/double-conversion/cached-powers.h,sha256=jjwfR3bue7mNlE5lbTrFR2KlgjRew2OkmjBa7oQO0Qg,3079
pyarrow/include/arrow/vendored/double-conversion/diy-fp.h,sha256=J-RgqH27jspT5Ubth9pTA9NAZH6e7n1OVhxModgi8Sc,5088
pyarrow/include/arrow/vendored/double-conversion/double-conversion.h,sha256=J1Tl5-8aFY0A9SnaA9z5Q90jnMxw55illPIuE-jdD5Q,1804
pyarrow/include/arrow/vendored/double-conversion/double-to-string.h,sha256=C-tKRi0IuLycXgS6CC1oiFkCroOo_-AO0VOjmfe0tlE,23925
pyarrow/include/arrow/vendored/double-conversion/fast-dtoa.h,sha256=ZAho25fqeP3t2RM0XgqfhTBXQIIicACLpdyHHMRX3JU,4122
pyarrow/include/arrow/vendored/double-conversion/fixed-dtoa.h,sha256=HLnpxkHjKldm-FBiDRbADYljJBSYbQGP4Gz-sVbiSJU,2828
pyarrow/include/arrow/vendored/double-conversion/ieee.h,sha256=CVKA9RXSjv4ZygqDHMiF-H2hUh3QHQvp1GZYC3MAhkE,15281
pyarrow/include/arrow/vendored/double-conversion/string-to-double.h,sha256=Ul6b-2R0pjUaAWNM3Ki4kH933LqrW6_XfPz4BSiE2v8,10906
pyarrow/include/arrow/vendored/double-conversion/strtod.h,sha256=6xCRm47vmcghYJug5mhhTVbsZ3m3Y6tQfMehEyVZNx0,3096
pyarrow/include/arrow/vendored/double-conversion/utils.h,sha256=wFRb5cGABiNoUSCnvKmdv_KIMcBtX1PX89tPFfvgbQI,15614
pyarrow/include/arrow/vendored/pcg/pcg_extras.hpp,sha256=FEYzq8NFxPfdJyLs4kVtTBLkaD6iO71INz9EJnaxTdc,19784
pyarrow/include/arrow/vendored/pcg/pcg_random.hpp,sha256=7TaV3nZhcwpf6XxlZ6cod1GaW5gm-iUn67t2fiMPNbA,73501
pyarrow/include/arrow/vendored/pcg/pcg_uint128.hpp,sha256=r8exMtH21S8pjizyZZvP8Q8lAdxkKF22ZEiurSTFtzM,28411
pyarrow/include/arrow/vendored/portable-snippets/debug-trap.h,sha256=9KphJ9gRtDT9DXR9iZ7aS23xa2T8tLmLsFEJMg0pLDQ,3081
pyarrow/include/arrow/vendored/portable-snippets/safe-math.h,sha256=q9yWh34bsFu1vSqLTuI3n_cIU4TlY98Lk1elxKHvZP0,48167
pyarrow/include/arrow/vendored/strptime.h,sha256=q1IZi5CvyUp_PNzbQ4_XLroAV24VEovBEz2TkpwUJ9c,1212
pyarrow/include/arrow/vendored/xxhash.h,sha256=MUwtyzu7xjkx9mBcS65SaDcCK7tgeqQgj-KYEMxcHWc,844
pyarrow/include/arrow/vendored/xxhash/xxhash.h,sha256=videnbIaUDw38kaDzbSQjyNwo-NauW4CxOpz3I45nEM,253096
pyarrow/include/arrow/visit_array_inline.h,sha256=XuQjuME8XZeJp7W86YuCsuoVVgmG1NulXAA0KJkmmB0,2446
pyarrow/include/arrow/visit_data_inline.h,sha256=4MkdFVsrjhMyTDNrScQtOYV_nwzqR2ddSS2yYnbyLt0,12460
pyarrow/include/arrow/visit_scalar_inline.h,sha256=KvNY0j8nE9gs_805LXMV3ATgvxvUqW4UeKpXUxR3rMA,2419
pyarrow/include/arrow/visit_type_inline.h,sha256=O0BURx2TowqdO61W9DJEr5cKP5BsVkUt8Bpc8VuiXec,4387
pyarrow/include/arrow/visitor.h,sha256=NKos98j54uY9tdXzctI_n_nwFRrXNOwanxLDqDZONw4,8690
pyarrow/include/arrow/visitor_generate.h,sha256=n2YKZW-5hY7ICQSwEUBZIYh2eg9ZoTfD54XRd9OlNDo,3324
pyarrow/include/parquet/api/io.h,sha256=Ricq0d2R4QXHiGZCbjxZ_0F_QmKq0IrfTidNu5NoXPI,847
pyarrow/include/parquet/api/reader.h,sha256=vnM5XDPn1TVsDJk4SDgb3ZU2Ta4vdrRzCpDWO90rYHk,1204
pyarrow/include/parquet/api/schema.h,sha256=KsNJ529pEh7bGUa0rLUCcfanI9rW2uSTirgpvKq0hdc,855
pyarrow/include/parquet/api/writer.h,sha256=UJZbY8QGVRMtAmozzjoM9TnI4gssqlNFUKCXBw2IfuI,1007
pyarrow/include/parquet/arrow/reader.h,sha256=hO2-WqOqZoVahbR68KYaTYYWIwzGzuUZAxWnB4mylQM,15699
pyarrow/include/parquet/arrow/schema.h,sha256=Mi56ul7itNS6NDbMpKOJCufjHVqaSY5_rbsNRNLE560,6204
pyarrow/include/parquet/arrow/test_util.h,sha256=oyaIkVk3Js9_LL5jEbXbzKIaCiXMtcBaeQkmtEMt37Q,20573
pyarrow/include/parquet/arrow/writer.h,sha256=0edx0fgyze6uxTgpDyacSljKMUay86pRqvIRQ7AZpXk,7005
pyarrow/include/parquet/benchmark_util.h,sha256=emDpaIbtfUNlK0M2zMP-oTy2S2raQ-LpQULK_G74OtM,1758
pyarrow/include/parquet/bloom_filter.h,sha256=TC3OxK0J2v6tHxT_Bbw7mlYtM0603KXgBoHRvmzM9aA,14999
pyarrow/include/parquet/bloom_filter_reader.h,sha256=63kpHYKs5TPrbRamkBLZsDYbD-I9UeVhF-R8d7JHeLg,2892
pyarrow/include/parquet/column_page.h,sha256=7w2hQDqkIhqpHLRh2LHk0ujnuknSPHVtuvJ21LS--n0,6942
pyarrow/include/parquet/column_reader.h,sha256=n7SebtrPLT78XHDhI3Pf7G07AFt4VbLHVNqPth26Sv0,19206
pyarrow/include/parquet/column_scanner.h,sha256=HecBvh-z0n_1HJsD-GIdcGHQAvDOHKlLzppB9RBsD9s,8863
pyarrow/include/parquet/column_writer.h,sha256=Y9VN1eJtsYmQVhpL9UPiWGrHbgSDbDds19Z1nv_yfOA,12294
pyarrow/include/parquet/encoding.h,sha256=SDGAMKCvNwOz92WiadNmhVO2bPG-IVUJJ4vb_pShtpo,16872
pyarrow/include/parquet/encryption/crypto_factory.h,sha256=NutbypTw9971ju95UkWtW-g8TYnR94TWZAnx5-dznPA,7064
pyarrow/include/parquet/encryption/encryption.h,sha256=SNFcfN7D83SKO8rTNHwK6uSsvKNFwBiUG1g5yikSp0c,15724
pyarrow/include/parquet/encryption/file_key_material_store.h,sha256=YzAVO3M2H5v5Fz2b_WlmB3GE5wVbMEnFTL3S9XPH6k0,2200
pyarrow/include/parquet/encryption/file_key_unwrapper.h,sha256=pB30St8lGEaEAxNcwnDnlGtATTvc1muMzNOusfgqzT8,4635
pyarrow/include/parquet/encryption/file_key_wrapper.h,sha256=d2W4xICbSRAy7aPe5RKahhPhiJDfvxHY_v_lifq7wqY,3762
pyarrow/include/parquet/encryption/file_system_key_material_store.h,sha256=rbK3gA6KGwbzv-mXDKRr1Ye2o_V7TOlL0lDD0RZ29Zg,3566
pyarrow/include/parquet/encryption/key_encryption_key.h,sha256=0c3ZrRud2vrCu5z513ocyPYxlsP2kg1fQ8m0Jqr701g,2232
pyarrow/include/parquet/encryption/key_material.h,sha256=kPTSIuRFYOnH4BCPIB33zG9hp5D2Ba-5kZVlq3rFnRI,6221
pyarrow/include/parquet/encryption/key_metadata.h,sha256=Pc0nA9LW3Fc9NLMMxz7osbw8si2jSiOVTES-J-9R0y0,4003
pyarrow/include/parquet/encryption/key_toolkit.h,sha256=HPabI8qFnIMgxZYhHgXCzYV0LU1c5yJ16xjUx21I9b0,4577
pyarrow/include/parquet/encryption/kms_client.h,sha256=D34pVHzkCbWqKnPIBYfs6cONxmuYzyLSS9-C52ZFhz0,3151
pyarrow/include/parquet/encryption/kms_client_factory.h,sha256=VZ97CMgDQxx5oZWFGprjXsaM1hZ0wNudPmFU1_lniAc,1293
pyarrow/include/parquet/encryption/local_wrap_kms_client.h,sha256=XZxkEct0-Tv93VDpda9sDou1kp9qkTKMxr36bpVcI8s,3954
pyarrow/include/parquet/encryption/test_encryption_util.h,sha256=zIGeULeTOCU1N-XYHdvIppth5wnnTYEwf2h-OuTcQZQ,5209
pyarrow/include/parquet/encryption/test_in_memory_kms.h,sha256=jYc5WPsrh_wcaaaWcjf23Gbiye3a_bdg2royUfukWEs,3521
pyarrow/include/parquet/encryption/two_level_cache_with_expiration.h,sha256=2kNvixV36bYjUlUJquqnaaOO-NI7e8O3Vm1x-xDPyDA,4818
pyarrow/include/parquet/encryption/type_fwd.h,sha256=dL8snyUwNjhTQE2FQ2dXAUjTboEXhH2JOehQovHfixc,955
pyarrow/include/parquet/exception.h,sha256=iAvCJcT6MgvR-Ty1PYAIdjYgxwQo4huhcajYTfbT4Vk,5519
pyarrow/include/parquet/file_reader.h,sha256=Z1RiOP74Y9tMv7kZ7FWX05IR05qzgSOTRKrY71pLOJE,11185
pyarrow/include/parquet/file_writer.h,sha256=6fK6Mn-MdiQ-J4oo8BTi_eVVVshlffoQiJzFaLRrqco,9343
pyarrow/include/parquet/geospatial/statistics.h,sha256=77ieKxQcY46FB7jgCZhk03fa2G_J2S9NJ8R0_JU-qGs,8892
pyarrow/include/parquet/hasher.h,sha256=HSY1EjPD2xx_dB9HtAg-lXL7hB4j9MDE0cAlR7u0NOc,5227
pyarrow/include/parquet/level_comparison.h,sha256=5z4fUJJPWq9W60l2CsAI7T7E2auGYD7m0fpR5rfLmsw,1306
pyarrow/include/parquet/level_comparison_inc.h,sha256=r20_6Rv5L7UmFGJ68f-JaZ5hLXb87wvZa80hZNQoF-I,2494
pyarrow/include/parquet/level_conversion.h,sha256=OsuqK1xiUnEnOLPKwfm9X-pXTaXRMlDIkj3lwGb2ggI,9432
pyarrow/include/parquet/level_conversion_inc.h,sha256=0r2Gfd_FMidLGFC_a8kgpC9bnUt2-IBbAn9QbQFTrTo,14161
pyarrow/include/parquet/metadata.h,sha256=-4WKrWLuxUQR_lcvqmD5k2v3C1tdGBnoJ20Y89XT_ms,20927
pyarrow/include/parquet/page_index.h,sha256=AqvLT6M81jY13YbpX-s-hOzCVz0AGNkZJ9om3_tp4Vk,17307
pyarrow/include/parquet/parquet_version.h,sha256=cH2igUAJ4xIIr9XI3beMp4K8_1FjHO9zZpSQGf7hHyg,1164
pyarrow/include/parquet/platform.h,sha256=VS0zEUC4d37LQmlQLQZ5aHNaiwRf8QrxixXdWf73m5Q,3898
pyarrow/include/parquet/printer.h,sha256=hXqzwuj5p1oKY2awZkVOq7qXx-Rsoo6QPH1oTE94AHU,1548
pyarrow/include/parquet/properties.h,sha256=WV8E8eOcETi_2m65UIj0i7uY7x8aFZZwnW2XO2_dOM8,55192
pyarrow/include/parquet/schema.h,sha256=CjZh2i9WN5VeoDbLqy7M1AZtopZ43_C9blWG3OT2IfU,18222
pyarrow/include/parquet/size_statistics.h,sha256=Ahr8qyIVre8U_JVRLHL0BbSoYpdJjo2OUeE1UD4I1tw,4327
pyarrow/include/parquet/statistics.h,sha256=0sk7koXslu-KuVC6CsTiFVD1Fu_ZWPD_FLhcXALas_g,15176
pyarrow/include/parquet/stream_reader.h,sha256=1WmN0vYCqTz1Lwb_Di4xPWTE-VbCQQuzZralSpWQm3U,8791
pyarrow/include/parquet/stream_writer.h,sha256=nw_v3nhrL682ozZ2KZKVkHnOsjwexbmBXTV2CKcq4YQ,7505
pyarrow/include/parquet/test_util.h,sha256=2c_2rPZ0TLKhqYLogmkf21yUgLTJcNuLCdpRgkOmV6Y,33440
pyarrow/include/parquet/type_fwd.h,sha256=xmvMW2wSIFvoEZBwVql7hzcPBzOHZnmTlEtIjwp_pzM,3065
pyarrow/include/parquet/types.h,sha256=gmZ9MepaMa4h5mm3N_64cn1n_vz5neyga6F2JLCcugw,27014
pyarrow/include/parquet/windows_compatibility.h,sha256=xIEGHW354URgdIP9A4V303TJL8A1IkCEvp08bMKsHTU,897
pyarrow/include/parquet/windows_fixup.h,sha256=DpyWCywx8YIqouun6BJcgMrHFMTCBgowWdJ1mnJnQ2s,1052
pyarrow/include/parquet/xxhasher.h,sha256=QAa7ZE7S3UFtU_Voz3oi3YclIYhbhviJkafLOYgiuWg,2074
pyarrow/includes/__init__.pxd,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
pyarrow/includes/common.pxd,sha256=me4htiESPFmZok1M8eDXk9OS-7p9Om_1pazNowvttuc,4950
pyarrow/includes/libarrow.pxd,sha256=D6EXJga50Y3zGHIwbcrJaImNwN9vgKush4gosiRxFv0,119829
pyarrow/includes/libarrow_acero.pxd,sha256=c84RdYfIuFWW_36-1RELJsowfQwXhgUxbdC_xKQyFCI,5298
pyarrow/includes/libarrow_cuda.pxd,sha256=0fRcHbCZY_gFdwIXIElnpGvTxeA5xVxZH1-vwZh16SM,4942
pyarrow/includes/libarrow_dataset.pxd,sha256=sr11RcLtmYx3UrrVizsnVzyYoJnmHDHk0ZC-YNXYcj4,17161
pyarrow/includes/libarrow_dataset_parquet.pxd,sha256=PW8g6CD2yjwgwoJgYQ9Ntg2e2v6Vfu4AYL5kiM4sq0Q,4584
pyarrow/includes/libarrow_feather.pxd,sha256=MTJUDQbfKP8Ir700Fobl7xcbjX7WcrsUV4mxFXlfwn0,2140
pyarrow/includes/libarrow_flight.pxd,sha256=HMKsWhyGPtdlRd8Vpj-09PZ2yWPRwD83jGqP-BnnmW8,24665
pyarrow/includes/libarrow_fs.pxd,sha256=PHdXeBI1oD-fLV_POryp-kcyt5084X0DdP3N0aLRmNQ,15379
pyarrow/includes/libarrow_python.pxd,sha256=0kcPYGYdb4duHCUZYRTBkRoWbUosRzs44DYnmu8eNls,11401
pyarrow/includes/libarrow_substrait.pxd,sha256=PtgwVFqsCQBzCHzK-VqhQOdTyU9J4SziQ_lnxRwChfs,4061
pyarrow/includes/libgandiva.pxd,sha256=o28v4Q43dYXQEcwldKO6RYs0tke2U6GZVxQmjq4rAt4,11530
pyarrow/includes/libparquet.pxd,sha256=LKuG_WgGqiTxKUilLBuNdREcH7IUXTLjhAsibLgBbWs,26201
pyarrow/includes/libparquet_encryption.pxd,sha256=q7_hEXbCO_4SWh4BdgV3-Nb-LJVG2YAuccjzJLb03Tg,5932
pyarrow/interchange/__init__.py,sha256=DH0bwbKpdjD1WCW1VinnXEuVLY098uHKkirv7DFc9JM,845
pyarrow/interchange/buffer.py,sha256=NF_GU1uQ6INqHqCwzY6XQQqRxKDh6znEeDHiRqaEIQ0,3359
pyarrow/interchange/column.py,sha256=n9f3mlViqUFOa3Kofdwoo52qC-WYVpyF2YilRsl4uaw,19350
pyarrow/interchange/dataframe.py,sha256=tmSMmBvBAc-ZSUzE8tBNbvQLHuuxLuBkMkK6KYwtS8M,8405
pyarrow/interchange/from_dataframe.py,sha256=JfkP4wuY_9x76H6RDtmsOzs6B6qe-1WS7zxpKeD481s,19709
pyarrow/io.pxi,sha256=gDtFPrwdBrxT88jpNwddg9mM-KSt_XFv333W2IbF3I4,86228
pyarrow/ipc.pxi,sha256=RLAQHAgfyzxg3G8I2EE8_yV9A25llDTAHCv5t77aTGI,42803
pyarrow/ipc.py,sha256=ssicn1x2jRZt2m1nQZ4sB70sa1FB3d8PoqiOnWO-tzg,9603
pyarrow/json.py,sha256=JMEXB5jNZp-DOYmGBrzs2RtwOCLdKCRa3E5kUfH0Ceg,869
pyarrow/jvm.py,sha256=_cyen6Bi-cBUlg-OAb_RTG6nj2W57kMrR1HuAWVcEQs,9577
pyarrow/lib.cpython-39-darwin.so,sha256=WoeRS9KZotFAGvf7kJeI5a4C_JbyPVZT7LeYE2Skb-4,4056232
pyarrow/lib.h,sha256=d-KUXKrlZP_EDmsH5qwVfJS-IPiyHt8qi-KcbJ2BYfQ,4562
pyarrow/lib.pxd,sha256=jpg9xQ1JXQGn5yaK1C8GFE_VfcRRvPaoocsI7MiGO3Q,17584
pyarrow/lib.pyx,sha256=sObLt9zELSeQZkyL373L9yHo2quhly_uAhNIXzaS9fA,6080
pyarrow/lib_api.h,sha256=7GafxqOe7WVEyVic3V4vcEqTRTxehjKSXH6MMyXtqpY,19794
pyarrow/libarrow.2100.dylib,sha256=ibuRsW9R_N2zcZQZ__qRs3I4Ir8UJt8vhf0z9P6zQyw,39591496
pyarrow/libarrow_acero.2100.dylib,sha256=ZbmHNtVQzD3pPq_HftfbRwoJjwf3CQqgSwcgW8-zYNA,2156480
pyarrow/libarrow_compute.2100.dylib,sha256=OADR2whFB5rdodXkBCgRT8fBwJXLAhYeKtKkUBr22Mc,16030000
pyarrow/libarrow_dataset.2100.dylib,sha256=AU9VYb503iVN1hidBBoLZ5X39I5XnWJdyvYqQ8J24XQ,2666672
pyarrow/libarrow_flight.2100.dylib,sha256=MNIW7NwEYD0P3-ozsYkClbvC7P3gFCAWr8FDcf1hQX4,15631024
pyarrow/libarrow_python.2100.0.0.dylib,sha256=33u2hiYrewmVdEqEooP0n8a99w5wuj3AsJDsU6UJIwc,1932720
pyarrow/libarrow_python.2100.dylib,sha256=33u2hiYrewmVdEqEooP0n8a99w5wuj3AsJDsU6UJIwc,1932720
pyarrow/libarrow_python.dylib,sha256=33u2hiYrewmVdEqEooP0n8a99w5wuj3AsJDsU6UJIwc,1932720
pyarrow/libarrow_python_flight.2100.0.0.dylib,sha256=G1ap2YI3hBVvUcwzyk_G_cI-5BTE9NrcS9H7DJIlda8,115176
pyarrow/libarrow_python_flight.2100.dylib,sha256=G1ap2YI3hBVvUcwzyk_G_cI-5BTE9NrcS9H7DJIlda8,115176
pyarrow/libarrow_python_flight.dylib,sha256=G1ap2YI3hBVvUcwzyk_G_cI-5BTE9NrcS9H7DJIlda8,115176
pyarrow/libarrow_python_parquet_encryption.2100.0.0.dylib,sha256=h2720_kc5-foQ0fC3rgx2hz0eABEgwop8KRLfR_ZTI0,78128
pyarrow/libarrow_python_parquet_encryption.2100.dylib,sha256=h2720_kc5-foQ0fC3rgx2hz0eABEgwop8KRLfR_ZTI0,78128
pyarrow/libarrow_python_parquet_encryption.dylib,sha256=h2720_kc5-foQ0fC3rgx2hz0eABEgwop8KRLfR_ZTI0,78128
pyarrow/libarrow_substrait.2100.dylib,sha256=PRKNmnxEm7WiSjpXEZL2QhQgL2xbBk4XVocFMd93LBU,3058352
pyarrow/libparquet.2100.dylib,sha256=4I9GNJwV_q6br5midQARrb6wRoSnVgZ2jPGbwJMGCSc,4330856
pyarrow/memory.pxi,sha256=cZSlJ8Brteb9aWa7VzvCrpnw26wRkplF9KtF2nqtH8M,8806
pyarrow/orc.py,sha256=yDp3vD4gOdOsOBmXKFe24Kq3xO3FDBfIOHvsvzQUkKY,12602
pyarrow/pandas-shim.pxi,sha256=RM38cacj1TlQ5IN79CprIr4AUbbY5GiWdiLsnx0zn3g,8817
pyarrow/pandas_compat.py,sha256=4WqUi9LkHkJxUieTef3N9sGVs9wuYHuSvRPYgGVINF4,45510
pyarrow/parquet/__init__.py,sha256=4W64CbvwvO60tG58nfNtyCwMVCfuPumtu82p-kiGPaE,822
pyarrow/parquet/core.py,sha256=cd9qm4qnZmJYbI2v-IhyjAGdvxBE1QYuFHFiwgjhmvU,93975
pyarrow/parquet/encryption.py,sha256=-XW7Qcbl-jQhpZsR610uQ8-z9ZVE_NL045Jdnp1TZ9M,1153
pyarrow/public-api.pxi,sha256=G6tk0gOWMBoQaQGo4lyp0OLSkqEZqMhB0z27MoQR3rU,14064
pyarrow/scalar.pxi,sha256=64c6PE7JQ7fj-hDeuyqDcjK_vWp_rj-1mgeWzS-b4PU,52223
pyarrow/src/arrow/python/CMakeLists.txt,sha256=D4Ypror_508aAd_juYkrS9Qu2maeirK4QXzwGEZEj0M,855
pyarrow/src/arrow/python/api.h,sha256=2XyYOFAmorPGXrTVYZkiKL67nuH8w1ha6D1reU7K-Vs,1179
pyarrow/src/arrow/python/arrow_to_pandas.cc,sha256=bEEincYIPRAmHCgsOH5Y_r2U1z67yzsxUoBWbL6WdMQ,95586
pyarrow/src/arrow/python/arrow_to_pandas.h,sha256=jUBEUMKXw70oJdMlgkSf6HitaNweQcc7hxI75_C9WSI,5561
pyarrow/src/arrow/python/arrow_to_python_internal.h,sha256=nQXPZTL3xa4Sm-a-Gv-8bpFs-qAOZHkqWmA_m-dSLVw,1740
pyarrow/src/arrow/python/async.h,sha256=C0f8YYmgwBGgDau4xEFsdjukiZB4YvpylETHEZryHOo,2352
pyarrow/src/arrow/python/benchmark.cc,sha256=z6qYRx4qMuNXPaC8fuPJlQd92aosMN85u1aD50R1-UU,1293
pyarrow/src/arrow/python/benchmark.h,sha256=f-kzyMOlPKDse2bcLWhyMrDEMZrG_JHAPpDJgGW0bXU,1192
pyarrow/src/arrow/python/common.cc,sha256=HExYdSkfggXu96XWNXBvK1XukCfPotLVFtk8D9FVaOw,7603
pyarrow/src/arrow/python/common.h,sha256=9IQC__JUqTRXv5MfAqKM6ivMwuQ-uMZKEguGDEODkiA,14389
pyarrow/src/arrow/python/csv.cc,sha256=ql5AY76AqiFksWsrmzSl551k5s9vS8YcmypM2A9rhw8,1803
pyarrow/src/arrow/python/csv.h,sha256=QxU3B-Hv_RsoEcMGS9-1434ugouL2ygC64Lq6FgviNM,1397
pyarrow/src/arrow/python/datetime.cc,sha256=WQoJT6a1ocd3SItRC_q8EA5-4Z9sWNdGFxXen3m0xeU,23013
pyarrow/src/arrow/python/datetime.h,sha256=Bny_THGi2tyUeHxcOuw01O7hNE8B_gave5ABAZQtwTQ,7931
pyarrow/src/arrow/python/decimal.cc,sha256=4CNaLDOWZelTllSIPdIKDlIhUy6AgKJxKG0WmzJyq94,9699
pyarrow/src/arrow/python/decimal.h,sha256=K9ScmzSa7XCuuwpXKnq0GPbRDTuRU5yXfWr9zGtOchc,6362
pyarrow/src/arrow/python/extension_type.cc,sha256=GtlvuJdLQmyrJVDy6_bn1LFzBIbvF9bTB7MWw9k9a7I,6884
pyarrow/src/arrow/python/extension_type.h,sha256=0gzb42y_mbw4fsYs3u8cwPFLBRlG-kkHQLgbvGtrY0U,3181
pyarrow/src/arrow/python/filesystem.cc,sha256=0twavI91TE20Otq5kkVUwnN5sindU_mBWoVAvz1ZMgI,6152
pyarrow/src/arrow/python/filesystem.h,sha256=FG0AcLekqaDf9IQPqKixAfIcY_ZLgIKP5NvvXdtBVUM,5126
pyarrow/src/arrow/python/flight.cc,sha256=tL3nFVTjDlmiuqDOSxf7HODdpqIMUSbSsNc1vaveP1w,14192
pyarrow/src/arrow/python/flight.h,sha256=t4ZD8gWaubbO_mBDv0XZ58TCo1VQl0hzHn1_7w4U64I,14450
pyarrow/src/arrow/python/gdb.cc,sha256=hkATQV6YlpxkBaLfY9_C24XJ6qYSg2T0DfQX4XfpvFY,22556
pyarrow/src/arrow/python/gdb.h,sha256=H-qvM-nU8a_3Z5tk8PvppTwQtBMSZhQKQIVgRAsRfFg,972
pyarrow/src/arrow/python/helpers.cc,sha256=MqeJ1PD1g97tKL9suvE1LWD2cO-MWMwo8-ag3FXAauY,16934
pyarrow/src/arrow/python/helpers.h,sha256=Q3TmwyffFpVYQEp55ZoWTDDhnte7zQKiXylsfbEMCuI,5479
pyarrow/src/arrow/python/inference.cc,sha256=Gm-lOXDzqcbef6gdgCQa5eXPuh8uvYqz9iUjKS2_yO4,24350
pyarrow/src/arrow/python/inference.h,sha256=FUFvB4Zy7V-tueXdmbDcqTeLK4xj5GZEeRW5yhiJlsU,2038
pyarrow/src/arrow/python/io.cc,sha256=k2pgFsCFXaI7naqXcxDtZJJp7K06NnEhsDEoMQvbaxc,11948
pyarrow/src/arrow/python/io.h,sha256=4jGnodpSUlnVqAVh9fWId7H4WldlLPkXyroABpdaW6w,3858
pyarrow/src/arrow/python/ipc.cc,sha256=3D9iMbOFHlhNXX4432wsfbfjWvDryZWgdA0Ak19V_8Q,4472
pyarrow/src/arrow/python/ipc.h,sha256=SZbw6jCCqLiLNCY3k632GmwHeD_r_xrDS0dhqV49VhY,2259
pyarrow/src/arrow/python/iterators.h,sha256=Ugfm3JvetAH0l-oAjjpZfhrUBqRimVMaw4-xusvqLSg,7327
pyarrow/src/arrow/python/numpy_convert.cc,sha256=166BIW7zVTRMKogxLUuhV4e5jOevmonvRtXDydNujgc,21194
pyarrow/src/arrow/python/numpy_convert.h,sha256=y13eHwfe1lJKzadoTr2-GyX6xPsE6Z7FN31s7PN-2Rk,4870
pyarrow/src/arrow/python/numpy_init.cc,sha256=cJKOH946T7VCcB-gVIoGgfbWTrbj3FPkI4TgnsLTf7s,1178
pyarrow/src/arrow/python/numpy_init.h,sha256=FniVHP7W2YBlenoMYhQrODvoqqvDMSls2JANGtNPQts,999
pyarrow/src/arrow/python/numpy_internal.h,sha256=1R_tNINnHYMGNTA5k_BMbGWFlA297f3Yj4ufrhm-yiE,5320
pyarrow/src/arrow/python/numpy_interop.h,sha256=rI6ek8JTOYtjo7gEADSDBS6QuAOHa2A0YQPZ2GeypFw,3418
pyarrow/src/arrow/python/numpy_to_arrow.cc,sha256=uuQo_rewpuc882cuFy-uMmtRLR1iMT3Ew3JystLBXfc,31961
pyarrow/src/arrow/python/numpy_to_arrow.h,sha256=z9KapsuoOSpWILPt9bea7GR4BL6AQ28T6DUO0mSkh3k,2760
pyarrow/src/arrow/python/parquet_encryption.cc,sha256=RNupwaySaVHKX_iCYOPK0yJWkTUpqbrpbCW2duWJ3kU,3567
pyarrow/src/arrow/python/parquet_encryption.h,sha256=Mc8tZ8gIfkH0AckNiIOt6hesP_MVKeKhcytT24ZOLdQ,4861
pyarrow/src/arrow/python/platform.h,sha256=XYS5IqiMUejxN2COzu60Zs8b_wAaGTBw4M-zKVqqs5U,1422
pyarrow/src/arrow/python/pyarrow.cc,sha256=vukhk0aEmqI2QZ54XMwyC3930mJHGUVSSW49GSgifWw,3683
pyarrow/src/arrow/python/pyarrow.h,sha256=TK3BtD9n3QKOQ9dX3LXbQc0hu9alWcufV0O93iQW7B0,2761
pyarrow/src/arrow/python/pyarrow_api.h,sha256=7l0G4-_m9yALYoifsY8Z6qh3HHD0PgkpVSgCn_JaGU4,867
pyarrow/src/arrow/python/pyarrow_lib.h,sha256=-70_Ckj3_0ImlzaXSJOE_d3w9pGM66lXiGPyln9c96Y,863
pyarrow/src/arrow/python/python_test.cc,sha256=vA6FEloSjmu0U8qtO6YL0gfhzbYTFqMuB9QChUxqCks,32319
pyarrow/src/arrow/python/python_test.h,sha256=ea32mM20uHySlygi9MtVxr26O-ydTZHCUQIlxaIMjT4,1195
pyarrow/src/arrow/python/python_to_arrow.cc,sha256=DjnP866Q8RUY9WnE8moCI5nExs5Rni4oYz2kuytZ0-8,47894
pyarrow/src/arrow/python/python_to_arrow.h,sha256=BoVytf6P7PBYXyznchElKZSFvEsFyimB-tLFdw0AUNo,2521
pyarrow/src/arrow/python/type_traits.h,sha256=U7dW4sajVVmjXALsP-B8ZZwI4GgIUG3t-rbp2fwrHWI,10236
pyarrow/src/arrow/python/udf.cc,sha256=tRd7cPh66N5tU4DdO70F8x1FqODcGZ3HtrYjF4BSs9w,29820
pyarrow/src/arrow/python/udf.h,sha256=de3R8PhNJO5lT9oCqRxe8e2_SE3jBpHOkwbNqCrlgjQ,3104
pyarrow/src/arrow/python/util.cc,sha256=Tkgz17XmYFzVybvQ9Vo9LChVo98R5BlbjqCNMHk86DM,1852
pyarrow/src/arrow/python/util.h,sha256=sP2CEGG7NNqwjrEeNKB-VFqkKxn5P2Tsc0jPUMXQuLg,1732
pyarrow/src/arrow/python/vendored/CMakeLists.txt,sha256=02XvDJAdKiajCEBOmnMKBpmzbRU7FPkNdlNXtw0-A24,837
pyarrow/src/arrow/python/vendored/pythoncapi_compat.h,sha256=bzMnlHTCfjk5DQRIxwytunYh5aQxU3iSElaaDyNnAY8,40900
pyarrow/src/arrow/python/visibility.h,sha256=hwJw5sGrWJckQkNaAuLe4Tf-VDjQbXknyzNOVgZI3FI,1381
pyarrow/substrait.py,sha256=yOMgCb-A5XW9t2DKgutRydDHpFvQkiNNq6ZIu0hIUB8,1230
pyarrow/table.pxi,sha256=UOT_A8-Ugat92lFF5C1Qyk-h2PA7F938fP5L-XXP0TE,208558
pyarrow/tensor.pxi,sha256=vWs4_2-xeSkFsvL0VMVciIntRWiaH-6QxZyx8j00v1U,43661
pyarrow/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
pyarrow/tests/arrow_16597.py,sha256=DNb41h9E3ITGvAJJu86i5SfsKrwstQJ0E5gT_bpTS_k,1354
pyarrow/tests/arrow_39313.py,sha256=0pyBixoX38fldTPO1Vwshi_H0XBACrz8esYoL4o71KI,1431
pyarrow/tests/arrow_7980.py,sha256=tZKb_tRLfxHaosDk9Yu2GLEsJjMaruXD5CKhbK_6Hq8,1094
pyarrow/tests/bound_function_visit_strings.pyx,sha256=vDEFoNYR8BWNkCntKDuBUT8sXNRBex_5G2bFKogr1Bs,2026
pyarrow/tests/conftest.py,sha256=yhbHWUARnpRonmxVtXkPv7YBOr_K-B0DRWfkrW4wnCE,10386
pyarrow/tests/data/feather/v0.17.0.version.2-compression.lz4.feather,sha256=qzcc7Bo4OWBXYsyyKdDJwdTRstMqB1Zz0GiGYtndBnE,594
pyarrow/tests/data/orc/README.md,sha256=_4X5XszZqQtWAVEz5N1Va4VyyayGQgNDKrcdMX2Ib4s,932
pyarrow/tests/data/orc/TestOrcFile.emptyFile.jsn.gz,sha256=xLjAXd-3scx3DCyeAsmxTO3dv1cj9KRvYopKe5rQNiI,50
pyarrow/tests/data/orc/TestOrcFile.emptyFile.orc,sha256=zj0579dQBXhF7JuB-ZphkmQ81ybLo6Ca4zPV4HXoImY,523
pyarrow/tests/data/orc/TestOrcFile.test1.jsn.gz,sha256=kLxmwMVHtfzpHqBztFjfY_PTCloaXpfHq9DDDszb8Wk,323
pyarrow/tests/data/orc/TestOrcFile.test1.orc,sha256=A4JxgMCffTkz9-XT1QT1tg2TlYZRRz1g7iIMmqzovqA,1711
pyarrow/tests/data/orc/TestOrcFile.testDate1900.jsn.gz,sha256=oWf7eBR3ZtOA91OTvdeQJYos1an56msGsJwhGOan3lo,182453
pyarrow/tests/data/orc/TestOrcFile.testDate1900.orc,sha256=nYsVYhUGGOL80gHj37si_vX0dh8QhIMSeU4sHjNideM,30941
pyarrow/tests/data/orc/decimal.jsn.gz,sha256=kTEyYdPDAASFUX8Niyry5mRDF-Y-LsrhSAjbu453mvA,19313
pyarrow/tests/data/orc/decimal.orc,sha256=W5cV2WdLy4OrSTnd_Qv5ntphG4TcB-MyG4UpRFwSxJY,16337
pyarrow/tests/data/parquet/v0.7.1.all-named-index.parquet,sha256=YPGUXtw-TsOPbiNDieZHobNp3or7nHhAxJGjmIDAyqE,3948
pyarrow/tests/data/parquet/v0.7.1.column-metadata-handling.parquet,sha256=7sebZgpfdcP37QksT3FhDL6vOA9gR6GBaq44NCVtOYw,2012
pyarrow/tests/data/parquet/v0.7.1.parquet,sha256=vmdzhIzpBbmRkq3Gjww7KqurfSFNtQuSpSIDeQVmqys,4372
pyarrow/tests/data/parquet/v0.7.1.some-named-index.parquet,sha256=VGgSjqihCRtdBxlUcfP5s3BSR7aUQKukW-bGgJLf_HY,4008
pyarrow/tests/extensions.pyx,sha256=UVfIEnGYy2SDiYkMW6jp6zTo1tvO7gUREH4OGEWjU2k,3046
pyarrow/tests/interchange/__init__.py,sha256=9hdXHABrVpkbpjZgUft39kOFL2xSGeG4GEua0Hmelus,785
pyarrow/tests/interchange/test_conversion.py,sha256=23e5tpKBL-ekA5uWpM6-f6HVPF937Hnzfune0Ty9moo,18609
pyarrow/tests/interchange/test_interchange_spec.py,sha256=AwqwD8b2gV1f5HERtzG6UtOIshFGafUI_9CQOVLPnho,9381
pyarrow/tests/pandas_examples.py,sha256=SDPL87Ea997nGi7afOqlceq6wH4Bh5IR5S0fHZV3Acs,5107
pyarrow/tests/pandas_threaded_import.py,sha256=b_ubLr5dj4dWJht9552qc3S3Yt3fQQgaUH6208oZvHg,1429
pyarrow/tests/parquet/__init__.py,sha256=dKsXU9M-sJyz2wYIuqwsKM9meOlK_qY6qhmQzIvEpCE,931
pyarrow/tests/parquet/common.py,sha256=C0lWLo0iHK16wIs5q00DSZ3jYQV9UosA3Ss4m2l6odE,5982
pyarrow/tests/parquet/conftest.py,sha256=kREy8Y_kM5Th1b4QU0PTNrREFyMp43e_2UGB15kkNZg,3030
pyarrow/tests/parquet/encryption.py,sha256=Oi3QbixApvWGoGImiW7PAjR28cTQqlRXZKMI3O7E4UY,2521
pyarrow/tests/parquet/test_basic.py,sha256=M-50kACpidK_Rv6UNGHgdXeyUXCBMsnLM7HRA0JNiKU,36768
pyarrow/tests/parquet/test_compliant_nested_type.py,sha256=Lz7tCPrSpv9GrKPMS-eu1LehsCTwz7KdUdCYJ8tF8dE,3901
pyarrow/tests/parquet/test_data_types.py,sha256=Y_RqQBfDgrxQnOc0IjuHA7ueIOmotH8CMkmqQ4prz9I,19319
pyarrow/tests/parquet/test_dataset.py,sha256=Z27xb0WtZXqB2X8rD9sW8v-o8spykw1DhQyRPj9V1nY,42126
pyarrow/tests/parquet/test_datetime.py,sha256=A3ZaRj88u0IrlhCNp2KY_A8txrb7y2pKPgEVvI7e7bU,16398
pyarrow/tests/parquet/test_encryption.py,sha256=jnXgTgzgD9a1tORikWgVAvFl2apaPpjoCLKZwlYknTU,24003
pyarrow/tests/parquet/test_metadata.py,sha256=340mr8FxLTVfLMSDDDRm8WWblCzRz7FD77mr4BoJodg,27732
pyarrow/tests/parquet/test_pandas.py,sha256=18Td_AiMXyWPZi3sPAvLejElYaZrzm8ZAhO3IEZRzqc,22674
pyarrow/tests/parquet/test_parquet_file.py,sha256=Ly2toAojWxYmJ6SZ2cmI7erKYA1My3ChYZ5-bQmZhxk,13671
pyarrow/tests/parquet/test_parquet_writer.py,sha256=izbITRtjn115elcx_jYdQ8Q5VuXL5OuYmXaAyyUhAFc,15915
pyarrow/tests/pyarrow_cython_example.pyx,sha256=fx6zT1bUb2-cDnwKoG71K3ozpmrNJ53kKQHHJTExGz8,2115
pyarrow/tests/read_record_batch.py,sha256=9Y0X0h03hUXwOKZz7jBBZSwgIrjxT-FkWIw6pu38Frc,953
pyarrow/tests/strategies.py,sha256=ksEfxpT9Bn89V5pj9Xt2WgIAdDhqTMAqfrONozhBXSs,14226
pyarrow/tests/test_acero.py,sha256=t1TIMZJ9zfCYu9V_Mg_i6SvtoIXFo2GZbxDJh3Luu60,18009
pyarrow/tests/test_adhoc_memory_leak.py,sha256=Pn4PcIbOBRtSJuz9Ar_ocubco0QOMZ-eAE9Bs7Wp4mA,1453
pyarrow/tests/test_array.py,sha256=vMKvA3geqQJXvN3NqgMdnPlPnaSvoPzWxSKkULqpCYM,143641
pyarrow/tests/test_builder.py,sha256=zNEcslLwyb40oYbG7lInQcI81QHMKDOzi1zthw1Je7c,2803
pyarrow/tests/test_cffi.py,sha256=Fbs1dFCxdnvXYLgO5oaxm_h8KV3vefE9jc3nI1JZNxw,26385
pyarrow/tests/test_compute.py,sha256=Qd-LSGqJX-3f4qsYVDcnJNeSLmn16YM96FwfMvoaEXk,153698
pyarrow/tests/test_convert_builtin.py,sha256=Hh1Ti08oBBPXfEQ-196rH_LjHYGvuzyxVDu54lKsGm0,80992
pyarrow/tests/test_cpp_internals.py,sha256=Xg4CUB6zohQkcYG64Lj_Uf2BscI27Vv0JC_CqNkDQuE,2006
pyarrow/tests/test_csv.py,sha256=-Lna8TcZ3ZVcomgXN-SIYx9wH9mskEvSpzl9gzwQRYw,77291
pyarrow/tests/test_cuda.py,sha256=81lyEjUUd2UWbeFW-JJECYJ9UZKAXOTys55uMd2i0so,36153
pyarrow/tests/test_cuda_numba_interop.py,sha256=iHP_FE4sWbsKwNNXRcYnVozp3Wd1o0Mg6BDymx710G4,8794
pyarrow/tests/test_cython.py,sha256=2rFR9ZNwF5pL1LP0lvyuwankmpwUac78iqcijxYB1DI,7040
pyarrow/tests/test_dataset.py,sha256=zyEObGF3rezyuqLr-pTZ8G_BjkgAhi9aFm4zGS277SE,214453
pyarrow/tests/test_dataset_encryption.py,sha256=mA8ipIlOBSA4eKc6xnRz-IFyM7fu_kIQ5FV2r4vE2rs,7593
pyarrow/tests/test_deprecations.py,sha256=W_rneq4jC6zqCNoGhBDf1F28Q-0LHI7YKLgtsbV6LHM,891
pyarrow/tests/test_device.py,sha256=qe9Wiwo-XVazt9pdxyqQJUz6fNR0jTs9CHiyaoppNA4,2550
pyarrow/tests/test_dlpack.py,sha256=sdubFfaU6ZCjZFoj-vbqnT-Yf79kI2ZBOe9Qi_J0Vfk,5974
pyarrow/tests/test_exec_plan.py,sha256=pjOkSaWeqjN6celKxUEH3tBGXLh8kKbmSSsvKOWsbQQ,10096
pyarrow/tests/test_extension_type.py,sha256=q-Y3BI469ePC0aLG5LUSTObdeMqkeQtnD-oRLBx_9kM,67567
pyarrow/tests/test_feather.py,sha256=QMFIyOh2bGwatHq3gE1OZWIgIdfJ2i5UjhNTN0DVK5U,25616
pyarrow/tests/test_flight.py,sha256=fdQhBokN08BugEDaepw2-edKegYbHuX-q_BNkLNc4rE,92833
pyarrow/tests/test_flight_async.py,sha256=kP2wIrUz1HIbrr4CE0SxSg4Zc56ta3xX6EdibAI2yVM,2848
pyarrow/tests/test_fs.py,sha256=tTqf-XPhwNUfr54A_pKsB9SxqL5kSbUgyDptm6h2gIs,70148
pyarrow/tests/test_gandiva.py,sha256=AEf9ln-j5MmIMQ0JTQPhnZwbNh82ynSURsWPaKaNing,15623
pyarrow/tests/test_gdb.py,sha256=OJzMfZtev3YOKJBm2QxnE-q-9-exy2JLhxpiVhY3T_0,44938
pyarrow/tests/test_io.py,sha256=fBEyAu9zTMHu9ovgwOwv7kEslYhY38sd0GNR78d-ECg,63736
pyarrow/tests/test_ipc.py,sha256=-lwLoZuz_OXoZS1RjkoDGTd2xNV32JUyxzwzmGgjIFw,43392
pyarrow/tests/test_json.py,sha256=yTOKNdiCcJxmPCAiUfNojAOqsytj_w9uZ0YPJd3yNWU,23003
pyarrow/tests/test_jvm.py,sha256=QLRHeFx8Gbz1UywlnDi2akV6DBtqGHReQEuNl2vSbcE,15475
pyarrow/tests/test_memory.py,sha256=j8fs5HqguRobzbcHSvyRzd0ICT7IMeonMvcs147DEd4,9860
pyarrow/tests/test_misc.py,sha256=pFgce82k4zCww6dBslyRveZPiNywh2ywymHpsftEqcU,7310
pyarrow/tests/test_orc.py,sha256=opg9F0fQ16GB0OqTx5aDjaKk4E8dQTZwxDYq4ZzJ778,21184
pyarrow/tests/test_pandas.py,sha256=fbcWY0h8D1Dug-GI4-8NZKZDTQ8toLZz3FOYXxhAYNc,193111
pyarrow/tests/test_scalars.py,sha256=TZwWm6woQD24AmZ0Vl0b-4no_SA6mEgGJyurZ8FyFUM,29577
pyarrow/tests/test_schema.py,sha256=fWM9KXCQF4m1EOQll-b89RjXDNhi2O_jzpsAZWrCqJM,22620
pyarrow/tests/test_sparse_tensor.py,sha256=bGO7Es4D65zmGNRN013G9VUCQ_v-mPUb90sAk--3yyw,17666
pyarrow/tests/test_strategies.py,sha256=ZPJiWvj26qQPsR9aZCF-VUsT6PyWPrJsr6q2oXJshSs,1829
pyarrow/tests/test_substrait.py,sha256=rsLPNlDPDAxZ1OIu-LAINSD4q_UZJhOvtTzPfXjrWM8,32223
pyarrow/tests/test_table.py,sha256=YM6MEid_hosbxzbplUp-s6fs9TPAY_ro1FjjowYuHS8,123867
pyarrow/tests/test_tensor.py,sha256=LYSEYGUjtdnsbL0WAir9jFindo-r0bLySiDA1uAXL8E,6643
pyarrow/tests/test_types.py,sha256=phlyXhj7mT6BAaRDJ2O0QSrROk-LGNY4fSwuOJL24zU,43822
pyarrow/tests/test_udf.py,sha256=WA9E5skUqh7uMr_zH3rQ11LRx0SK2G3WO8HjVHGWyQY,29792
pyarrow/tests/test_util.py,sha256=ozTlooHBMOP3nbX5b3dG2aanrXwxXHx1giicm0QQyPM,5030
pyarrow/tests/test_without_numpy.py,sha256=ysbB-jML318I04ViQT4Ok7iMg1cI-NU8kguPu-FTSl4,1855
pyarrow/tests/util.py,sha256=eY_YFC0iZsIRax1mY3jN-kYrzOCa6OkhpJaRBUaxnLc,13483
pyarrow/tests/wsgi_examples.py,sha256=vQIDb5989sRVLsELw-fRHhfX-dE96sTl5J2lEuEKup8,1348
pyarrow/types.pxi,sha256=0GOLfnO-6MankBVN6I9XNOngcsT-8NYnQdrv1dBCrTI,162477
pyarrow/types.py,sha256=M-fGpHFc-OyTDT_IiG_9l5PO2H7A2iynbiSxk6Zv8bM,7492
pyarrow/util.py,sha256=ARlwfZFK6sMKGNG2V9HcG0EC0LGHHImB9AMhoWUI4nI,8640
pyarrow/vendored/__init__.py,sha256=9hdXHABrVpkbpjZgUft39kOFL2xSGeG4GEua0Hmelus,785
pyarrow/vendored/docscrape.py,sha256=phTjwuzoO5hB88QerZk3uGu9c5OrZwjFzI7vEIIbCUQ,22975
pyarrow/vendored/version.py,sha256=5-Vo4Q3kPJrm1DSGusnMlTxuA8ynI4hAryApBd6MnpQ,14345
