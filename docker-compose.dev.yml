version: '3.8'

services:
  # Simplified API service for development
  sensei-api:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    ports:
      - "8000:8000"
    environment:
      - SENSEI_ENVIRONMENT=development
      - SENSEI_DEBUG=true
      - SENSEI_LOG_LEVEL=INFO
      - GOOGLE_APPLICATION_CREDENTIALS=/app/credentials/sensei-ai-service-account.json
    volumes:
      - ./config:/app/config:ro
      - ./credentials:/app/credentials:ro
      - ./monitoring_output:/app/monitoring_output
    networks:
      - sensei-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  sensei-network:
    driver: bridge
