#!/usr/bin/env python3
"""
BigQuery production training script for Sensei AI Suite.

Connects to real BigQuery data and trains models with production pipeline.
"""

import argparse
import json
import os
import sys
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional, List

import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.metrics import accuracy_score, roc_auc_score, classification_report
import xgboost as xgb
import catboost as cb

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

# Import configuration and utilities
try:
    from config.settings import settings, ServerCapacity
    from src.sensei.utils.logging import log_info, log_error
except ImportError:
    print("Warning: Could not import settings, using defaults")
    settings = None
    
    # Define ServerCapacity locally if import fails
    from enum import Enum
    class ServerCapacity(str, Enum):
        SMALL = "small"
        MEDIUM = "medium"
        LARGE = "large"
        XLARGE = "xlarge"
    
    # Simple logging fallback
    def log_info(message: str, **kwargs):
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        extra = " ".join([f"{k}={v}" for k, v in kwargs.items()])
        print(f"[{timestamp}] INFO: {message} {extra}")
    
    def log_error(message: str, **kwargs):
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        extra = " ".join([f"{k}={v}" for k, v in kwargs.items()])
        print(f"[{timestamp}] ERROR: {message} {extra}")


def load_bigquery_data(query: str, max_samples: Optional[int] = None) -> pd.DataFrame:
    """
    Load data from BigQuery.
    
    Args:
        query: SQL query to execute
        max_samples: Maximum number of samples to load
        
    Returns:
        DataFrame with loaded data
    """
    try:
        from google.cloud import bigquery
        
        # Initialize BigQuery client
        project_id = os.getenv("GCP_PROJECT_ID", "datalake-sensei")
        client = bigquery.Client(project=project_id)
        
        # Add LIMIT if max_samples specified
        if max_samples:
            if "LIMIT" not in query.upper():
                query += f" LIMIT {max_samples}"
        
        log_info("Executing BigQuery query", project=project_id, max_samples=max_samples)
        
        # Execute query
        df = client.query(query).to_dataframe()
        
        log_info("Data loaded from BigQuery", rows=len(df), columns=len(df.columns))
        return df
        
    except ImportError:
        log_error("google-cloud-bigquery not installed")
        raise
    except Exception as e:
        log_error(f"BigQuery error: {e}")
        raise


def get_conversion_query(max_samples: Optional[int] = None) -> str:
    """Get SQL query for conversion model data using real serving layer tables."""
    query = """
    SELECT
        -- Contact identifiers
        c.id_contact,
        c.email,
        c.nom,
        c.prenom,

        -- Behavioral features (with null handling)
        COALESCE(SAFE_CAST(c.hubspotscore AS FLOAT64), 0) as hubspotscore,
        COALESCE(SAFE_CAST(c.num_unique_conversion_events AS FLOAT64), 0) as num_unique_conversion_events,
        COALESCE(SAFE_CAST(c.num_conversion_events AS FLOAT64), 0) as num_conversion_events,
        COALESCE(SAFE_CAST(c.num_associated_deals AS FLOAT64), 0) as num_associated_deals,
        COALESCE(SAFE_CAST(c.hs_analytics_num_page_views AS FLOAT64), 0) as hs_analytics_num_page_views,
        COALESCE(SAFE_CAST(c.nombre_de_transactions_terminees AS FLOAT64), 0) as nombre_de_transactions_terminees,

        -- Target variable: has completed transactions
        CASE
            WHEN COALESCE(SAFE_CAST(c.nombre_de_transactions_terminees AS INT64), 0) > 0 THEN 1
            ELSE 0
        END as y_converted,

        -- Additional features
        c.statut_du_lead,
        c.relationship_status,
        CASE
            WHEN c.lead_traite = 'Oui' THEN 1
            WHEN c.lead_traite = 'Non' THEN 0
            ELSE 0
        END as lead_traite,
        c.ip_country,
        c.dt_creation_contact,
        c.dt_modification_contact

    FROM `datalake-sensei.serving_layer.vw_dim_contact` c
    WHERE
        c.dt_creation_contact >= '2023-01-01'  -- Plus de données historiques
        AND c.email IS NOT NULL
        AND c.statut_du_lead IS NOT NULL
    ORDER BY RAND()  -- Échantillonnage aléatoire pour avoir des conversions
    """

    if max_samples:
        query += f" LIMIT {max_samples}"

    return query


def get_channel_query(max_samples: Optional[int] = None) -> str:
    """Get SQL query for channel model data using real serving layer tables (simplified)."""
    query = """
    SELECT
        -- Contact identifiers
        c.id_contact,
        c.email,
        c.nom,
        c.prenom,

        -- Contact features
        CASE WHEN c.numero_telephone IS NOT NULL THEN 1 ELSE 0 END as has_phone,
        CASE WHEN c.email IS NOT NULL THEN 1 ELSE 0 END as has_email,

        -- Behavioral features
        COALESCE(SAFE_CAST(c.hubspotscore AS FLOAT64), 0) as hubspotscore,
        COALESCE(SAFE_CAST(c.num_associated_deals AS FLOAT64), 0) as num_associated_deals,
        COALESCE(SAFE_CAST(c.hs_analytics_num_page_views AS FLOAT64), 0) as hs_analytics_num_page_views,

        -- Response patterns based on deal success
        CASE
            WHEN COALESCE(SAFE_CAST(c.nombre_de_transactions_terminees AS INT64), 0) > 0 THEN 1.0
            WHEN COALESCE(SAFE_CAST(c.num_associated_deals AS INT64), 0) > 0 THEN 0.5
            ELSE 0.0
        END as response_success_rate,

        -- Additional features
        c.statut_du_lead,
        c.ip_country,

        -- Target: optimal channel based on contact characteristics and success
        CASE
            WHEN COALESCE(SAFE_CAST(c.nombre_de_transactions_terminees AS INT64), 0) > 0
                 AND c.numero_telephone IS NOT NULL THEN 'call_successful'
            WHEN COALESCE(SAFE_CAST(c.nombre_de_transactions_terminees AS INT64), 0) > 0
                 AND c.email IS NOT NULL THEN 'email_successful'
            WHEN COALESCE(SAFE_CAST(c.hubspotscore AS FLOAT64), 0) > 50
                 AND c.numero_telephone IS NOT NULL THEN 'call_high_score'
            WHEN COALESCE(SAFE_CAST(c.hubspotscore AS FLOAT64), 0) > 50
                 AND c.email IS NOT NULL THEN 'email_high_score'
            WHEN c.numero_telephone IS NOT NULL THEN 'call_general'
            ELSE 'email_general'
        END as channel_timing_optimal

    FROM `datalake-sensei.serving_layer.vw_dim_contact` c
    WHERE
        c.dt_creation_contact >= '2023-01-01'
        AND (c.numero_telephone IS NOT NULL OR c.email IS NOT NULL)
        AND c.statut_du_lead IS NOT NULL
    ORDER BY RAND()
    """

    if max_samples:
        query += f" LIMIT {max_samples}"

    return query


def get_nlp_query(max_samples: Optional[int] = None) -> str:
    """Get SQL query for NLP model data using real serving layer tables."""
    query = """
    SELECT
        -- Transcript identifiers
        t.callId,
        t.speakerId,

        -- Transcript content
        t.content as transcript_text,
        t.startTime,
        t.endTime,

        -- Call metadata
        c.startDate as call_date,
        c.duration as call_duration,
        c.title as call_title,

        -- Call summary and scores
        s.summary_content,
        s.aiSummary_content,
        s.score_appel_prequai,
        s.score_ice_breaker,
        s.score_phase_decouverte,
        s.score_consolider_vente,
        s.score_visio_vente,

        -- Contact context
        contact.name as contact_name,
        contact.email as contact_email,

        -- Success indicator
        CASE
            WHEN s.score_consolider_vente > 7 THEN 1
            ELSE 0
        END as y_successful_call

    FROM `datalake-sensei.serving_layer.vw_dim_modjo_transcript` t
    JOIN `datalake-sensei.serving_layer.vw_fact_modjo_call` c
        ON t.callId = c.callId
    LEFT JOIN `datalake-sensei.serving_layer.vw_dim_modjo_call_summary` s
        ON t.callId = s.callId
    LEFT JOIN `datalake-sensei.serving_layer.vw_dim_modjo_contact` contact
        ON c.contactId = contact.contactId
    WHERE
        c.startDate >= '2024-01-01'
        AND t.content IS NOT NULL
        AND LENGTH(t.content) > 50
        AND c.duration > 60  -- At least 1 minute call
    ORDER BY c.startDate DESC
    """

    if max_samples:
        query += f" LIMIT {max_samples}"

    return query


def prepare_conversion_features(data: pd.DataFrame) -> tuple:
    """Prepare features for conversion prediction using real data."""
    log_info("Preparing conversion features", samples=len(data))

    # Select numerical features from real data
    numerical_features = [
        'hubspotscore', 'num_unique_conversion_events', 'num_conversion_events',
        'num_associated_deals', 'hs_analytics_num_page_views', 'nombre_de_transactions_terminees'
    ]

    # Create features dataframe
    features = pd.DataFrame()

    # Add numerical features
    for col in numerical_features:
        if col in data.columns:
            features[col] = pd.to_numeric(data[col], errors='coerce').fillna(0)

    # Add categorical features (one-hot encoded)
    categorical_features = ['statut_du_lead', 'relationship_status', 'ip_country']

    for cat_col in categorical_features:
        if cat_col in data.columns:
            # Limit categories to top 10 to avoid too many features
            top_categories = data[cat_col].value_counts().head(10).index
            data_filtered = data[cat_col].where(data[cat_col].isin(top_categories), 'other')
            dummies = pd.get_dummies(data_filtered, prefix=cat_col, dummy_na=True)
            features = pd.concat([features, dummies], axis=1)

    # Add boolean features
    if 'lead_traite' in data.columns:
        features['lead_traite'] = pd.to_numeric(data['lead_traite'], errors='coerce').fillna(0)

    # Add derived features
    features['has_transactions'] = (data['nombre_de_transactions_terminees'] > 0).astype(int)
    features['has_deals'] = (data['num_associated_deals'] > 0).astype(int)
    features['has_page_views'] = (data['hs_analytics_num_page_views'] > 0).astype(int)

    # Target
    target = data['y_converted'].fillna(0)

    log_info("Conversion features prepared",
             features=len(features.columns),
             samples=len(features),
             positive_rate=target.mean())

    return features, target


def prepare_channel_features(data: pd.DataFrame) -> tuple:
    """Prepare features for channel optimization using real data (simplified)."""
    log_info("Preparing channel features", samples=len(data))

    # Select features from real data
    feature_cols = [
        'has_phone', 'has_email', 'hubspotscore', 'num_associated_deals',
        'hs_analytics_num_page_views', 'response_success_rate'
    ]

    # Create features dataframe
    features = pd.DataFrame()

    # Add numerical features
    for col in feature_cols:
        if col in data.columns:
            features[col] = pd.to_numeric(data[col], errors='coerce').fillna(0)

    # Add categorical features (one-hot encoded)
    categorical_features = ['statut_du_lead', 'ip_country']

    for cat_col in categorical_features:
        if cat_col in data.columns:
            # Limit categories to top 5 to avoid too many features
            top_categories = data[cat_col].value_counts().head(5).index
            data_filtered = data[cat_col].where(data[cat_col].isin(top_categories), 'other')
            dummies = pd.get_dummies(data_filtered, prefix=cat_col, dummy_na=True)
            features = pd.concat([features, dummies], axis=1)

    # Add derived features
    features['has_both_channels'] = (features['has_phone'] * features['has_email'])
    features['high_engagement'] = (features['hs_analytics_num_page_views'] > 10).astype(int)

    # Target - encode as numerical labels
    from sklearn.preprocessing import LabelEncoder
    target_raw = data['channel_timing_optimal']

    # Encode string labels to numbers
    label_encoder = LabelEncoder()
    target = label_encoder.fit_transform(target_raw)

    log_info("Channel features prepared",
             features=len(features.columns),
             samples=len(features),
             classes=len(label_encoder.classes_),
             class_names=list(label_encoder.classes_))

    return features, target, label_encoder


def prepare_nlp_features(data: pd.DataFrame) -> tuple:
    """Prepare features for NLP model using real transcript data."""
    log_info("Preparing NLP features", samples=len(data))

    # Text features from transcripts
    features = pd.DataFrame()

    # Basic text statistics
    features['transcript_length'] = data['transcript_text'].str.len().fillna(0)
    features['word_count'] = data['transcript_text'].str.split().str.len().fillna(0)
    features['call_duration'] = pd.to_numeric(data['call_duration'], errors='coerce').fillna(0)

    # Modjo scores (if available)
    score_cols = ['score_appel_prequai', 'score_ice_breaker', 'score_phase_decouverte',
                  'score_consolider_vente', 'score_visio_vente']

    for col in score_cols:
        if col in data.columns:
            features[col] = pd.to_numeric(data[col], errors='coerce').fillna(0)

    # Derived features
    features['words_per_minute'] = features['word_count'] / (features['call_duration'] / 60 + 1)
    features['has_summary'] = data['summary_content'].notna().astype(int)
    features['has_ai_summary'] = data['aiSummary_content'].notna().astype(int)

    # Simple text analysis
    if 'transcript_text' in data.columns:
        # Count positive/negative words (simple approach)
        positive_words = ['excellent', 'parfait', 'super', 'génial', 'formidable', 'intéressé', 'oui', 'accord']
        negative_words = ['non', 'problème', 'difficile', 'impossible', 'refus', 'pas intéressé']

        features['positive_words'] = data['transcript_text'].str.lower().str.count('|'.join(positive_words)).fillna(0)
        features['negative_words'] = data['transcript_text'].str.lower().str.count('|'.join(negative_words)).fillna(0)
        features['sentiment_ratio'] = features['positive_words'] / (features['negative_words'] + 1)

    # Target: successful call based on scores or conversion
    target = data['y_successful_call'].fillna(0)

    log_info("NLP features prepared",
             features=len(features.columns),
             samples=len(features),
             positive_rate=target.mean())

    return features, target


def train_conversion_model_bigquery(X: pd.DataFrame, y: pd.Series, capacity: ServerCapacity) -> Dict[str, Any]:
    """Train conversion model with BigQuery data."""
    log_info("Training conversion model on BigQuery data", 
             samples=len(X), features=len(X.columns), capacity=capacity.value)
    
    start_time = time.time()
    
    # Check class distribution
    positive_rate = y.mean()
    log_info("Class distribution", positive_rate=positive_rate, negative_rate=1-positive_rate)
    
    if positive_rate < 0.01 or positive_rate > 0.99:
        log_error("Extreme class imbalance detected", positive_rate=positive_rate)
    
    # Split data with stratification
    X_train, X_val, y_train, y_val = train_test_split(
        X, y, test_size=0.2, random_state=42, stratify=y
    )
    
    # Configure model for real data
    if capacity == ServerCapacity.SMALL:
        params = {
            'n_estimators': 100,
            'max_depth': 4,
            'learning_rate': 0.05,
            'reg_alpha': 1.0,
            'reg_lambda': 1.0,
            'scale_pos_weight': (1 - positive_rate) / positive_rate  # Handle imbalance
        }
    elif capacity == ServerCapacity.MEDIUM:
        params = {
            'n_estimators': 200,
            'max_depth': 6,
            'learning_rate': 0.03,
            'reg_alpha': 0.5,
            'reg_lambda': 0.5,
            'scale_pos_weight': (1 - positive_rate) / positive_rate
        }
    else:  # LARGE or XLARGE
        params = {
            'n_estimators': 500,
            'max_depth': 8,
            'learning_rate': 0.01,
            'reg_alpha': 0.1,
            'reg_lambda': 0.1,
            'scale_pos_weight': (1 - positive_rate) / positive_rate
        }
    
    # Train model
    model = xgb.XGBClassifier(
        **params,
        subsample=0.8,
        colsample_bytree=0.8,
        random_state=42,
        eval_metric='auc'
    )
    
    model.fit(
        X_train, y_train,
        eval_set=[(X_val, y_val)],
        early_stopping_rounds=50,
        verbose=False
    )
    
    # Calculate metrics
    train_pred = model.predict_proba(X_train)[:, 1]
    val_pred = model.predict_proba(X_val)[:, 1]
    
    train_auc = roc_auc_score(y_train, train_pred)
    val_auc = roc_auc_score(y_val, val_pred)
    
    # Cross-validation
    cv_scores = cross_val_score(model, X, y, cv=3, scoring='roc_auc')
    cv_auc = cv_scores.mean()
    
    training_time = time.time() - start_time
    
    metrics = {
        'training_time_seconds': training_time,
        'train_size': len(X_train),
        'val_size': len(X_val),
        'feature_count': len(X.columns),
        'positive_rate': float(positive_rate),
        'train_auc': float(train_auc),
        'val_auc': float(val_auc),
        'cv_auc': float(cv_auc),
        'cv_std': float(cv_scores.std()),
        'overfitting_gap': float(train_auc - val_auc),
        'n_estimators_used': model.best_iteration if hasattr(model, 'best_iteration') else params['n_estimators'],
        'model_params': params,
        'data_source': 'bigquery'
    }
    
    log_info("Conversion model trained on BigQuery data", **{k: v for k, v in metrics.items() if isinstance(v, (int, float, str))})
    return metrics, model


def train_channel_model_bigquery(X: pd.DataFrame, y: pd.Series, capacity: ServerCapacity) -> Dict[str, Any]:
    """Train channel model with BigQuery data."""
    log_info("Training channel model on BigQuery data",
             samples=len(X), features=len(X.columns), capacity=capacity.value)

    start_time = time.time()

    # Check class distribution
    import numpy as np
    unique, counts = np.unique(y, return_counts=True)
    class_counts = dict(zip(unique, counts))
    log_info("Channel class distribution", **{f"class_{k}": int(v) for k, v in class_counts.items()})

    # Split data
    X_train, X_val, y_train, y_val = train_test_split(
        X, y, test_size=0.2, random_state=42, stratify=y
    )

    # Configure model for real data (multiclass)
    if capacity == ServerCapacity.SMALL:
        params = {
            'n_estimators': 100,
            'max_depth': 4,
            'learning_rate': 0.05,
            'reg_alpha': 1.0,
            'reg_lambda': 1.0
        }
    elif capacity == ServerCapacity.MEDIUM:
        params = {
            'n_estimators': 200,
            'max_depth': 6,
            'learning_rate': 0.03,
            'reg_alpha': 0.5,
            'reg_lambda': 0.5
        }
    else:  # LARGE or XLARGE
        params = {
            'n_estimators': 500,
            'max_depth': 8,
            'learning_rate': 0.01,
            'reg_alpha': 0.1,
            'reg_lambda': 0.1
        }

    # Train model (binary or multiclass)
    num_classes = len(class_counts)

    if num_classes == 2:
        # Binary classification
        model = xgb.XGBClassifier(
            **params,
            subsample=0.8,
            colsample_bytree=0.8,
            random_state=42,
            eval_metric='logloss',
            objective='binary:logistic'
        )
    else:
        # Multiclass classification
        model = xgb.XGBClassifier(
            **params,
            subsample=0.8,
            colsample_bytree=0.8,
            random_state=42,
            eval_metric='mlogloss',
            objective='multi:softprob',
            num_class=num_classes
        )

    model.fit(
        X_train, y_train,
        eval_set=[(X_val, y_val)],
        early_stopping_rounds=50,
        verbose=False
    )

    # Calculate metrics
    from sklearn.metrics import accuracy_score
    train_pred = model.predict(X_train)
    val_pred = model.predict(X_val)

    train_acc = accuracy_score(y_train, train_pred)
    val_acc = accuracy_score(y_val, val_pred)

    # Cross-validation
    cv_scores = cross_val_score(model, X, y, cv=3, scoring='accuracy')
    cv_acc = cv_scores.mean()

    training_time = time.time() - start_time

    metrics = {
        'training_time_seconds': training_time,
        'train_size': len(X_train),
        'val_size': len(X_val),
        'feature_count': len(X.columns),
        'num_classes': len(class_counts),
        'train_accuracy': float(train_acc),
        'val_accuracy': float(val_acc),
        'cv_accuracy': float(cv_acc),
        'cv_std': float(cv_scores.std()),
        'overfitting_gap': float(train_acc - val_acc),
        'n_estimators_used': model.best_iteration if hasattr(model, 'best_iteration') else params['n_estimators'],
        'model_params': params,
        'data_source': 'bigquery',
        'class_distribution': {f"class_{k}": int(v) for k, v in class_counts.items()}
    }

    log_info("Channel model trained on BigQuery data", **{k: v for k, v in metrics.items() if isinstance(v, (int, float, str))})
    return metrics, model


def save_model_and_results(model_name: str, model, metrics: Dict[str, Any], capacity: ServerCapacity) -> None:
    """Save model and results to registry."""
    models_dir = Path("models")
    models_dir.mkdir(exist_ok=True)
    
    # Create model-specific directory
    model_dir = models_dir / model_name
    model_dir.mkdir(exist_ok=True)
    
    # Generate version
    version = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Save model
    model_path = model_dir / f"{model_name}_bigquery_{version}.pkl"
    import joblib
    joblib.dump(model, model_path)
    
    # Load existing registry
    registry_path = models_dir / "registry.json"
    if registry_path.exists():
        with open(registry_path, 'r') as f:
            registry = json.load(f)
    else:
        registry = {}
    
    # Add new results
    if model_name not in registry:
        registry[model_name] = []
    
    registry[model_name].append({
        "version": version,
        "path": str(model_path),
        "metrics": metrics,
        "created_at": datetime.now().isoformat(),
        "server_capacity": capacity.value,
        "type": "bigquery_production",
        "data_source": "bigquery"
    })
    
    # Save registry
    with open(registry_path, 'w') as f:
        json.dump(registry, f, indent=2)
    
    log_info(f"BigQuery model saved", model=model_name, version=version, path=str(model_path))


def main():
    """Main training function for BigQuery data."""
    parser = argparse.ArgumentParser(description="BigQuery production training for Sensei AI Suite")
    parser.add_argument("--capacity", choices=["small", "medium", "large", "xlarge"], 
                       default="medium", help="Server capacity")
    parser.add_argument("--models", nargs="+", choices=["conversion", "channel", "nlp"], 
                       default=["conversion", "channel"], help="Models to train")
    parser.add_argument("--max-samples", type=int, help="Maximum samples to load")
    
    args = parser.parse_args()
    
    # Set environment variables
    os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = str(Path("credentials/sensei-ai-service-account.json").absolute())
    os.environ["GCP_PROJECT_ID"] = "datalake-sensei"
    os.environ["ENVIRONMENT"] = "production"
    
    # Get capacity
    try:
        capacity = ServerCapacity(args.capacity)
    except ValueError:
        capacity = ServerCapacity.MEDIUM
    
    log_info("Starting BigQuery production training", 
             capacity=capacity.value, models=args.models, max_samples=args.max_samples)
    
    results = {}
    
    # Train conversion model
    if "conversion" in args.models:
        try:
            log_info("Loading conversion data from BigQuery")
            query = get_conversion_query(args.max_samples)
            data = load_bigquery_data(query)
            
            X, y = prepare_conversion_features(data)
            metrics, model = train_conversion_model_bigquery(X, y, capacity)
            save_model_and_results("conversion", model, metrics, capacity)
            results["conversion"] = {"status": "success", "metrics": metrics}
            
        except Exception as e:
            log_error(f"Conversion model failed: {e}")
            results["conversion"] = {"status": "error", "error": str(e)}
    
    # Train channel model
    if "channel" in args.models:
        try:
            log_info("Loading channel data from BigQuery")
            query = get_channel_query(args.max_samples)
            data = load_bigquery_data(query)

            X, y, label_encoder = prepare_channel_features(data)
            metrics, model = train_channel_model_bigquery(X, y, capacity)

            # Add label encoder info to metrics
            metrics['label_encoder_classes'] = list(label_encoder.classes_)

            save_model_and_results("channel", model, metrics, capacity)
            results["channel"] = {"status": "success", "metrics": metrics}

        except Exception as e:
            log_error(f"Channel model failed: {e}")
            results["channel"] = {"status": "error", "error": str(e)}
    
    # Print summary
    print("\n" + "="*80)
    print("BIGQUERY PRODUCTION TRAINING SUMMARY")
    print("="*80)
    print(f"Capacity: {capacity.value}")
    print(f"Data source: BigQuery (datalake-sensei)")
    print("-"*80)
    
    for model_name, result in results.items():
        status = result.get("status", "unknown")
        print(f"{model_name:15} | {status.upper():10}")
        
        if status == "success":
            metrics = result.get("metrics", {})
            if "val_auc" in metrics:
                print(f"{'':15} | AUC: {metrics['val_auc']:.3f} (gap: {metrics.get('overfitting_gap', 0):.3f})")
                print(f"{'':15} | Positive rate: {metrics.get('positive_rate', 0):.3f}")
            print(f"{'':15} | Training time: {metrics.get('training_time_seconds', 0):.1f}s")
    
    print("="*80)
    
    # Return success if all models trained successfully
    success = all(r.get("status") == "success" for r in results.values())
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
