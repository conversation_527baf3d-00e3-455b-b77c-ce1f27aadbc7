#!/usr/bin/env python3
"""
Test BigQuery connection and data access for Sensei AI Suite.
"""

import os
import sys
from pathlib import Path
from datetime import datetime

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

try:
    from src.sensei.utils.logging import log_info, log_error
except ImportError:
    def log_info(message: str, **kwargs):
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        extra = " ".join([f"{k}={v}" for k, v in kwargs.items()])
        print(f"[{timestamp}] INFO: {message} {extra}")
    
    def log_error(message: str, **kwargs):
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        extra = " ".join([f"{k}={v}" for k, v in kwargs.items()])
        print(f"[{timestamp}] ERROR: {message} {extra}")


def test_bigquery_connection():
    """Test BigQuery connection and permissions."""
    try:
        from google.cloud import bigquery
        
        # Set credentials
        credentials_path = Path("credentials/sensei-ai-service-account.json").absolute()
        os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = str(credentials_path)
        
        # Initialize client
        project_id = "datalake-sensei"
        client = bigquery.Client(project=project_id)
        
        log_info("BigQuery client initialized", project=project_id)
        
        # Test basic connection
        datasets = list(client.list_datasets())
        log_info("BigQuery connection successful", datasets_count=len(datasets))
        
        # List available datasets
        for dataset in datasets:
            log_info("Available dataset", dataset_id=dataset.dataset_id)
        
        return True, client
        
    except ImportError:
        log_error("google-cloud-bigquery not installed")
        return False, None
    except Exception as e:
        log_error(f"BigQuery connection failed: {e}")
        return False, None


def test_data_access(client):
    """Test access to specific tables."""
    try:
        # Test contact data from serving layer
        contact_query = """
        SELECT COUNT(*) as total_contacts
        FROM `datalake-sensei.serving_layer.vw_dim_contact`
        WHERE dt_creation_contact >= '2024-01-01'
        """
        
        log_info("Testing contact data access")
        result = client.query(contact_query).to_dataframe()
        contact_count = result.iloc[0]['total_contacts']
        log_info("Contact data accessible", total_contacts=contact_count)
        
        # Test sample contact data from serving layer
        sample_query = """
        SELECT
            id_contact,
            email,
            nom,
            prenom,
            hubspotscore,
            statut_du_lead,
            nombre_de_transactions_terminees,
            dt_creation_contact
        FROM `datalake-sensei.serving_layer.vw_dim_contact`
        WHERE dt_creation_contact >= '2024-01-01'
        AND email IS NOT NULL
        LIMIT 5
        """
        
        sample_data = client.query(sample_query).to_dataframe()
        log_info("Sample contact data", rows=len(sample_data), columns=len(sample_data.columns))
        print("\nSample contact data:")
        print(sample_data.head())
        
        # Test transcript data from serving layer
        transcript_query = """
        SELECT COUNT(*) as total_transcripts
        FROM `datalake-sensei.serving_layer.vw_dim_modjo_transcript`
        """
        
        log_info("Testing transcript data access")
        result = client.query(transcript_query).to_dataframe()
        transcript_count = result.iloc[0]['total_transcripts']
        log_info("Transcript data accessible", total_transcripts=transcript_count)
        
        return True
        
    except Exception as e:
        log_error(f"Data access test failed: {e}")
        return False


def test_data_quality(client):
    """Test data quality and completeness."""
    try:
        # Test serving layer data quality
        quality_query = """
        SELECT
            COUNT(*) as total_rows,
            COUNT(DISTINCT id_contact) as unique_contacts,
            COUNT(email) as with_email,
            COUNT(hubspotscore) as with_score,
            COUNT(statut_du_lead) as with_status,
            COUNT(CASE WHEN CAST(nombre_de_transactions_terminees AS INT64) > 0 THEN 1 END) as converted_customers,
            AVG(CASE WHEN CAST(nombre_de_transactions_terminees AS INT64) > 0 THEN 1.0 ELSE 0.0 END) as conversion_rate
        FROM `datalake-sensei.serving_layer.vw_dim_contact`
        WHERE dt_creation_contact >= '2024-01-01'
        """
        
        log_info("Testing data quality")
        result = client.query(quality_query).to_dataframe()
        
        stats = result.iloc[0]
        log_info("Data quality stats",
                total_rows=int(stats['total_rows']),
                unique_contacts=int(stats['unique_contacts']),
                with_email=int(stats['with_email']),
                with_score=int(stats['with_score']),
                with_status=int(stats['with_status']),
                converted_customers=int(stats['converted_customers']),
                conversion_rate=float(stats['conversion_rate']))

        # Check data quality thresholds
        quality_checks = {
            "sufficient_data": stats['total_rows'] >= 100,
            "email_completeness": stats['with_email'] / stats['total_rows'] >= 0.5,
            "score_availability": stats['with_score'] / stats['total_rows'] >= 0.3,
            "status_data": stats['with_status'] / stats['total_rows'] >= 0.5,
            "reasonable_conversion_rate": 0.0001 <= stats['conversion_rate'] <= 0.5
        }
        
        log_info("Quality checks", **quality_checks)
        
        all_passed = all(quality_checks.values())
        if all_passed:
            log_info("All data quality checks passed")
        else:
            log_error("Some data quality checks failed", failed_checks=[k for k, v in quality_checks.items() if not v])
        
        return all_passed
        
    except Exception as e:
        log_error(f"Data quality test failed: {e}")
        return False


def main():
    """Main test function."""
    print("="*80)
    print("SENSEI AI SUITE - BIGQUERY CONNECTION TEST")
    print("="*80)
    
    # Test connection
    log_info("Testing BigQuery connection")
    connection_ok, client = test_bigquery_connection()
    
    if not connection_ok:
        log_error("BigQuery connection failed - aborting tests")
        return 1
    
    # Test data access
    log_info("Testing data access")
    access_ok = test_data_access(client)
    
    if not access_ok:
        log_error("Data access failed")
        return 1
    
    # Test data quality
    log_info("Testing data quality")
    quality_ok = test_data_quality(client)
    
    # Summary
    print("\n" + "="*80)
    print("TEST SUMMARY")
    print("="*80)
    print(f"✅ BigQuery Connection: {'PASSED' if connection_ok else 'FAILED'}")
    print(f"✅ Data Access: {'PASSED' if access_ok else 'FAILED'}")
    print(f"✅ Data Quality: {'PASSED' if quality_ok else 'FAILED'}")
    print("="*80)
    
    if connection_ok and access_ok and quality_ok:
        print("🎉 ALL TESTS PASSED - READY FOR PRODUCTION TRAINING!")
        return 0
    else:
        print("❌ SOME TESTS FAILED - CHECK CONFIGURATION")
        return 1


if __name__ == "__main__":
    sys.exit(main())
