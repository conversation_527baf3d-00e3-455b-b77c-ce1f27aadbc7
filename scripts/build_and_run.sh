#!/bin/bash

# Build and Run Script for Sensei AI v1.0
# Optimized for development and production deployment

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="sensei-ai"
IMAGE_NAME="sensei-ai:v1.0"
CONTAINER_NAME="sensei-api"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed or not in PATH"
        exit 1
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose is not installed or not in PATH"
        exit 1
    fi
    
    # Check credentials
    if [ ! -f "credentials/sensei-ai-service-account.json" ]; then
        log_warning "BigQuery credentials not found at credentials/sensei-ai-service-account.json"
        log_warning "Some features may not work without proper credentials"
    fi
    
    log_success "Prerequisites check completed"
}

# Clean up existing containers and images
cleanup() {
    log_info "Cleaning up existing containers and images..."
    
    # Stop and remove container if exists
    if docker ps -a --format 'table {{.Names}}' | grep -q "^${CONTAINER_NAME}$"; then
        log_info "Stopping existing container: ${CONTAINER_NAME}"
        docker stop ${CONTAINER_NAME} || true
        docker rm ${CONTAINER_NAME} || true
    fi
    
    # Remove old image if exists
    if docker images --format 'table {{.Repository}}:{{.Tag}}' | grep -q "^${IMAGE_NAME}$"; then
        log_info "Removing existing image: ${IMAGE_NAME}"
        docker rmi ${IMAGE_NAME} || true
    fi
    
    log_success "Cleanup completed"
}

# Build Docker image
build_image() {
    log_info "Building Docker image: ${IMAGE_NAME}"
    
    # Build with optimized settings
    docker build \
        --tag ${IMAGE_NAME} \
        --target production \
        --build-arg BUILD_ENV=production \
        --build-arg VERSION=1.0.0 \
        . || {
        log_error "Docker build failed"
        exit 1
    }
    
    log_success "Docker image built successfully: ${IMAGE_NAME}"
}

# Run with Docker Compose (recommended)
run_with_compose() {
    log_info "Starting services with Docker Compose..."
    
    # Use development compose file for simplicity
    docker-compose -f docker-compose.dev.yml up -d || {
        log_error "Docker Compose startup failed"
        exit 1
    }
    
    log_success "Services started with Docker Compose"
    log_info "API available at: http://localhost:8000"
    log_info "Health check: http://localhost:8000/health"
    log_info "API docs: http://localhost:8000/docs"
}

# Run single container (alternative)
run_single_container() {
    log_info "Starting single container: ${CONTAINER_NAME}"
    
    docker run -d \
        --name ${CONTAINER_NAME} \
        --port 8000:8000 \
        --volume "$(pwd)/config:/app/config:ro" \
        --volume "$(pwd)/credentials:/app/credentials:ro" \
        --volume "$(pwd)/monitoring_output:/app/monitoring_output" \
        --env SENSEI_ENVIRONMENT=development \
        --env SENSEI_DEBUG=true \
        --env GOOGLE_APPLICATION_CREDENTIALS=/app/credentials/sensei-ai-service-account.json \
        ${IMAGE_NAME} || {
        log_error "Container startup failed"
        exit 1
    }
    
    log_success "Container started: ${CONTAINER_NAME}"
}

# Wait for service to be ready
wait_for_service() {
    log_info "Waiting for service to be ready..."
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f -s http://localhost:8000/health > /dev/null 2>&1; then
            log_success "Service is ready!"
            return 0
        fi
        
        log_info "Attempt $attempt/$max_attempts - Service not ready yet, waiting..."
        sleep 2
        ((attempt++))
    done
    
    log_error "Service failed to start within expected time"
    return 1
}

# Run tests
run_tests() {
    log_info "Running integration tests..."
    
    # Set credentials for tests
    export GOOGLE_APPLICATION_CREDENTIALS="$(pwd)/credentials/sensei-ai-service-account.json"
    
    # Run consolidated integration tests
    python -m pytest tests/test_integration.py -v -s || {
        log_warning "Some tests failed - check logs for details"
        return 1
    }
    
    log_success "Integration tests completed"
}

# Show logs
show_logs() {
    log_info "Showing service logs..."
    docker-compose -f docker-compose.dev.yml logs -f sensei-api
}

# Stop services
stop_services() {
    log_info "Stopping services..."
    docker-compose -f docker-compose.dev.yml down || true
    docker stop ${CONTAINER_NAME} 2>/dev/null || true
    log_success "Services stopped"
}

# Main execution
main() {
    local command=${1:-"all"}
    
    case $command in
        "check")
            check_prerequisites
            ;;
        "clean")
            cleanup
            ;;
        "build")
            check_prerequisites
            cleanup
            build_image
            ;;
        "run")
            run_with_compose
            wait_for_service
            ;;
        "test")
            run_tests
            ;;
        "logs")
            show_logs
            ;;
        "stop")
            stop_services
            ;;
        "all")
            log_info "🚀 Starting complete build and run process for Sensei AI v1.0"
            check_prerequisites
            cleanup
            build_image
            run_with_compose
            wait_for_service
            log_success "🎉 Sensei AI v1.0 is ready!"
            log_info "📊 API Documentation: http://localhost:8000/docs"
            log_info "🔍 Health Check: http://localhost:8000/health"
            log_info "📝 View logs: ./scripts/build_and_run.sh logs"
            log_info "🛑 Stop services: ./scripts/build_and_run.sh stop"
            ;;
        *)
            echo "Usage: $0 {check|clean|build|run|test|logs|stop|all}"
            echo ""
            echo "Commands:"
            echo "  check  - Check prerequisites"
            echo "  clean  - Clean up existing containers/images"
            echo "  build  - Build Docker image"
            echo "  run    - Run services with Docker Compose"
            echo "  test   - Run integration tests"
            echo "  logs   - Show service logs"
            echo "  stop   - Stop all services"
            echo "  all    - Complete build and run process (default)"
            exit 1
            ;;
    esac
}

# Execute main function with all arguments
main "$@"
