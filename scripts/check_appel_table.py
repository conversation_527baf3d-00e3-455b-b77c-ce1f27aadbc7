#!/usr/bin/env python3
"""
Check the vw_dim_appel table structure.
"""

import os
import sys
from pathlib import Path

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

def check_appel_table():
    """Check the appel table structure."""
    try:
        from google.cloud import bigquery
        
        # Set credentials
        credentials_path = Path("credentials/sensei-ai-service-account.json").absolute()
        os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = str(credentials_path)
        
        # Initialize client
        project_id = "datalake-sensei"
        client = bigquery.Client(project=project_id)
        
        # Check if table exists
        try:
            table_ref = client.dataset("serving_layer").table("vw_dim_appel")
            table = client.get_table(table_ref)
            
            print(f"Table vw_dim_appel exists!")
            print(f"Rows: {table.num_rows:,}")
            print(f"Columns: {len(table.schema)}")
            print("\nSchema:")
            for field in table.schema:
                print(f"  {field.name}: {field.field_type}")
            
            # Sample data
            query = """
            SELECT *
            FROM `datalake-sensei.serving_layer.vw_dim_appel`
            LIMIT 3
            """
            
            result = client.query(query).to_dataframe()
            print(f"\nSample data:")
            print(result.head())
            
        except Exception as e:
            print(f"Table vw_dim_appel error: {e}")
            
            # Try to find similar tables
            print("\nLooking for similar tables...")
            dataset_ref = client.dataset("serving_layer")
            tables = list(client.list_tables(dataset_ref))
            
            appel_tables = [t for t in tables if 'appel' in t.table_id.lower() or 'call' in t.table_id.lower()]
            print(f"Found {len(appel_tables)} call-related tables:")
            for table in appel_tables:
                print(f"  - {table.table_id}")
        
        return True
        
    except Exception as e:
        print(f"Error: {e}")
        return False


if __name__ == "__main__":
    check_appel_table()
