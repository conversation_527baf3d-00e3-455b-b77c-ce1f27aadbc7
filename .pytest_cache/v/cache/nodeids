["tests/test_advanced_data_analysis.py::TestAdvancedDataAnalysis::test_comprehensive_data_discovery", "tests/test_advanced_ml_pipeline.py::TestAdvancedMLPipeline::test_advanced_ml_training", "tests/test_advanced_ml_v2.py::TestAdvancedMLv2::test_complete_v2_pipeline", "tests/test_api_mock.py::TestAPIMock::test_conversion_prediction_mock", "tests/test_api_mock.py::TestAPIMock::test_error_handling", "tests/test_api_mock.py::TestAPIMock::test_health_endpoint", "tests/test_api_mock.py::TestAPIMock::test_input_validation", "tests/test_api_mock.py::TestAPIMock::test_performance_basic", "tests/test_api_mock.py::TestDataProcessingMock::test_data_quality_checks_mock", "tests/test_api_mock.py::TestDataProcessingMock::test_feature_engineering_mock", "tests/test_api_real_conditions.py::TestAPIRealConditions::test_batch_processing_performance", "tests/test_api_real_conditions.py::TestAPIRealConditions::test_concurrent_requests_performance", "tests/test_api_real_conditions.py::TestAPIRealConditions::test_conversion_prediction_realistic_data", "tests/test_api_real_conditions.py::TestAPIRealConditions::test_error_handling_and_validation", "tests/test_api_real_conditions.py::TestAPIRealConditions::test_health_endpoints_comprehensive", "tests/test_api_real_integration.py::TestAPIRealIntegration::test_end_to_end_integration", "tests/test_basic_functionality.py::TestBasicFunctionality::test_catboost_functionality", "tests/test_basic_functionality.py::TestBasicFunctionality::test_core_imports", "tests/test_basic_functionality.py::TestBasicFunctionality::test_data_processing_basics", "tests/test_basic_functionality.py::TestBasicFunctionality::test_fastapi_basic", "tests/test_basic_functionality.py::TestBasicFunctionality::test_hyperparameter_optimization_basic", "tests/test_basic_functionality.py::TestBasicFunctionality::test_lightgbm_functionality", "tests/test_basic_functionality.py::TestBasicFunctionality::test_ml_model_basics", "tests/test_basic_functionality.py::TestBasicFunctionality::test_python_version", "tests/test_basic_functionality.py::TestBasicFunctionality::test_sensei_imports", "tests/test_bigquery_basic.py::TestBigQueryBasic::test_basic_connection", "tests/test_bigquery_basic.py::TestBigQueryBasic::test_credentials_validation", "tests/test_bigquery_basic.py::TestBigQueryBasic::test_information_schema_access", "tests/test_bigquery_basic.py::TestBigQueryBasic::test_project_datasets", "tests/test_bigquery_basic.py::TestBigQueryBasic::test_query_performance_basic", "tests/test_bigquery_basic.py::TestBigQueryBasic::test_serving_layer_exists", "tests/test_bigquery_basic.py::TestBigQueryBasic::test_serving_layer_tables_simple", "tests/test_bigquery_basic.py::TestBigQueryBasic::test_table_sample_access", "tests/test_bigquery_direct.py::TestBigQueryDirect::test_bigquery_direct_connection", "tests/test_bigquery_direct.py::TestBigQueryDirect::test_data_quality_basic", "tests/test_bigquery_direct.py::TestBigQueryDirect::test_data_types_analysis", "tests/test_bigquery_direct.py::TestBigQueryDirect::test_query_performance_direct", "tests/test_bigquery_direct.py::TestBigQueryDirect::test_sample_data_access", "tests/test_bigquery_direct.py::TestBigQueryDirect::test_serving_layer_tables", "tests/test_e2e_validation.py::TestDataConnectivity::test_bigquery_connection", "tests/test_e2e_validation.py::TestDataConnectivity::test_required_views_existence", "tests/test_e2e_validation.py::TestDataQuality::test_data_distribution_analysis", "tests/test_e2e_validation.py::TestDataQuality::test_data_leakage_detection", "tests/test_e2e_validation.py::TestEnvironmentValidation::test_configuration_loading", "tests/test_e2e_validation.py::TestEnvironmentValidation::test_dependencies_availability", "tests/test_e2e_validation.py::TestEnvironmentValidation::test_project_structure", "tests/test_e2e_validation.py::TestEnvironmentValidation::test_python_imports", "tests/test_e2e_validation.py::TestFeaturePipeline::test_feature_extraction_sample", "tests/test_e2e_validation.py::TestFeaturePipeline::test_feature_store_initialization", "tests/test_e2e_validation.py::TestModelValidation::test_model_registry_initialization", "tests/test_e2e_validation.py::TestModelValidation::test_model_training_small_sample", "tests/test_final_validation.py::TestFinalValidation::test_system_integration_final", "tests/test_hyperparameter_optimization.py::TestHyperparameterOptimization::test_automated_hyperparameter_search", "tests/test_hyperparameter_optimization.py::TestHyperparameterOptimization::test_conversion_model_hyperparameter_tuning", "tests/test_hyperparameter_optimization.py::TestHyperparameterOptimization::test_optuna_integration", "tests/test_hyperparameter_optimization.py::TestHyperparameterOptimization::test_overfitting_detection", "tests/test_mock_data_validation.py::TestMockDataValidation::test_data_quality_checks", "tests/test_mock_data_validation.py::TestMockDataValidation::test_hyperparameter_optimization_mock", "tests/test_mock_data_validation.py::TestMockDataValidation::test_mock_data_generation", "tests/test_mock_data_validation.py::TestMockDataValidation::test_model_training_with_mock_data", "tests/test_real_bigquery_connection.py::TestRealBigQueryConnection::test_bigquery_client_creation", "tests/test_real_bigquery_connection.py::TestRealBigQueryConnection::test_bigquery_connection", "tests/test_real_bigquery_connection.py::TestRealBigQueryConnection::test_conversion_data_access", "tests/test_real_bigquery_connection.py::TestRealBigQueryConnection::test_data_quality_check", "tests/test_real_bigquery_connection.py::TestRealBigQueryConnection::test_hubspot_data_access", "tests/test_real_bigquery_connection.py::TestRealBigQueryConnection::test_query_performance", "tests/test_real_bigquery_connection.py::TestRealBigQueryConnection::test_serving_layer_access", "tests/test_real_data_pipeline.py::TestRealDataPipeline::test_call_data_exploration", "tests/test_real_data_pipeline.py::TestRealDataPipeline::test_contact_data_exploration", "tests/test_real_data_pipeline.py::TestRealDataPipeline::test_data_quality_real", "tests/test_real_data_pipeline.py::TestRealDataPipeline::test_email_engagement_data", "tests/test_real_data_pipeline.py::TestRealDataPipeline::test_feature_engineering_basic", "tests/test_real_data_pipeline.py::TestRealDataPipeline::test_leads_data_exploration", "tests/test_real_data_pipeline.py::TestRealDataPipeline::test_ml_model_with_real_data", "tests/test_real_ml_pipeline.py::TestRealMLPipeline::test_data_extraction_corrected", "tests/test_real_ml_pipeline.py::TestRealMLPipeline::test_data_processing_pipeline", "tests/test_real_ml_pipeline.py::TestRealMLPipeline::test_ml_model_training_real", "tests/test_simple_integration.py::TestSimpleIntegration::test_basic_imports", "tests/test_simple_integration.py::TestSimpleIntegration::test_config_loading", "tests/test_simple_integration.py::TestSimpleIntegration::test_data_processing_pipeline", "tests/test_simple_integration.py::TestSimpleIntegration::test_data_quality_validation", "tests/test_simple_integration.py::TestSimpleIntegration::test_feature_importance_analysis", "tests/test_simple_integration.py::TestSimpleIntegration::test_logging_setup", "tests/test_simple_integration.py::TestSimpleIntegration::test_ml_pipeline_basic", "tests/test_simple_integration.py::TestSimpleIntegration::test_model_validation_basic", "tests/test_simple_integration.py::TestSimpleIntegration::test_simple_api_creation", "tests/test_v2_features_standalone.py::TestV2FeaturesStandalone::test_complete_v2_pipeline_standalone"]