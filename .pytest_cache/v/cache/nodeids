["tests/test_api_mock.py::TestAPIMock::test_conversion_prediction_mock", "tests/test_api_mock.py::TestAPIMock::test_error_handling", "tests/test_api_mock.py::TestAPIMock::test_health_endpoint", "tests/test_api_mock.py::TestAPIMock::test_input_validation", "tests/test_api_mock.py::TestAPIMock::test_performance_basic", "tests/test_api_mock.py::TestDataProcessingMock::test_data_quality_checks_mock", "tests/test_api_mock.py::TestDataProcessingMock::test_feature_engineering_mock", "tests/test_basic_functionality.py::TestBasicFunctionality::test_catboost_functionality", "tests/test_basic_functionality.py::TestBasicFunctionality::test_core_imports", "tests/test_basic_functionality.py::TestBasicFunctionality::test_data_processing_basics", "tests/test_basic_functionality.py::TestBasicFunctionality::test_fastapi_basic", "tests/test_basic_functionality.py::TestBasicFunctionality::test_hyperparameter_optimization_basic", "tests/test_basic_functionality.py::TestBasicFunctionality::test_lightgbm_functionality", "tests/test_basic_functionality.py::TestBasicFunctionality::test_ml_model_basics", "tests/test_basic_functionality.py::TestBasicFunctionality::test_python_version", "tests/test_basic_functionality.py::TestBasicFunctionality::test_sensei_imports", "tests/test_mock_data_validation.py::TestMockDataValidation::test_data_quality_checks", "tests/test_mock_data_validation.py::TestMockDataValidation::test_hyperparameter_optimization_mock", "tests/test_mock_data_validation.py::TestMockDataValidation::test_mock_data_generation", "tests/test_mock_data_validation.py::TestMockDataValidation::test_model_training_with_mock_data"]