{"tests/test_api_mock.py::TestAPIMock::test_health_endpoint": true, "tests/test_api_mock.py::TestAPIMock::test_conversion_prediction_mock": true, "tests/test_api_mock.py::TestAPIMock::test_input_validation": true, "tests/test_api_mock.py::TestAPIMock::test_error_handling": true, "tests/test_api_mock.py::TestAPIMock::test_performance_basic": true, "tests/test_api.py": true, "tests/test_real_bigquery_connection.py::TestRealBigQueryConnection::test_bigquery_client_creation": true, "tests/test_real_bigquery_connection.py::TestRealBigQueryConnection::test_bigquery_connection": true, "tests/test_real_bigquery_connection.py::TestRealBigQueryConnection::test_serving_layer_access": true, "tests/test_real_bigquery_connection.py::TestRealBigQueryConnection::test_conversion_data_access": true, "tests/test_real_bigquery_connection.py::TestRealBigQueryConnection::test_data_quality_check": true, "tests/test_real_bigquery_connection.py::TestRealBigQueryConnection::test_query_performance": true, "tests/test_bigquery_direct.py::TestBigQueryDirect::test_bigquery_direct_connection": true, "tests/test_bigquery_direct.py::TestBigQueryDirect::test_serving_layer_tables": true, "tests/test_bigquery_direct.py::TestBigQueryDirect::test_sample_data_access": true, "tests/test_bigquery_direct.py::TestBigQueryDirect::test_query_performance_direct": true, "tests/test_bigquery_direct.py::TestBigQueryDirect::test_data_quality_basic": true, "tests/test_real_data_pipeline.py::TestRealDataPipeline::test_feature_engineering_basic": true, "tests/test_api_real_integration.py::TestAPIRealIntegration::test_end_to_end_integration": true, "tests/test_advanced_data_analysis.py::TestAdvancedDataAnalysis::test_comprehensive_data_discovery": true, "tests/test_advanced_ml_v2.py::TestAdvancedMLv2::test_complete_v2_pipeline": true, "tests/test_v2_features_standalone.py::TestV2FeaturesStandalone::test_complete_v2_pipeline_standalone": true}