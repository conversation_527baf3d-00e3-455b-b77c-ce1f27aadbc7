"""
Test BigQuery basique - connectivité et accès simple.

Test l'accès BigQuery avec des requêtes simples pour valider la connectivité.
"""

import pytest
import os
import pandas as pd
from pathlib import Path
import logging

logger = logging.getLogger(__name__)


class TestBigQueryBasic:
    """Test BigQuery basique."""
    
    def setup_method(self):
        """Setup pour chaque test."""
        # Vérifier les credentials
        credentials_path = os.getenv('GOOGLE_APPLICATION_CREDENTIALS')
        if not credentials_path or not os.path.exists(credentials_path):
            pytest.skip("Credentials BigQuery non configurés")
        
        logger.info(f"Credentials: {credentials_path}")
    
    def test_basic_connection(self):
        """Test de connexion de base."""
        try:
            from google.cloud import bigquery
            
            # Créer le client
            client = bigquery.Client(project="datalake-sensei")
            
            # Test très simple
            query = "SELECT 1 as test_value"
            query_job = client.query(query)
            results = query_job.result()
            
            # Convertir en DataFrame
            df = results.to_dataframe()
            
            assert len(df) == 1, "Résultat incorrect"
            assert df.iloc[0]['test_value'] == 1, "Valeur incorrecte"
            
            logger.info("✓ Connexion BigQuery de base validée")
            
        except Exception as e:
            logger.error(f"Erreur connexion de base: {e}")
            pytest.fail(f"Impossible de se connecter à BigQuery: {e}")
    
    def test_project_datasets(self):
        """Test d'accès aux datasets du projet."""
        try:
            from google.cloud import bigquery
            
            client = bigquery.Client(project="datalake-sensei")
            
            # Lister les datasets
            datasets = list(client.list_datasets())
            
            dataset_names = [dataset.dataset_id for dataset in datasets]
            
            logger.info(f"✓ Datasets trouvés: {len(dataset_names)}")
            for name in dataset_names:
                logger.info(f"  - {name}")
            
            # Vérifier qu'on a au moins un dataset
            assert len(dataset_names) > 0, "Aucun dataset trouvé"
            
            return dataset_names
            
        except Exception as e:
            logger.error(f"Erreur accès datasets: {e}")
            pytest.fail(f"Impossible d'accéder aux datasets: {e}")
    
    def test_serving_layer_exists(self):
        """Test que le dataset serving_layer existe."""
        try:
            from google.cloud import bigquery
            
            client = bigquery.Client(project="datalake-sensei")
            
            # Vérifier l'existence du dataset serving_layer
            try:
                dataset_ref = client.dataset("serving_layer")
                dataset = client.get_dataset(dataset_ref)
                
                logger.info(f"✓ Dataset serving_layer trouvé")
                logger.info(f"  - Créé: {dataset.created}")
                logger.info(f"  - Localisation: {dataset.location}")
                
                return True
                
            except Exception as e:
                logger.warning(f"⚠ Dataset serving_layer non accessible: {e}")
                return False
                
        except Exception as e:
            logger.error(f"Erreur vérification serving_layer: {e}")
            pytest.fail(f"Impossible de vérifier serving_layer: {e}")
    
    def test_serving_layer_tables_simple(self):
        """Test d'accès simple aux tables du serving_layer."""
        try:
            from google.cloud import bigquery
            
            client = bigquery.Client(project="datalake-sensei")
            
            # Lister les tables du dataset serving_layer
            try:
                dataset_ref = client.dataset("serving_layer")
                tables = list(client.list_tables(dataset_ref))
                
                table_names = [table.table_id for table in tables]
                
                logger.info(f"✓ Tables dans serving_layer: {len(table_names)}")
                for name in table_names[:10]:  # Première 10 tables
                    logger.info(f"  - {name}")
                
                if len(table_names) > 10:
                    logger.info(f"  ... et {len(table_names) - 10} autres")
                
                # Vérifier qu'on a des tables
                assert len(table_names) > 0, "Aucune table trouvée dans serving_layer"
                
                return table_names
                
            except Exception as e:
                logger.warning(f"⚠ Impossible de lister les tables: {e}")
                return []
                
        except Exception as e:
            logger.error(f"Erreur accès tables: {e}")
            pytest.fail(f"Impossible d'accéder aux tables: {e}")
    
    def test_table_sample_access(self):
        """Test d'accès à un échantillon d'une table."""
        try:
            from google.cloud import bigquery
            
            client = bigquery.Client(project="datalake-sensei")
            
            # D'abord, obtenir la liste des tables
            dataset_ref = client.dataset("serving_layer")
            tables = list(client.list_tables(dataset_ref))
            
            if len(tables) == 0:
                logger.warning("⚠ Aucune table disponible pour test")
                return None
            
            # Prendre la première table
            first_table = tables[0]
            table_name = first_table.table_id
            
            logger.info(f"Test avec table: {table_name}")
            
            # Accéder à un échantillon
            query = f"""
            SELECT *
            FROM `datalake-sensei.serving_layer.{table_name}`
            LIMIT 3
            """
            
            query_job = client.query(query)
            results = query_job.result()
            df = results.to_dataframe()
            
            logger.info(f"✓ Échantillon récupéré: {len(df)} lignes, {len(df.columns)} colonnes")
            
            if len(df.columns) > 0:
                logger.info(f"  Colonnes: {list(df.columns)[:5]}...")  # Première 5 colonnes
            
            # Vérifications de base
            assert len(df.columns) > 0, "Aucune colonne dans l'échantillon"
            
            return df
            
        except Exception as e:
            logger.error(f"Erreur accès échantillon: {e}")
            # Ne pas faire échouer le test, juste logger
            logger.warning(f"Accès échantillon non disponible: {e}")
            return None
    
    def test_information_schema_access(self):
        """Test d'accès au schéma d'information."""
        try:
            from google.cloud import bigquery
            
            client = bigquery.Client(project="datalake-sensei")
            
            # Test d'accès au schéma d'information
            query = """
            SELECT table_name
            FROM `datalake-sensei.serving_layer.INFORMATION_SCHEMA.TABLES`
            LIMIT 5
            """
            
            query_job = client.query(query)
            results = query_job.result()
            df = results.to_dataframe()
            
            logger.info(f"✓ Information schema accessible: {len(df)} tables trouvées")
            
            if len(df) > 0:
                for _, row in df.iterrows():
                    logger.info(f"  - {row['table_name']}")
            
            return df
            
        except Exception as e:
            logger.error(f"Erreur accès information schema: {e}")
            # Ne pas faire échouer le test
            logger.warning(f"Information schema non accessible: {e}")
            return None
    
    def test_query_performance_basic(self):
        """Test de performance de base."""
        try:
            from google.cloud import bigquery
            import time
            
            client = bigquery.Client(project="datalake-sensei")
            
            # Test de performance sur une requête très simple
            start_time = time.time()
            
            query = "SELECT 'test' as message, CURRENT_TIMESTAMP() as timestamp"
            
            query_job = client.query(query)
            results = query_job.result()
            df = results.to_dataframe()
            
            execution_time = time.time() - start_time
            
            assert len(df) == 1, "Résultat incorrect"
            assert execution_time < 30, f"Requête trop lente: {execution_time:.2f}s"
            
            logger.info(f"✓ Performance requête de base: {execution_time:.2f}s")
            logger.info(f"  - Message: {df.iloc[0]['message']}")
            
            return execution_time
            
        except Exception as e:
            logger.error(f"Erreur test performance: {e}")
            pytest.fail(f"Test de performance échoué: {e}")
    
    def test_credentials_validation(self):
        """Test de validation des credentials."""
        try:
            from google.cloud import bigquery
            
            client = bigquery.Client(project="datalake-sensei")
            
            # Test d'accès aux informations du projet
            query = "SELECT @@project_id as project_id"
            
            query_job = client.query(query)
            results = query_job.result()
            df = results.to_dataframe()
            
            project_id = df.iloc[0]['project_id']
            
            logger.info(f"✓ Credentials validés pour projet: {project_id}")
            
            assert project_id == "datalake-sensei", f"Projet incorrect: {project_id}"
            
            return project_id
            
        except Exception as e:
            logger.error(f"Erreur validation credentials: {e}")
            pytest.fail(f"Validation credentials échouée: {e}")


if __name__ == "__main__":
    # Configurer les credentials si pas déjà fait
    if not os.getenv('GOOGLE_APPLICATION_CREDENTIALS'):
        credentials_path = Path(__file__).parent.parent / "credentials" / "sensei-ai-service-account.json"
        if credentials_path.exists():
            os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = str(credentials_path)
    
    pytest.main([__file__, "-v", "-s"])
