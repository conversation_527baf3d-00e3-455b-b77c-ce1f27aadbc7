"""
Analyse avancée des données disponibles pour optimisation des modèles.

Explore toutes les tables du serving layer pour identifier les opportunités d'amélioration.
"""

import pytest
import os
import pandas as pd
import numpy as np
from pathlib import Path
import logging
from google.cloud import bigquery
import warnings
warnings.filterwarnings("ignore")

logger = logging.getLogger(__name__)


class AdvancedDataAnalyzer:
    """Analyseur avancé des données disponibles."""
    
    def __init__(self):
        self.client = bigquery.Client(project="datalake-sensei")
        self.available_tables = {}
        self.data_insights = {}
    
    def discover_all_tables(self):
        """Découvre toutes les tables disponibles."""
        logger.info("🔍 Découverte de toutes les tables disponibles...")
        
        query = """
        SELECT table_name
        FROM `datalake-sensei.serving_layer.INFORMATION_SCHEMA.TABLES`
        WHERE table_type = 'VIEW'
        ORDER BY table_name
        """
        
        result = self.client.query(query).result()
        df = result.to_dataframe()
        
        for _, row in df.iterrows():
            table_name = row['table_name']
            self.available_tables[table_name] = {
                'accessible': True,
                'type': 'view'
            }
        
        logger.info(f"✅ {len(self.available_tables)} tables découvertes")
        return self.available_tables
    
    def analyze_typeform_data(self):
        """Analyse les données Typeform pour insights."""
        logger.info("📋 Analyse des données Typeform...")
        
        insights = {}
        
        # Analyser les soumissions de formulaires
        try:
            query = """
            SELECT 
                COUNT(*) as total_soumissions,
                COUNT(DISTINCT id_prospect) as prospects_uniques,
                AVG(duree_reponses_minutes) as duree_moyenne,
                MIN(dt_soumission) as premiere_soumission,
                MAX(dt_soumission) as derniere_soumission
            FROM `datalake-sensei.serving_layer.vw_fact_soumission_formulaire`
            """
            
            result = self.client.query(query).result()
            df = result.to_dataframe()
            
            if len(df) > 0:
                insights['soumissions'] = {
                    'total': int(df.iloc[0]['total_soumissions']),
                    'prospects_uniques': int(df.iloc[0]['prospects_uniques']),
                    'duree_moyenne': float(df.iloc[0]['duree_moyenne']) if df.iloc[0]['duree_moyenne'] else 0,
                    'periode': (df.iloc[0]['premiere_soumission'], df.iloc[0]['derniere_soumission'])
                }
                
                logger.info(f"  📊 Soumissions: {insights['soumissions']['total']:,}")
                logger.info(f"  👥 Prospects uniques: {insights['soumissions']['prospects_uniques']:,}")
                logger.info(f"  ⏱️ Durée moyenne: {insights['soumissions']['duree_moyenne']:.1f} min")
        
        except Exception as e:
            logger.warning(f"⚠️ Erreur analyse soumissions: {e}")
        
        # Analyser les types de questions
        try:
            query = """
            SELECT 
                type_question,
                COUNT(*) as nb_questions,
                COUNT(DISTINCT id_formulaire) as nb_formulaires
            FROM `datalake-sensei.serving_layer.vw_dim_question`
            GROUP BY type_question
            ORDER BY nb_questions DESC
            """
            
            result = self.client.query(query).result()
            df = result.to_dataframe()
            
            if len(df) > 0:
                insights['types_questions'] = df.to_dict('records')
                logger.info(f"  📝 Types de questions: {len(insights['types_questions'])}")
                for item in insights['types_questions'][:5]:
                    logger.info(f"    - {item['type_question']}: {item['nb_questions']} questions")
        
        except Exception as e:
            logger.warning(f"⚠️ Erreur analyse questions: {e}")
        
        return insights
    
    def analyze_transaction_data(self):
        """Analyse les données de transactions."""
        logger.info("💰 Analyse des données de transactions...")
        
        insights = {}
        
        try:
            query = """
            SELECT 
                COUNT(*) as total_transactions,
                COUNT(DISTINCT nom_complet) as clients_uniques,
                AVG(montant) as montant_moyen,
                SUM(montant) as chiffre_affaires_total,
                COUNT(DISTINCT dealstage) as nb_etapes,
                COUNT(DISTINCT pipeline) as nb_pipelines
            FROM `datalake-sensei.serving_layer.vw_fact_transaction`
            WHERE montant IS NOT NULL
            """
            
            result = self.client.query(query).result()
            df = result.to_dataframe()
            
            if len(df) > 0:
                insights['global'] = {
                    'total_transactions': int(df.iloc[0]['total_transactions']),
                    'clients_uniques': int(df.iloc[0]['clients_uniques']),
                    'montant_moyen': float(df.iloc[0]['montant_moyen']),
                    'ca_total': float(df.iloc[0]['chiffre_affaires_total']),
                    'nb_etapes': int(df.iloc[0]['nb_etapes']),
                    'nb_pipelines': int(df.iloc[0]['nb_pipelines'])
                }
                
                logger.info(f"  💼 Transactions: {insights['global']['total_transactions']:,}")
                logger.info(f"  👥 Clients uniques: {insights['global']['clients_uniques']:,}")
                logger.info(f"  💰 Montant moyen: {insights['global']['montant_moyen']:,.0f}€")
                logger.info(f"  📈 CA total: {insights['global']['ca_total']:,.0f}€")
        
        except Exception as e:
            logger.warning(f"⚠️ Erreur analyse transactions: {e}")
        
        # Analyser les étapes de pipeline
        try:
            query = """
            SELECT 
                dealstage,
                COUNT(*) as nb_deals,
                AVG(montant) as montant_moyen,
                SUM(montant) as montant_total
            FROM `datalake-sensei.serving_layer.vw_fact_transaction`
            WHERE montant IS NOT NULL
            GROUP BY dealstage
            ORDER BY nb_deals DESC
            LIMIT 10
            """
            
            result = self.client.query(query).result()
            df = result.to_dataframe()
            
            if len(df) > 0:
                insights['etapes_pipeline'] = df.to_dict('records')
                logger.info(f"  🔄 Étapes de pipeline: {len(insights['etapes_pipeline'])}")
        
        except Exception as e:
            logger.warning(f"⚠️ Erreur analyse pipeline: {e}")
        
        return insights
    
    def analyze_modjo_data(self):
        """Analyse les données Modjo."""
        logger.info("📞 Analyse des données Modjo...")
        
        insights = {}
        
        # Analyser les appels
        try:
            query = """
            SELECT 
                COUNT(*) as total_appels,
                AVG(duration) as duree_moyenne,
                COUNT(DISTINCT contactCrmId) as contacts_uniques,
                COUNT(DISTINCT userId) as users_uniques
            FROM `datalake-sensei.serving_layer.vw_fact_modjo_call`
            WHERE duration IS NOT NULL
            """
            
            result = self.client.query(query).result()
            df = result.to_dataframe()
            
            if len(df) > 0:
                insights['appels'] = {
                    'total': int(df.iloc[0]['total_appels']),
                    'duree_moyenne': float(df.iloc[0]['duree_moyenne']),
                    'contacts_uniques': int(df.iloc[0]['contacts_uniques']),
                    'users_uniques': int(df.iloc[0]['users_uniques'])
                }
                
                logger.info(f"  📞 Appels: {insights['appels']['total']:,}")
                logger.info(f"  ⏱️ Durée moyenne: {insights['appels']['duree_moyenne']:.0f}s")
                logger.info(f"  👥 Contacts: {insights['appels']['contacts_uniques']:,}")
        
        except Exception as e:
            logger.warning(f"⚠️ Erreur analyse appels: {e}")
        
        # Analyser les scores IA
        try:
            query = """
            SELECT 
                COUNT(*) as appels_avec_scores,
                AVG(score_appel_prequai) as score_prequai_moyen,
                AVG(score_ice_breaker) as score_ice_breaker_moyen,
                AVG(score_phase_decouverte) as score_decouverte_moyen,
                AVG(score_consolider_vente) as score_vente_moyen,
                AVG(score_visio_vente) as score_visio_moyen
            FROM `datalake-sensei.serving_layer.vw_dim_modjo_call_summary`
            WHERE score_appel_prequai IS NOT NULL
            """
            
            result = self.client.query(query).result()
            df = result.to_dataframe()
            
            if len(df) > 0:
                insights['scores_ia'] = {
                    'appels_avec_scores': int(df.iloc[0]['appels_avec_scores']),
                    'score_prequai': float(df.iloc[0]['score_prequai_moyen']) if df.iloc[0]['score_prequai_moyen'] else 0,
                    'score_ice_breaker': float(df.iloc[0]['score_ice_breaker_moyen']) if df.iloc[0]['score_ice_breaker_moyen'] else 0,
                    'score_decouverte': float(df.iloc[0]['score_decouverte_moyen']) if df.iloc[0]['score_decouverte_moyen'] else 0,
                    'score_vente': float(df.iloc[0]['score_vente_moyen']) if df.iloc[0]['score_vente_moyen'] else 0,
                    'score_visio': float(df.iloc[0]['score_visio_moyen']) if df.iloc[0]['score_visio_moyen'] else 0
                }
                
                logger.info(f"  🤖 Appels avec scores IA: {insights['scores_ia']['appels_avec_scores']:,}")
                logger.info(f"  📊 Score préqualification: {insights['scores_ia']['score_prequai']:.1f}")
                logger.info(f"  📊 Score découverte: {insights['scores_ia']['score_decouverte']:.1f}")
        
        except Exception as e:
            logger.warning(f"⚠️ Erreur analyse scores IA: {e}")
        
        return insights
    
    def analyze_engagement_patterns(self):
        """Analyse les patterns d'engagement multi-canal."""
        logger.info("🔄 Analyse des patterns d'engagement...")
        
        insights = {}
        
        try:
            # Analyser l'engagement email
            query = """
            SELECT 
                COUNT(*) as total_emails,
                COUNT(DISTINCT id_contact_emetteur) as emetteurs_uniques,
                COUNT(DISTINCT id_contact_destinataire) as destinataires_uniques
            FROM `datalake-sensei.serving_layer.vw_dim_email`
            """
            
            result = self.client.query(query).result()
            df = result.to_dataframe()
            
            if len(df) > 0:
                insights['emails'] = {
                    'total': int(df.iloc[0]['total_emails']),
                    'emetteurs': int(df.iloc[0]['emetteurs_uniques']),
                    'destinataires': int(df.iloc[0]['destinataires_uniques'])
                }
                
                logger.info(f"  📧 Emails: {insights['emails']['total']:,}")
        
        except Exception as e:
            logger.warning(f"⚠️ Erreur analyse emails: {e}")
        
        try:
            # Analyser les réunions
            query = """
            SELECT 
                COUNT(*) as total_reunions,
                COUNT(DISTINCT hs_created_by) as createurs_uniques
            FROM `datalake-sensei.serving_layer.vw_dim_reunion`
            """
            
            result = self.client.query(query).result()
            df = result.to_dataframe()
            
            if len(df) > 0:
                insights['reunions'] = {
                    'total': int(df.iloc[0]['total_reunions']),
                    'createurs': int(df.iloc[0]['createurs_uniques'])
                }
                
                logger.info(f"  🤝 Réunions: {insights['reunions']['total']:,}")
        
        except Exception as e:
            logger.warning(f"⚠️ Erreur analyse réunions: {e}")
        
        return insights
    
    def identify_data_opportunities(self):
        """Identifie les opportunités d'amélioration des modèles."""
        logger.info("💡 Identification des opportunités d'amélioration...")
        
        opportunities = {
            'nouvelles_features': [],
            'sources_enrichissement': [],
            'patterns_comportementaux': [],
            'scores_qualite': []
        }
        
        # Nouvelles features potentielles
        opportunities['nouvelles_features'] = [
            {
                'name': 'typeform_completion_rate',
                'description': 'Taux de complétion des formulaires Typeform',
                'source': 'vw_fact_soumission_formulaire',
                'impact_estime': 'Moyen - Indicateur d\'engagement'
            },
            {
                'name': 'modjo_conversation_quality',
                'description': 'Score composite des conversations Modjo',
                'source': 'vw_dim_modjo_call_summary',
                'impact_estime': 'Élevé - Prédicteur de conversion'
            },
            {
                'name': 'multi_channel_engagement',
                'description': 'Score d\'engagement multi-canal (email + appel + réunion)',
                'source': 'vw_dim_email + vw_dim_appel + vw_dim_reunion',
                'impact_estime': 'Très élevé - Vue 360°'
            },
            {
                'name': 'transaction_velocity',
                'description': 'Vitesse de progression dans le pipeline',
                'source': 'vw_fact_transaction',
                'impact_estime': 'Élevé - Timing optimal'
            },
            {
                'name': 'content_sentiment',
                'description': 'Analyse de sentiment des emails et transcriptions',
                'source': 'vw_dim_email + vw_dim_modjo_transcript',
                'impact_estime': 'Élevé - Qualité relation'
            }
        ]
        
        # Sources d'enrichissement
        opportunities['sources_enrichissement'] = [
            {
                'source': 'vw_dim_abonnement',
                'description': 'Données de récurrence et LTV',
                'utilisation': 'Prédiction de valeur client à long terme'
            },
            {
                'source': 'vw_dim_ligne_produit',
                'description': 'Préférences produits et pricing',
                'utilisation': 'Recommandation produits et pricing optimal'
            },
            {
                'source': 'vw_dim_ticket',
                'description': 'Historique support et satisfaction',
                'utilisation': 'Indicateur de risque de churn'
            }
        ]
        
        # Patterns comportementaux
        opportunities['patterns_comportementaux'] = [
            'Séquences temporelles d\'engagement (email → appel → réunion)',
            'Patterns de réponse aux formulaires Typeform',
            'Corrélation entre scores Modjo et conversion',
            'Saisonnalité des conversions par canal',
            'Influence du consultant sur le taux de conversion'
        ]
        
        # Scores de qualité
        opportunities['scores_qualite'] = [
            'Score de complétude des données par contact',
            'Score de fraîcheur des interactions',
            'Score de cohérence multi-sources',
            'Score de maturité du prospect (basé sur l\'historique)'
        ]
        
        logger.info(f"💡 {len(opportunities['nouvelles_features'])} nouvelles features identifiées")
        logger.info(f"📊 {len(opportunities['sources_enrichissement'])} sources d'enrichissement")
        logger.info(f"🔄 {len(opportunities['patterns_comportementaux'])} patterns comportementaux")
        
        return opportunities


class TestAdvancedDataAnalysis:
    """Test d'analyse avancée des données."""
    
    def setup_method(self):
        """Setup pour chaque test."""
        credentials_path = os.getenv('GOOGLE_APPLICATION_CREDENTIALS')
        if not credentials_path or not os.path.exists(credentials_path):
            pytest.skip("Credentials BigQuery non configurés")
        
        self.analyzer = AdvancedDataAnalyzer()
        logger.info(f"Credentials: {credentials_path}")
    
    def test_comprehensive_data_discovery(self):
        """Test de découverte complète des données."""
        try:
            logger.info("🔍 Découverte complète des données disponibles...")
            
            # Découvrir toutes les tables
            tables = self.analyzer.discover_all_tables()
            
            # Analyser chaque source de données
            typeform_insights = self.analyzer.analyze_typeform_data()
            transaction_insights = self.analyzer.analyze_transaction_data()
            modjo_insights = self.analyzer.analyze_modjo_data()
            engagement_insights = self.analyzer.analyze_engagement_patterns()
            
            # Identifier les opportunités
            opportunities = self.analyzer.identify_data_opportunities()
            
            logger.info("✅ Analyse complète terminée")
            
            # Vérifications
            assert len(tables) > 20, f"Pas assez de tables: {len(tables)}"
            assert len(opportunities['nouvelles_features']) > 0, "Aucune nouvelle feature identifiée"
            
            return {
                'tables': tables,
                'typeform': typeform_insights,
                'transactions': transaction_insights,
                'modjo': modjo_insights,
                'engagement': engagement_insights,
                'opportunities': opportunities
            }
            
        except Exception as e:
            logger.error(f"Erreur analyse avancée: {e}")
            pytest.fail(f"Analyse avancée échouée: {e}")


if __name__ == "__main__":
    # Configurer les credentials
    if not os.getenv('GOOGLE_APPLICATION_CREDENTIALS'):
        credentials_path = Path(__file__).parent.parent / "credentials" / "sensei-ai-service-account.json"
        if credentials_path.exists():
            os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = str(credentials_path)
    
    pytest.main([__file__, "-v", "-s"])
