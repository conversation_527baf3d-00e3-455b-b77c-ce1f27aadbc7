"""
Tests for Sensei AI API endpoints.

Comprehensive test suite covering all API functionality including:
- Authentication and authorization
- Conversion prediction endpoints
- Channel timing optimization
- NLP analysis
- Model management
- Health checks
"""

import pytest
import asyncio
from httpx import AsyncClient
from fastapi.testclient import TestClient
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.sensei.api import create_app
from src.sensei.api.auth import create_api_key


@pytest.fixture
def app():
    """Create test FastAPI app."""
    return create_app()


@pytest.fixture
def client(app):
    """Create test client."""
    return TestClient(app)


@pytest.fixture
def api_key():
    """Create test API key."""
    return create_api_key(
        user_id="test_user",
        name="Test API Key",
        permissions=["*"],
    )


@pytest.fixture
def auth_headers(api_key):
    """Create authentication headers."""
    return {"Authorization": f"Bearer {api_key}"}


class TestHealthEndpoints:
    """Test health check endpoints."""
    
    def test_health_check(self, client):
        """Test main health check endpoint."""
        response = client.get("/health/")
        assert response.status_code == 200
        
        data = response.json()
        assert "status" in data
        assert "timestamp" in data
        assert "uptime_seconds" in data
        assert "components" in data
    
    def test_liveness_check(self, client):
        """Test liveness endpoint."""
        response = client.get("/health/liveness")
        assert response.status_code == 200
        
        data = response.json()
        assert data["status"] == "alive"
    
    def test_readiness_check(self, client):
        """Test readiness endpoint."""
        response = client.get("/health/readiness")
        # May return 200 or 503 depending on system state
        assert response.status_code in [200, 503]


class TestAuthenticationEndpoints:
    """Test authentication and authorization."""
    
    def test_unauthenticated_request(self, client):
        """Test request without authentication."""
        response = client.post("/conversion/predict", json={
            "prospect_id": "test-123",
            "hubspot_score": 75
        })
        assert response.status_code == 401
    
    def test_invalid_api_key(self, client):
        """Test request with invalid API key."""
        headers = {"Authorization": "Bearer invalid-key"}
        response = client.post("/conversion/predict", json={
            "prospect_id": "test-123",
            "hubspot_score": 75
        }, headers=headers)
        assert response.status_code == 401
    
    def test_valid_authentication(self, client, auth_headers):
        """Test request with valid authentication."""
        response = client.get("/models/", headers=auth_headers)
        assert response.status_code == 200


class TestConversionEndpoints:
    """Test conversion prediction endpoints."""
    
    def test_conversion_prediction(self, client, auth_headers):
        """Test single conversion prediction."""
        request_data = {
            "prospect_id": "test-prospect-123",
            "hubspot_score": 75,
            "num_conversion_events": 3,
            "email_count": 5,
            "call_count": 2,
            "typeform_submissions": 1,
            "days_since_last_engagement": 7
        }
        
        response = client.post(
            "/conversion/predict",
            json=request_data,
            headers=auth_headers
        )
        
        # May return 200 or 503 depending on model availability
        if response.status_code == 200:
            data = response.json()
            assert "prospect_id" in data
            assert "conversion_probability" in data
            assert "confidence_score" in data
            assert "risk_category" in data
            assert "recommendations" in data
            
            # Validate data types and ranges
            assert 0 <= data["conversion_probability"] <= 1
            assert 0 <= data["confidence_score"] <= 1
            assert data["risk_category"] in ["low", "medium", "high"]
            assert isinstance(data["recommendations"], list)
        else:
            assert response.status_code == 503  # Model not available
    
    def test_batch_conversion_prediction(self, client, auth_headers):
        """Test batch conversion prediction."""
        request_data = {
            "prospects": [
                {
                    "prospect_id": "test-1",
                    "hubspot_score": 80,
                    "email_count": 3
                },
                {
                    "prospect_id": "test-2",
                    "hubspot_score": 60,
                    "call_count": 1
                }
            ]
        }
        
        response = client.post(
            "/conversion/predict/batch",
            json=request_data,
            headers=auth_headers
        )
        
        # May return 200 or 503 depending on model availability
        if response.status_code == 200:
            data = response.json()
            assert "results" in data
            assert "processed_count" in data
            assert "failed_count" in data
        else:
            assert response.status_code == 503
    
    def test_invalid_conversion_request(self, client, auth_headers):
        """Test conversion prediction with invalid data."""
        request_data = {
            "prospect_id": "",  # Invalid empty ID
            "hubspot_score": 150  # Invalid score > 100
        }
        
        response = client.post(
            "/conversion/predict",
            json=request_data,
            headers=auth_headers
        )
        assert response.status_code == 422  # Validation error


class TestChannelTimingEndpoints:
    """Test channel and timing optimization endpoints."""
    
    def test_channel_timing_recommendation(self, client, auth_headers):
        """Test channel timing recommendation."""
        request_data = {
            "prospect_id": "test-prospect-456",
            "email_interactions": 5,
            "call_interactions": 2,
            "meeting_interactions": 1,
            "email_success_rate": 0.6,
            "call_success_rate": 0.8,
            "meeting_success_rate": 0.9,
            "deal_value": 50000
        }
        
        response = client.post(
            "/channel-timing/recommend",
            json=request_data,
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert "prospect_id" in data
        assert "primary_recommendation" in data
        assert "alternative_recommendations" in data
        assert "optimal_contact_times" in data
        
        # Validate primary recommendation
        primary = data["primary_recommendation"]
        assert "channel" in primary
        assert "probability" in primary
        assert "confidence" in primary
        assert 0 <= primary["probability"] <= 1


class TestNLPEndpoints:
    """Test NLP analysis endpoints."""
    
    def test_nlp_analysis(self, client, auth_headers):
        """Test conversation analysis."""
        request_data = {
            "conversation_id": "conv-123",
            "transcript": "Hello, thank you for taking the time to speak with me today. I'm really excited about the opportunity to work together. What are your main challenges with your current solution?",
            "duration_minutes": 30,
            "participant_count": 2
        }
        
        response = client.post(
            "/nlp/analyze",
            json=request_data,
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert "conversation_id" in data
        assert "overall_quality_score" in data
        assert "sentiment_score" in data
        assert "engagement_level" in data
        assert "insights" in data
        assert "main_topics" in data
        
        # Validate data ranges
        assert 0 <= data["overall_quality_score"] <= 100
        assert -1 <= data["sentiment_score"] <= 1
        assert data["engagement_level"] in ["low", "medium", "high"]
    
    def test_nlp_analysis_short_transcript(self, client, auth_headers):
        """Test NLP analysis with very short transcript."""
        request_data = {
            "conversation_id": "conv-short",
            "transcript": "Hi there"  # Too short
        }
        
        response = client.post(
            "/nlp/analyze",
            json=request_data,
            headers=auth_headers
        )
        assert response.status_code == 422  # Validation error


class TestModelEndpoints:
    """Test model management endpoints."""
    
    def test_list_models(self, client, auth_headers):
        """Test listing models."""
        response = client.get("/models/", headers=auth_headers)
        assert response.status_code == 200
        
        data = response.json()
        assert "models" in data
        assert "total_models" in data
        assert "active_models" in data
        assert isinstance(data["models"], list)
    
    def test_get_registry_stats(self, client, auth_headers):
        """Test getting registry statistics."""
        response = client.get("/models/registry/stats", headers=auth_headers)
        assert response.status_code == 200
        
        data = response.json()
        assert "stats" in data
        assert "success" in data
        assert data["success"] is True


class TestErrorHandling:
    """Test error handling and edge cases."""
    
    def test_404_endpoint(self, client):
        """Test non-existent endpoint."""
        response = client.get("/non-existent-endpoint")
        assert response.status_code == 404
    
    def test_method_not_allowed(self, client, auth_headers):
        """Test wrong HTTP method."""
        response = client.get("/conversion/predict", headers=auth_headers)
        assert response.status_code == 405
    
    def test_large_request_body(self, client, auth_headers):
        """Test request with very large body."""
        # Create a large request (this might be limited by middleware)
        large_prospects = [
            {
                "prospect_id": f"test-{i}",
                "hubspot_score": 50
            }
            for i in range(200)  # Exceeds batch limit
        ]
        
        response = client.post(
            "/conversion/predict/batch",
            json={"prospects": large_prospects},
            headers=auth_headers
        )
        assert response.status_code == 422  # Validation error


@pytest.mark.asyncio
class TestAsyncEndpoints:
    """Test endpoints with async client."""
    
    async def test_concurrent_requests(self, auth_headers):
        """Test handling concurrent requests."""
        app = create_app()
        
        async with AsyncClient(app=app, base_url="http://test") as client:
            # Make multiple concurrent requests
            tasks = []
            for i in range(5):
                task = client.post(
                    "/conversion/predict",
                    json={
                        "prospect_id": f"concurrent-test-{i}",
                        "hubspot_score": 70
                    },
                    headers=auth_headers
                )
                tasks.append(task)
            
            responses = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Check that all requests completed
            assert len(responses) == 5
            
            # Most should succeed or return 503 (model unavailable)
            success_count = sum(
                1 for r in responses 
                if not isinstance(r, Exception) and r.status_code in [200, 503]
            )
            assert success_count >= 4  # Allow for some failures


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
