"""
Basic functionality tests for Sensei AI.

Tests core functionality without requiring external dependencies like BigQuery.
"""

import pytest
import sys
import os
import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, List, Any
import logging

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

logger = logging.getLogger(__name__)


class TestBasicFunctionality:
    """Test basic system functionality."""
    
    def test_python_version(self):
        """Test Python version compatibility."""
        assert sys.version_info >= (3, 9), f"Python 3.9+ required, found {sys.version}"
        logger.info(f"✓ Python version: {sys.version}")
    
    def test_core_imports(self):
        """Test that core modules can be imported."""
        try:
            # Test basic imports
            import pandas as pd
            import numpy as np
            import sklearn
            
            logger.info("✓ Core data science libraries imported")
            
            # Test ML libraries
            import lightgbm as lgb
            import catboost as cb
            
            logger.info("✓ ML libraries imported")
            
            # Test API libraries
            import fastapi
            import uvicorn
            import pydantic
            
            logger.info("✓ API libraries imported")
            
        except ImportError as e:
            pytest.fail(f"Failed to import required libraries: {e}")
    
    def test_sensei_imports(self):
        """Test Sensei AI module imports."""
        try:
            # Test utils
            from sensei.utils.config import Config
            from sensei.utils.logging import get_logger
            
            logger.info("✓ Sensei utils imported")
            
            # Test basic model structure
            from sensei.models.base import BaseModel
            
            logger.info("✓ Sensei models imported")
            
        except ImportError as e:
            logger.warning(f"Sensei modules not fully available: {e}")
            # This is expected if the full implementation isn't complete
    
    def test_data_processing_basics(self):
        """Test basic data processing functionality."""
        # Create sample data
        np.random.seed(42)
        data = {
            'id': range(100),
            'feature1': np.random.normal(0, 1, 100),
            'feature2': np.random.uniform(0, 100, 100),
            'feature3': np.random.choice(['A', 'B', 'C'], 100),
            'target': np.random.binomial(1, 0.3, 100)
        }
        
        df = pd.DataFrame(data)
        
        # Test basic operations
        assert len(df) == 100, "DataFrame creation failed"
        assert df['feature1'].dtype == np.float64, "Numeric feature type incorrect"
        assert df['feature3'].dtype == object, "Categorical feature type incorrect"
        
        # Test missing value handling
        df_with_missing = df.copy()
        df_with_missing.loc[0:10, 'feature1'] = np.nan
        
        filled_df = df_with_missing.fillna(0)
        assert filled_df['feature1'].isnull().sum() == 0, "Missing value filling failed"
        
        logger.info("✓ Basic data processing works")
    
    def test_ml_model_basics(self):
        """Test basic ML model functionality."""
        from sklearn.ensemble import RandomForestClassifier
        from sklearn.model_selection import train_test_split
        from sklearn.metrics import accuracy_score
        
        # Create sample data
        np.random.seed(42)
        X = np.random.normal(0, 1, (200, 5))
        y = (X[:, 0] + X[:, 1] > 0).astype(int)  # Simple linear relationship
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.3, random_state=42
        )
        
        # Train model
        model = RandomForestClassifier(n_estimators=10, random_state=42)
        model.fit(X_train, y_train)
        
        # Test predictions
        predictions = model.predict(X_test)
        accuracy = accuracy_score(y_test, predictions)
        
        assert accuracy > 0.5, f"Model accuracy too low: {accuracy}"
        logger.info(f"✓ Basic ML model works: accuracy = {accuracy:.3f}")
    
    def test_lightgbm_functionality(self):
        """Test LightGBM functionality."""
        try:
            import lightgbm as lgb
            from sklearn.model_selection import train_test_split
            from sklearn.metrics import roc_auc_score
            
            # Create sample data
            np.random.seed(42)
            X = np.random.normal(0, 1, (500, 10))
            y = (X[:, 0] + X[:, 1] + np.random.normal(0, 0.1, 500) > 0).astype(int)
            
            # Split data
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.3, random_state=42
            )
            
            # Train LightGBM model
            model = lgb.LGBMClassifier(
                n_estimators=50,
                random_state=42,
                verbose=-1
            )
            model.fit(X_train, y_train)
            
            # Test predictions
            predictions = model.predict_proba(X_test)[:, 1]
            auc = roc_auc_score(y_test, predictions)
            
            assert auc > 0.5, f"LightGBM AUC too low: {auc}"
            logger.info(f"✓ LightGBM works: AUC = {auc:.3f}")
            
        except ImportError:
            pytest.skip("LightGBM not available")
    
    def test_catboost_functionality(self):
        """Test CatBoost functionality."""
        try:
            import catboost as cb
            from sklearn.model_selection import train_test_split
            from sklearn.metrics import roc_auc_score
            
            # Create sample data
            np.random.seed(42)
            X = np.random.normal(0, 1, (500, 10))
            y = (X[:, 0] + X[:, 1] + np.random.normal(0, 0.1, 500) > 0).astype(int)
            
            # Split data
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.3, random_state=42
            )
            
            # Train CatBoost model
            model = cb.CatBoostClassifier(
                iterations=50,
                random_state=42,
                verbose=False
            )
            model.fit(X_train, y_train)
            
            # Test predictions
            predictions = model.predict_proba(X_test)[:, 1]
            auc = roc_auc_score(y_test, predictions)
            
            assert auc > 0.5, f"CatBoost AUC too low: {auc}"
            logger.info(f"✓ CatBoost works: AUC = {auc:.3f}")
            
        except ImportError:
            pytest.skip("CatBoost not available")
    
    def test_fastapi_basic(self):
        """Test FastAPI basic functionality."""
        try:
            from fastapi import FastAPI
            from fastapi.testclient import TestClient
            
            # Create simple app
            app = FastAPI()
            
            @app.get("/")
            def read_root():
                return {"message": "Hello World"}
            
            @app.get("/health")
            def health_check():
                return {"status": "healthy"}
            
            # Test with client
            client = TestClient(app)
            
            response = client.get("/")
            assert response.status_code == 200
            assert response.json() == {"message": "Hello World"}
            
            response = client.get("/health")
            assert response.status_code == 200
            assert response.json() == {"status": "healthy"}
            
            logger.info("✓ FastAPI basic functionality works")
            
        except ImportError:
            pytest.skip("FastAPI not available")
    
    def test_hyperparameter_optimization_basic(self):
        """Test basic hyperparameter optimization."""
        try:
            import optuna
            from sklearn.ensemble import RandomForestClassifier
            from sklearn.model_selection import cross_val_score
            
            # Create sample data
            np.random.seed(42)
            X = np.random.normal(0, 1, (200, 5))
            y = (X[:, 0] + X[:, 1] > 0).astype(int)
            
            def objective(trial):
                n_estimators = trial.suggest_int('n_estimators', 10, 50)
                max_depth = trial.suggest_int('max_depth', 3, 10)
                
                model = RandomForestClassifier(
                    n_estimators=n_estimators,
                    max_depth=max_depth,
                    random_state=42
                )
                
                scores = cross_val_score(model, X, y, cv=3, scoring='roc_auc')
                return scores.mean()
            
            # Run optimization
            study = optuna.create_study(direction='maximize')
            study.optimize(objective, n_trials=10, show_progress_bar=False)
            
            assert study.best_value > 0.5, f"Optimization failed: {study.best_value}"
            logger.info(f"✓ Hyperparameter optimization works: best_value = {study.best_value:.3f}")
            
        except ImportError:
            pytest.skip("Optuna not available")


if __name__ == "__main__":
    pytest.main([__file__, "-v", "-s"])
