"""
Mock data validation tests for Sensei AI.

Tests the system with synthetic data to validate ML pipeline,
feature engineering, and model training without requiring BigQuery.
"""

import pytest
import sys
import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, List, Any, Tuple
import logging
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, roc_auc_score, precision_score, recall_score
import warnings

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

logger = logging.getLogger(__name__)
warnings.filterwarnings("ignore")


class MockDataGenerator:
    """Generate realistic synthetic data for testing."""
    
    def __init__(self, seed: int = 42):
        """Initialize with random seed."""
        self.seed = seed
        np.random.seed(seed)
    
    def generate_conversion_data(self, n_samples: int = 1000) -> pd.DataFrame:
        """Generate synthetic conversion data."""
        np.random.seed(self.seed)
        
        # Generate base features
        data = {
            'id_contact': [f'contact_{i:06d}' for i in range(n_samples)],
            
            # HubSpot features
            'hubspotscore': np.random.beta(2, 5, n_samples) * 100,  # Skewed towards lower scores
            'num_unique_conversion_events': np.random.poisson(1.5, n_samples),
            'num_conversion_events': np.random.poisson(2.0, n_samples),
            'num_associated_deals': np.random.poisson(0.8, n_samples),
            'hs_analytics_num_page_views': np.random.negative_binomial(5, 0.3, n_samples),
            'statut_du_lead': np.random.choice(['new', 'qualified', 'unqualified', 'contacted'], n_samples),
            'ip_country': np.random.choice(['US', 'FR', 'UK', 'DE', 'CA'], n_samples, p=[0.4, 0.2, 0.15, 0.15, 0.1]),
            
            # Typeform features
            'typeform_submissions_count': np.random.poisson(0.5, n_samples),
            'avg_response_time_minutes': np.random.exponential(15, n_samples),
            'unique_forms_count': np.random.poisson(0.3, n_samples),
            'response_completion_rate': np.random.beta(3, 2, n_samples),
            
            # Engagement features
            'email_count': np.random.poisson(3, n_samples),
            'call_count': np.random.poisson(1, n_samples),
            'meeting_count': np.random.poisson(0.2, n_samples),
            'days_since_last_engagement': np.random.exponential(10, n_samples),
            
            # Modjo features
            'modjo_call_count': np.random.poisson(0.5, n_samples),
            'avg_prequalification_score': np.random.normal(70, 15, n_samples),
            'avg_ice_breaker_score': np.random.normal(65, 20, n_samples),
            'avg_discovery_score': np.random.normal(60, 25, n_samples),
            'avg_sales_consolidation_score': np.random.normal(55, 30, n_samples),
            'avg_sales_video_score': np.random.normal(50, 35, n_samples),
        }
        
        df = pd.DataFrame(data)
        
        # Clip scores to reasonable ranges
        score_columns = [col for col in df.columns if 'score' in col.lower()]
        for col in score_columns:
            df[col] = np.clip(df[col], 0, 100)
        
        # Generate realistic target variable with strong predictive signal
        # Create clear patterns that ML models can learn

        # Normalize key features for better signal
        hubspot_norm = df['hubspotscore'] / 100
        email_norm = np.minimum(df['email_count'] / 8, 1)
        call_norm = np.minimum(df['call_count'] / 4, 1)

        # Create strong predictive features
        high_value_prospect = (
            (hubspot_norm > 0.7) &
            (df['email_count'] > 3) &
            (df['statut_du_lead'] == 'qualified')
        )

        engaged_prospect = (
            (df['call_count'] > 0) |
            (df['meeting_count'] > 0) |
            (df['typeform_submissions_count'] > 0)
        )

        # Calculate conversion probability with clear patterns but realistic rates
        conversion_prob = (
            0.05 +  # Base probability
            hubspot_norm * 0.2 +  # HubSpot influence
            email_norm * 0.1 +  # Email engagement
            call_norm * 0.15 +  # Call engagement
            high_value_prospect * 0.15 +  # High-value prospect bonus
            engaged_prospect * 0.08 +  # Engagement bonus
            (df['meeting_count'] > 0) * 0.12 +  # Meeting bonus
            np.random.normal(0, 0.04, n_samples)  # Some noise
        )

        conversion_prob = np.clip(conversion_prob, 0.02, 0.5)
        df['converted'] = np.random.binomial(1, conversion_prob, n_samples)
        
        # Add predictive derived features
        df['total_engagement_score'] = (
            df['email_count'] * 1 +
            df['call_count'] * 3 +
            df['meeting_count'] * 5 +
            df['typeform_submissions_count'] * 2
        )

        df['hubspot_score_category'] = pd.cut(
            df['hubspotscore'],
            bins=[0, 30, 70, 100],
            labels=['low', 'medium', 'high']
        )

        df['engagement_recency'] = pd.cut(
            df['days_since_last_engagement'],
            bins=[0, 7, 30, float('inf')],
            labels=['recent', 'moderate', 'old']
        )

        # Add interaction features that are highly predictive
        df['hubspot_email_interaction'] = df['hubspotscore'] * df['email_count']
        df['call_meeting_combo'] = (df['call_count'] > 0) & (df['meeting_count'] > 0)
        df['high_engagement_qualified'] = (df['total_engagement_score'] > 10) & (df['statut_du_lead'] == 'qualified')
        df['conversion_events_per_engagement'] = np.where(
            df['total_engagement_score'] > 0,
            df['num_conversion_events'] / df['total_engagement_score'],
            0
        )
        
        return df
    
    def generate_channel_timing_data(self, n_samples: int = 500) -> pd.DataFrame:
        """Generate synthetic channel timing data."""
        np.random.seed(self.seed + 1)
        
        data = {
            'id_contact': [f'contact_{i:06d}' for i in range(n_samples)],
            'total_emails': np.random.poisson(5, n_samples),
            'total_calls': np.random.poisson(2, n_samples),
            'total_meetings': np.random.poisson(0.5, n_samples),
            'email_success_rate': np.random.beta(2, 3, n_samples),
            'call_success_rate': np.random.beta(3, 2, n_samples),
            'meeting_success_rate': np.random.beta(4, 1, n_samples),
            'montant': np.random.lognormal(10, 1, n_samples),
            'dealstage': np.random.choice(['prospect', 'qualified', 'proposal', 'negotiation'], n_samples),
            'creation_hour': np.random.randint(8, 18, n_samples),
            'creation_day_of_week': np.random.randint(1, 6, n_samples),  # Weekdays
            'creation_month': np.random.randint(1, 13, n_samples),
        }
        
        df = pd.DataFrame(data)
        
        # Generate preferred channel based on success rates
        channel_probs = np.column_stack([
            df['email_success_rate'],
            df['call_success_rate'], 
            df['meeting_success_rate']
        ])
        
        # Normalize probabilities
        channel_probs = channel_probs / channel_probs.sum(axis=1, keepdims=True)
        
        preferred_channels = []
        for i in range(n_samples):
            channel = np.random.choice(['email', 'call', 'meeting'], p=channel_probs[i])
            preferred_channels.append(channel)
        
        df['preferred_channel'] = preferred_channels
        
        # Add timing features
        df['optimal_time_slot'] = np.random.choice(
            ['morning', 'afternoon', 'evening'], 
            n_samples, 
            p=[0.4, 0.5, 0.1]
        )
        
        df['optimal_day_type'] = np.random.choice(
            ['weekday_peak', 'weekday_off', 'weekend'],
            n_samples,
            p=[0.7, 0.25, 0.05]
        )
        
        return df
    
    def generate_nlp_data(self, n_samples: int = 200) -> pd.DataFrame:
        """Generate synthetic NLP conversation data."""
        np.random.seed(self.seed + 2)
        
        # Sample conversation templates
        conversation_templates = [
            "Hello, thank you for taking the time to speak with me today. I'm really excited about the opportunity to work together.",
            "I understand you're looking for a solution to help with your sales process. Can you tell me more about your current challenges?",
            "That's a great question about pricing. Let me walk you through our different packages and see what might work best for you.",
            "I appreciate your concerns about implementation. We have a dedicated team that helps with onboarding and training.",
            "Based on what you've told me, I think our premium package would be the best fit for your needs.",
        ]
        
        data = {
            'conversation_id': [f'conv_{i:06d}' for i in range(n_samples)],
            'transcript': [],
            'duration_minutes': np.random.exponential(25, n_samples),
            'participant_count': np.random.choice([2, 3, 4], n_samples, p=[0.7, 0.2, 0.1]),
        }
        
        # Generate transcripts
        for i in range(n_samples):
            # Combine random templates
            num_segments = np.random.randint(2, 6)
            transcript_parts = np.random.choice(conversation_templates, num_segments, replace=True)
            transcript = ' '.join(transcript_parts)
            
            # Add some variation
            if np.random.random() > 0.5:
                transcript += " What questions do you have for me?"
            if np.random.random() > 0.7:
                transcript += " I think this could be a great partnership."
            
            data['transcript'].append(transcript)
        
        df = pd.DataFrame(data)
        
        # Generate quality scores based on transcript features
        df['word_count'] = df['transcript'].str.split().str.len()
        df['question_count'] = df['transcript'].str.count(r'\?')
        df['positive_words'] = df['transcript'].str.count(r'\b(great|excellent|perfect|amazing|wonderful)\b')
        
        # Overall quality score
        df['overall_quality_score'] = np.clip(
            50 + 
            (df['word_count'] / 10) +
            (df['question_count'] * 5) +
            (df['positive_words'] * 10) +
            np.random.normal(0, 10, n_samples),
            0, 100
        )
        
        # Sentiment score
        df['sentiment_score'] = np.clip(
            (df['positive_words'] - df['transcript'].str.count(r'\b(bad|terrible|awful)\b')) / 10 +
            np.random.normal(0, 0.2, n_samples),
            -1, 1
        )
        
        # Engagement level
        engagement_scores = (
            (df['word_count'] > 50) * 1 +
            (df['question_count'] > 1) * 1 +
            (df['duration_minutes'] > 20) * 1
        )
        df['engagement_level'] = pd.cut(
            engagement_scores,
            bins=[-1, 0, 1, 3],
            labels=['low', 'medium', 'high']
        )
        
        return df


class TestMockDataValidation:
    """Test system with mock data."""
    
    def setup_method(self):
        """Setup for each test method."""
        self.data_generator = MockDataGenerator(seed=42)
    
    def test_mock_data_generation(self):
        """Test mock data generation."""
        # Test conversion data
        conversion_df = self.data_generator.generate_conversion_data(100)
        
        assert len(conversion_df) == 100, "Conversion data generation failed"
        assert 'converted' in conversion_df.columns, "Target variable missing"
        assert conversion_df['hubspotscore'].between(0, 100).all(), "HubSpot scores out of range"
        
        # Check class distribution
        conversion_rate = conversion_df['converted'].mean()
        assert 0.05 <= conversion_rate <= 0.5, f"Unrealistic conversion rate: {conversion_rate}"
        
        logger.info(f"✓ Conversion data generated: {len(conversion_df)} samples, {conversion_rate:.1%} conversion rate")
        
        # Test channel timing data
        channel_df = self.data_generator.generate_channel_timing_data(50)
        
        assert len(channel_df) == 50, "Channel timing data generation failed"
        assert 'preferred_channel' in channel_df.columns, "Preferred channel missing"
        
        logger.info(f"✓ Channel timing data generated: {len(channel_df)} samples")
        
        # Test NLP data
        nlp_df = self.data_generator.generate_nlp_data(30)
        
        assert len(nlp_df) == 30, "NLP data generation failed"
        assert 'transcript' in nlp_df.columns, "Transcript missing"
        assert nlp_df['overall_quality_score'].between(0, 100).all(), "Quality scores out of range"
        
        logger.info(f"✓ NLP data generated: {len(nlp_df)} samples")
    
    def test_data_quality_checks(self):
        """Test data quality validation."""
        df = self.data_generator.generate_conversion_data(500)
        
        # Check for missing values
        missing_counts = df.isnull().sum()
        high_missing = missing_counts[missing_counts > len(df) * 0.1]
        
        if len(high_missing) > 0:
            logger.warning(f"⚠ Columns with >10% missing values: {dict(high_missing)}")
        else:
            logger.info("✓ No excessive missing values")
        
        # Check for data leakage patterns
        suspicious_columns = []
        for col in df.columns:
            if any(pattern in col.lower() for pattern in ['converted', 'target', 'label']):
                if col != 'converted':  # Allow the actual target
                    suspicious_columns.append(col)
        
        if suspicious_columns:
            logger.warning(f"⚠ Potentially leaky columns: {suspicious_columns}")
        else:
            logger.info("✓ No obvious data leakage patterns")
        
        # Check feature correlations
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        if len(numeric_cols) > 1:
            corr_matrix = df[numeric_cols].corr()
            
            # Find high correlations (excluding target)
            high_corr_pairs = []
            for i in range(len(corr_matrix.columns)):
                for j in range(i+1, len(corr_matrix.columns)):
                    col1, col2 = corr_matrix.columns[i], corr_matrix.columns[j]
                    if col1 != 'converted' and col2 != 'converted':
                        if abs(corr_matrix.iloc[i, j]) > 0.9:
                            high_corr_pairs.append((col1, col2, corr_matrix.iloc[i, j]))
            
            if high_corr_pairs:
                logger.warning(f"⚠ High correlations found: {high_corr_pairs[:3]}")
            else:
                logger.info("✓ No excessive feature correlations")
    
    def test_model_training_with_mock_data(self):
        """Test model training with synthetic data."""
        from sklearn.ensemble import RandomForestClassifier
        from sklearn.model_selection import cross_val_score
        
        # Generate training data
        df = self.data_generator.generate_conversion_data(800)
        
        # Prepare features
        feature_cols = [col for col in df.columns if col not in ['id_contact', 'converted']]
        numeric_features = df[feature_cols].select_dtypes(include=[np.number]).columns.tolist()
        
        X = df[numeric_features].fillna(0)
        y = df['converted']
        
        logger.info(f"Training with {len(X)} samples, {len(numeric_features)} features")
        logger.info(f"Class distribution: {dict(y.value_counts())}")
        
        # Test different model complexities
        models = {
            'simple': RandomForestClassifier(n_estimators=10, max_depth=3, random_state=42),
            'medium': RandomForestClassifier(n_estimators=50, max_depth=6, random_state=42),
            'complex': RandomForestClassifier(n_estimators=100, max_depth=None, random_state=42)
        }
        
        results = {}
        
        for name, model in models.items():
            # Cross-validation
            cv_scores = cross_val_score(model, X, y, cv=5, scoring='roc_auc')
            
            results[name] = {
                'mean_auc': cv_scores.mean(),
                'std_auc': cv_scores.std(),
                'scores': cv_scores
            }
            
            logger.info(f"  {name}: AUC = {cv_scores.mean():.3f} ± {cv_scores.std():.3f}")
        
        # Check that models perform reasonably
        for name, result in results.items():
            assert result['mean_auc'] > 0.55, f"{name} model AUC too low: {result['mean_auc']:.3f}"
        
        # Check for overfitting (complex model shouldn't be much better than simple)
        simple_auc = results['simple']['mean_auc']
        complex_auc = results['complex']['mean_auc']
        
        if complex_auc - simple_auc > 0.1:
            logger.warning(f"⚠ Possible overfitting: complex model much better than simple ({complex_auc:.3f} vs {simple_auc:.3f})")
        else:
            logger.info("✓ No obvious overfitting detected")
        
        logger.info("✓ Model training validation completed")
    
    def test_hyperparameter_optimization_mock(self):
        """Test hyperparameter optimization with mock data."""
        try:
            import optuna
            from sklearn.ensemble import RandomForestClassifier
            from sklearn.model_selection import cross_val_score
            
            # Generate data
            df = self.data_generator.generate_conversion_data(400)
            
            # Prepare features
            numeric_features = df.select_dtypes(include=[np.number]).columns.tolist()
            numeric_features = [col for col in numeric_features if col != 'converted']
            
            X = df[numeric_features].fillna(0)
            y = df['converted']
            
            logger.info(f"Hyperparameter optimization with {len(X)} samples")
            
            def objective(trial):
                n_estimators = trial.suggest_int('n_estimators', 10, 100)
                max_depth = trial.suggest_int('max_depth', 3, 10)
                min_samples_split = trial.suggest_int('min_samples_split', 2, 10)
                
                model = RandomForestClassifier(
                    n_estimators=n_estimators,
                    max_depth=max_depth,
                    min_samples_split=min_samples_split,
                    random_state=42
                )
                
                scores = cross_val_score(model, X, y, cv=3, scoring='roc_auc')
                return scores.mean()
            
            # Run optimization
            study = optuna.create_study(direction='maximize')
            study.optimize(objective, n_trials=15, show_progress_bar=False)
            
            logger.info(f"✓ Optimization completed: best AUC = {study.best_value:.3f}")
            logger.info(f"  Best params: {study.best_params}")
            
            # Test best model
            best_model = RandomForestClassifier(**study.best_params, random_state=42)
            best_scores = cross_val_score(best_model, X, y, cv=3, scoring='roc_auc')
            
            assert best_scores.mean() > 0.55, f"Optimized model AUC too low: {best_scores.mean():.3f}"
            logger.info(f"✓ Best model validation: AUC = {best_scores.mean():.3f} ± {best_scores.std():.3f}")
            
        except ImportError:
            pytest.skip("Optuna not available")


if __name__ == "__main__":
    pytest.main([__file__, "-v", "-s"])
