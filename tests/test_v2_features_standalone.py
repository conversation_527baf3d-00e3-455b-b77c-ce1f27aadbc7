"""
Test standalone des features v2.0 sans dépendances API.

Test les nouvelles features et le monitoring de manière isolée.
"""

import pytest
import os
import pandas as pd
import numpy as np
from pathlib import Path
import logging
from typing import List
from google.cloud import bigquery
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split
from sklearn.metrics import roc_auc_score, accuracy_score
import warnings
warnings.filterwarnings("ignore")

logger = logging.getLogger(__name__)


class StandaloneAdvancedFeatureEngineer:
    """Version standalone du feature engineer avancé."""
    
    def __init__(self, client: bigquery.Client):
        self.client = client
    
    def extract_typeform_features(self, contact_ids: List[str]) -> pd.DataFrame:
        """Extrait les features Typeform."""
        if not contact_ids:
            return pd.DataFrame()
        
        ids_str = "', '".join(str(id) for id in contact_ids)
        
        query = f"""
        SELECT 
            sf.id_prospect as id_contact,
            COUNT(DISTINCT sf.id_soumission_formulaire) as nb_soumissions_typeform,
            AVG(sf.duree_reponses_minutes) as duree_moyenne_typeform,
            COUNT(DISTINCT r.id_reponse) as nb_reponses_typeform
        FROM `datalake-sensei.serving_layer.vw_fact_soumission_formulaire` sf
        LEFT JOIN `datalake-sensei.serving_layer.vw_dim_reponse` r 
            ON sf.id_soumission_formulaire = r.id_soumission_formulaire
        WHERE sf.id_prospect IN ('{ids_str}')
        GROUP BY sf.id_prospect
        """
        
        try:
            result = self.client.query(query).result()
            df = result.to_dataframe()
            
            if len(df) > 0:
                df['id_contact'] = df['id_contact'].astype(str)
                logger.info(f"✅ Features Typeform: {len(df)} contacts")
            
            return df
        except Exception as e:
            logger.warning(f"⚠️ Erreur Typeform: {e}")
            return pd.DataFrame()
    
    def extract_modjo_features(self, contact_ids: List[str]) -> pd.DataFrame:
        """Extrait les features Modjo."""
        if not contact_ids:
            return pd.DataFrame()
        
        ids_str = "', '".join(str(id) for id in contact_ids)
        
        query = f"""
        SELECT 
            fc.contactCrmId as id_contact,
            COUNT(DISTINCT fc.callId) as nb_appels_modjo,
            AVG(fc.duration) as duree_moyenne_modjo,
            COUNT(DISTINCT fc.dealId) as nb_deals_modjo
        FROM `datalake-sensei.serving_layer.vw_fact_modjo_call` fc
        WHERE fc.contactCrmId IN ('{ids_str}')
        GROUP BY fc.contactCrmId
        """
        
        try:
            result = self.client.query(query).result()
            df = result.to_dataframe()
            
            if len(df) > 0:
                df['id_contact'] = df['id_contact'].astype(str)
                logger.info(f"✅ Features Modjo: {len(df)} contacts")
            
            return df
        except Exception as e:
            logger.warning(f"⚠️ Erreur Modjo: {e}")
            return pd.DataFrame()
    
    def create_advanced_features(self, base_df: pd.DataFrame) -> pd.DataFrame:
        """Crée des features avancées."""
        logger.info("🚀 Création des features avancées...")
        
        if 'id_contact' not in base_df.columns:
            return base_df
        
        contact_ids = base_df['id_contact'].astype(str).tolist()
        result_df = base_df.copy()
        
        # Features Typeform
        typeform_features = self.extract_typeform_features(contact_ids)
        if len(typeform_features) > 0:
            result_df = result_df.merge(typeform_features, on='id_contact', how='left')
        
        # Features Modjo
        modjo_features = self.extract_modjo_features(contact_ids)
        if len(modjo_features) > 0:
            result_df = result_df.merge(modjo_features, on='id_contact', how='left')
        
        # Features de synthèse
        if 'nb_soumissions_typeform' in result_df.columns and 'nb_appels_modjo' in result_df.columns:
            result_df['engagement_multi_canal'] = (
                result_df['nb_soumissions_typeform'].fillna(0) + 
                result_df['nb_appels_modjo'].fillna(0)
            )
        
        logger.info(f"✅ Features avancées: {len(result_df.columns)} colonnes")
        return result_df


class StandaloneMLMonitoring:
    """Version standalone du monitoring ML."""
    
    def __init__(self):
        self.metrics = {}
    
    def evaluate_model(self, model, X_test, y_test, feature_names=None):
        """Évalue le modèle."""
        y_pred = model.predict(X_test)
        y_pred_proba = model.predict_proba(X_test)[:, 1] if hasattr(model, 'predict_proba') else None
        
        metrics = {
            'accuracy': accuracy_score(y_test, y_pred),
            'test_size': len(X_test)
        }
        
        if y_pred_proba is not None and y_test.nunique() > 1:
            metrics['auc'] = roc_auc_score(y_test, y_pred_proba)
        
        if hasattr(model, 'feature_importances_') and feature_names:
            importance = dict(zip(feature_names, model.feature_importances_))
            metrics['top_features'] = dict(sorted(importance.items(), key=lambda x: x[1], reverse=True)[:5])
        
        self.metrics = metrics
        return metrics
    
    def get_recommendations(self):
        """Génère des recommandations."""
        recommendations = []
        
        if self.metrics.get('auc', 0) < 0.8:
            recommendations.append("Considérer l'ajout de nouvelles features")
        
        if self.metrics.get('accuracy', 0) < 0.75:
            recommendations.append("Optimiser les hyperparamètres")
        
        if not recommendations:
            recommendations.append("Modèle performant")
        
        return recommendations


class TestV2FeaturesStandalone:
    """Test standalone des features v2.0."""
    
    def setup_method(self):
        """Setup pour chaque test."""
        credentials_path = os.getenv('GOOGLE_APPLICATION_CREDENTIALS')
        if not credentials_path or not os.path.exists(credentials_path):
            pytest.skip("Credentials BigQuery non configurés")
        
        self.client = bigquery.Client(project="datalake-sensei")
        logger.info(f"Credentials: {credentials_path}")
    
    def test_advanced_features_extraction(self):
        """Test d'extraction des features avancées."""
        try:
            from tests.test_real_ml_pipeline import RealDataProcessor
            
            logger.info("🚀 Test extraction features avancées...")
            
            # Données de base
            query = """
            SELECT 
                c.id_contact,
                c.hubspotscore,
                c.num_conversion_events,
                c.num_associated_deals,
                c.statut_du_lead,
                c.ip_country,
                c.dt_creation_contact
            FROM `datalake-sensei.serving_layer.vw_dim_contact` c
            WHERE c.id_contact IS NOT NULL
            LIMIT 100
            """
            
            result = self.client.query(query).result()
            base_df = result.to_dataframe()
            
            logger.info(f"📊 Données de base: {len(base_df)} lignes")
            
            # Traitement de base
            processor = RealDataProcessor()
            base_df = processor.fix_data_types(base_df)
            base_df = processor.create_derived_features(base_df)
            base_df = processor.encode_categorical_features(base_df, fit=True)
            base_df = processor.create_target_variable(base_df)
            
            # Features avancées
            engineer = StandaloneAdvancedFeatureEngineer(self.client)
            advanced_df = engineer.create_advanced_features(base_df)
            
            # Analyser les résultats
            new_columns = set(advanced_df.columns) - set(base_df.columns)
            
            logger.info(f"✅ Résultats:")
            logger.info(f"  - Colonnes de base: {len(base_df.columns)}")
            logger.info(f"  - Colonnes avancées: {len(advanced_df.columns)}")
            logger.info(f"  - Nouvelles features: {len(new_columns)}")
            
            for col in new_columns:
                non_null = advanced_df[col].notna().sum()
                logger.info(f"    * {col}: {non_null}/{len(advanced_df)} valeurs")
            
            # Vérifications
            assert len(advanced_df.columns) >= len(base_df.columns), "Pas de nouvelles features"
            
            return {
                'base_features': len(base_df.columns),
                'advanced_features': len(advanced_df.columns),
                'new_features': len(new_columns),
                'advanced_df': advanced_df
            }
            
        except Exception as e:
            logger.error(f"Erreur extraction features: {e}")
            pytest.fail(f"Extraction features échouée: {e}")
    
    def test_ml_with_advanced_features(self):
        """Test ML avec features avancées."""
        try:
            # Obtenir les features
            feature_result = self.test_advanced_features_extraction()
            df = feature_result['advanced_df']
            
            logger.info("🤖 Test ML avec features avancées...")
            
            # Sélectionner les features
            feature_columns = [
                'hubspotscore', 'num_conversion_events', 'engagement_ratio',
                'days_since_creation', 'composite_score'
            ]
            
            # Ajouter les nouvelles features si disponibles
            new_feature_columns = [
                'nb_soumissions_typeform', 'nb_appels_modjo', 'engagement_multi_canal'
            ]
            
            for col in new_feature_columns:
                if col in df.columns:
                    feature_columns.append(col)
            
            available_features = [col for col in feature_columns if col in df.columns]
            
            logger.info(f"📋 Features utilisées: {len(available_features)}")
            for feat in available_features:
                logger.info(f"  - {feat}")
            
            # Préparer les données
            X = df[available_features].fillna(0)
            y = df['converted']
            
            if y.nunique() < 2:
                logger.warning("⚠️ Variable cible non variée")
                return None
            
            # Split et entraînement
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, random_state=42
            )
            
            model = RandomForestClassifier(
                n_estimators=100,
                max_depth=10,
                class_weight='balanced',
                random_state=42
            )
            
            model.fit(X_train, y_train)
            
            # Évaluation
            y_pred_proba = model.predict_proba(X_test)[:, 1]
            auc = roc_auc_score(y_test, y_pred_proba) if y_test.nunique() > 1 else 0.5
            accuracy = accuracy_score(y_test, model.predict(X_test))
            
            # Feature importance
            importance = pd.DataFrame({
                'feature': available_features,
                'importance': model.feature_importances_
            }).sort_values('importance', ascending=False)
            
            logger.info(f"✅ Modèle entraîné:")
            logger.info(f"  - AUC: {auc:.3f}")
            logger.info(f"  - Accuracy: {accuracy:.3f}")
            logger.info(f"  - Features: {len(available_features)}")
            
            logger.info("🔝 Top 5 features:")
            for _, row in importance.head().iterrows():
                logger.info(f"  📊 {row['feature']}: {row['importance']:.4f}")
            
            return {
                'model': model,
                'auc': auc,
                'accuracy': accuracy,
                'features': available_features,
                'importance': importance,
                'X_test': X_test,
                'y_test': y_test
            }
            
        except Exception as e:
            logger.error(f"Erreur ML avancé: {e}")
            pytest.fail(f"ML avancé échoué: {e}")
    
    def test_monitoring_system(self):
        """Test du système de monitoring."""
        try:
            # Obtenir le modèle
            ml_result = self.test_ml_with_advanced_features()
            
            if ml_result is None:
                logger.warning("⚠️ Pas de modèle disponible")
                return None
            
            logger.info("📊 Test du monitoring...")
            
            # Monitoring
            monitoring = StandaloneMLMonitoring()
            
            evaluation = monitoring.evaluate_model(
                ml_result['model'],
                ml_result['X_test'],
                ml_result['y_test'],
                ml_result['features']
            )
            
            recommendations = monitoring.get_recommendations()
            
            logger.info(f"✅ Monitoring:")
            logger.info(f"  - AUC: {evaluation.get('auc', 'N/A')}")
            logger.info(f"  - Accuracy: {evaluation.get('accuracy', 'N/A'):.3f}")
            logger.info(f"  - Échantillons test: {evaluation.get('test_size', 0)}")
            
            logger.info("💡 Recommandations:")
            for rec in recommendations:
                logger.info(f"  - {rec}")
            
            return {
                'evaluation': evaluation,
                'recommendations': recommendations
            }
            
        except Exception as e:
            logger.error(f"Erreur monitoring: {e}")
            pytest.fail(f"Monitoring échoué: {e}")
    
    def test_complete_v2_pipeline_standalone(self):
        """Test du pipeline complet v2.0 standalone."""
        try:
            logger.info("🎯 Test pipeline v2.0 standalone...")
            
            # 1. Features avancées
            feature_result = self.test_advanced_features_extraction()
            logger.info("✅ 1/3 - Features avancées OK")
            
            # 2. ML avec features avancées
            ml_result = self.test_ml_with_advanced_features()
            logger.info("✅ 2/3 - ML avancé OK")
            
            # 3. Monitoring
            monitoring_result = self.test_monitoring_system()
            logger.info("✅ 3/3 - Monitoring OK")
            
            # Résumé
            logger.info("🎉 PIPELINE V2.0 STANDALONE VALIDÉ !")
            logger.info(f"📊 Résumé:")
            logger.info(f"  - Features: {feature_result['base_features']} → {feature_result['advanced_features']}")
            logger.info(f"  - Nouvelles features: {feature_result['new_features']}")
            logger.info(f"  - AUC: {ml_result['auc']:.3f}")
            logger.info(f"  - Accuracy: {ml_result['accuracy']:.3f}")
            
            return {
                'features': feature_result,
                'ml': ml_result,
                'monitoring': monitoring_result,
                'status': 'SUCCESS'
            }
            
        except Exception as e:
            logger.error(f"Erreur pipeline v2.0: {e}")
            pytest.fail(f"Pipeline v2.0 échoué: {e}")


if __name__ == "__main__":
    # Configurer les credentials
    if not os.getenv('GOOGLE_APPLICATION_CREDENTIALS'):
        credentials_path = Path(__file__).parent.parent / "credentials" / "sensei-ai-service-account.json"
        if credentials_path.exists():
            os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = str(credentials_path)
    
    pytest.main([__file__, "-v", "-s"])
