"""
Test du système ML avancé v2.0 avec nouvelles features et monitoring.

Intègre Typeform, Modjo, engagement multi-canal et monitoring complet.
"""

import pytest
import os
import pandas as pd
import numpy as np
from pathlib import Path
import logging
from google.cloud import bigquery
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split
from sklearn.metrics import roc_auc_score, accuracy_score
import warnings
warnings.filterwarnings("ignore")

logger = logging.getLogger(__name__)


class TestAdvancedMLv2:
    """Test du système ML avancé v2.0."""
    
    def setup_method(self):
        """Setup pour chaque test."""
        credentials_path = os.getenv('GOOGLE_APPLICATION_CREDENTIALS')
        if not credentials_path or not os.path.exists(credentials_path):
            pytest.skip("Credentials BigQuery non configurés")
        
        self.client = bigquery.Client(project="datalake-sensei")
        logger.info(f"Credentials: {credentials_path}")
    
    def test_advanced_feature_engineering(self):
        """Test du feature engineering avancé."""
        try:
            from sensei.features.advanced_feature_engineering import AdvancedFeatureEngineer
            from tests.test_real_ml_pipeline import RealDataProcessor
            
            logger.info("🚀 Test du feature engineering avancé...")
            
            # Extraire des données de base
            query = """
            SELECT 
                c.id_contact,
                c.hubspotscore,
                c.num_conversion_events,
                c.num_associated_deals,
                c.hs_analytics_num_page_views,
                c.statut_du_lead,
                c.ip_country,
                c.dt_creation_contact
            FROM `datalake-sensei.serving_layer.vw_dim_contact` c
            WHERE c.id_contact IS NOT NULL
            LIMIT 200
            """
            
            result = self.client.query(query).result()
            base_df = result.to_dataframe()
            
            logger.info(f"📊 Données de base: {len(base_df)} lignes, {len(base_df.columns)} colonnes")
            
            # Traitement de base
            processor = RealDataProcessor()
            base_df = processor.fix_data_types(base_df)
            base_df = processor.create_derived_features(base_df)
            base_df = processor.encode_categorical_features(base_df, fit=True)
            base_df = processor.create_target_variable(base_df)
            
            # Feature engineering avancé
            engineer = AdvancedFeatureEngineer(self.client)
            advanced_df = engineer.create_advanced_features(base_df)
            
            logger.info(f"✅ Features avancées: {len(advanced_df)} lignes, {len(advanced_df.columns)} colonnes")
            
            # Analyser les nouvelles features
            new_columns = set(advanced_df.columns) - set(base_df.columns)
            logger.info(f"🆕 Nouvelles features: {len(new_columns)}")
            
            for col in list(new_columns)[:10]:  # Afficher les 10 premières
                non_null_count = advanced_df[col].notna().sum()
                logger.info(f"  - {col}: {non_null_count}/{len(advanced_df)} valeurs non-nulles")
            
            # Vérifications
            assert len(advanced_df.columns) > len(base_df.columns), "Aucune nouvelle feature créée"
            assert len(new_columns) >= 5, f"Pas assez de nouvelles features: {len(new_columns)}"
            
            # Analyser l'importance des features
            feature_analysis = engineer.get_feature_importance_analysis()
            logger.info(f"📈 Analyse d'importance: {len(feature_analysis['high_impact_features'])} features à fort impact")
            
            return {
                'base_features': len(base_df.columns),
                'advanced_features': len(advanced_df.columns),
                'new_features': len(new_columns),
                'sample_size': len(advanced_df),
                'feature_analysis': feature_analysis,
                'advanced_df': advanced_df
            }
            
        except Exception as e:
            logger.error(f"Erreur feature engineering avancé: {e}")
            pytest.fail(f"Feature engineering avancé échoué: {e}")
    
    def test_ml_model_with_advanced_features(self):
        """Test d'entraînement ML avec features avancées."""
        try:
            # Obtenir les features avancées
            feature_result = self.test_advanced_feature_engineering()
            df = feature_result['advanced_df']
            
            logger.info("🤖 Entraînement ML avec features avancées...")
            
            # Sélectionner les features pour le ML
            feature_columns = [
                # Features de base
                'hubspotscore', 'num_conversion_events', 'engagement_ratio',
                'days_since_creation', 'composite_score',
                # Features avancées (si disponibles)
                'nb_soumissions', 'rating_moyen', 'nb_appels', 'score_modjo_composite',
                'engagement_global_score', 'qualite_interactions_score', 'maturite_prospect'
            ]
            
            # Filtrer les colonnes existantes
            available_features = [col for col in feature_columns if col in df.columns]
            logger.info(f"📋 Features disponibles: {len(available_features)}")
            
            if len(available_features) < 5:
                logger.warning("⚠️ Pas assez de features pour ML robuste")
                return None
            
            # Préparer les données
            X = df[available_features].fillna(0)
            y = df['converted']
            
            conversion_rate = y.mean()
            logger.info(f"🎯 Taux de conversion: {conversion_rate:.1%}")
            
            if y.nunique() < 2:
                logger.warning("⚠️ Variable cible non variée")
                return None
            
            # Split des données
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, random_state=42
            )
            
            # Entraîner le modèle
            model = RandomForestClassifier(
                n_estimators=150,
                max_depth=12,
                min_samples_split=8,
                class_weight='balanced',
                random_state=42
            )
            
            model.fit(X_train, y_train)
            
            # Évaluer
            y_pred_proba = model.predict_proba(X_test)[:, 1]
            y_pred = model.predict(X_test)
            
            if y_test.nunique() > 1:
                auc = roc_auc_score(y_test, y_pred_proba)
                accuracy = accuracy_score(y_test, y_pred)
            else:
                auc = 0.5
                accuracy = 0.5
            
            # Feature importance
            feature_importance = pd.DataFrame({
                'feature': available_features,
                'importance': model.feature_importances_
            }).sort_values('importance', ascending=False)
            
            logger.info(f"✅ Modèle v2.0 entraîné:")
            logger.info(f"  - AUC: {auc:.3f}")
            logger.info(f"  - Accuracy: {accuracy:.3f}")
            logger.info(f"  - Features: {len(available_features)}")
            
            logger.info("🔝 Top 5 features importantes:")
            for _, row in feature_importance.head().iterrows():
                logger.info(f"  📊 {row['feature']}: {row['importance']:.4f}")
            
            # Vérifications
            assert auc >= 0.5, f"AUC trop faible: {auc:.3f}"
            assert len(available_features) >= 5, f"Pas assez de features: {len(available_features)}"
            
            return {
                'model': model,
                'auc': auc,
                'accuracy': accuracy,
                'features': available_features,
                'feature_importance': feature_importance,
                'X_train': X_train,
                'X_test': X_test,
                'y_train': y_train,
                'y_test': y_test,
                'conversion_rate': conversion_rate
            }
            
        except Exception as e:
            logger.error(f"Erreur ML avancé: {e}")
            pytest.fail(f"ML avancé échoué: {e}")
    
    def test_ml_monitoring_system(self):
        """Test du système de monitoring ML."""
        try:
            from sensei.monitoring.ml_monitoring import MLMonitoringSystem
            
            # Obtenir le modèle entraîné
            ml_result = self.test_ml_model_with_advanced_features()
            
            if ml_result is None:
                logger.warning("⚠️ Pas de modèle ML disponible pour monitoring")
                return None
            
            logger.info("📊 Test du système de monitoring ML...")
            
            # Initialiser le monitoring
            monitoring = MLMonitoringSystem("sensei_v2_test", "test_monitoring_output")
            
            # Évaluation complète
            evaluation = monitoring.evaluate_model_comprehensive(
                ml_result['model'],
                ml_result['X_test'],
                ml_result['y_test'],
                ml_result['features']
            )
            
            logger.info(f"✅ Évaluation complète:")
            logger.info(f"  - AUC: {evaluation.get('auc_roc', 'N/A')}")
            logger.info(f"  - Accuracy: {evaluation.get('accuracy', 'N/A'):.3f}")
            logger.info(f"  - Precision: {evaluation.get('precision', 'N/A'):.3f}")
            logger.info(f"  - Recall: {evaluation.get('recall', 'N/A'):.3f}")
            
            # Explication des prédictions
            explanation = monitoring.explain_predictions(
                ml_result['model'],
                ml_result['X_test'],
                ml_result['features'],
                n_samples=30
            )
            
            if 'error' not in explanation:
                top_shap_feature = list(explanation.get('top_shap_features', {}).keys())[0] if explanation.get('top_shap_features') else 'N/A'
                logger.info(f"🔍 Explication SHAP:")
                logger.info(f"  - Top feature SHAP: {top_shap_feature}")
                logger.info(f"  - Échantillons analysés: {explanation.get('sample_size', 0)}")
            
            # Monitoring de dérive
            drift_analysis = monitoring.monitor_data_drift(
                ml_result['X_train'],
                ml_result['X_test'],
                ml_result['features']
            )
            
            logger.info(f"📈 Analyse de dérive:")
            logger.info(f"  - Features dérivées: {len(drift_analysis.get('drifted_features', []))}")
            logger.info(f"  - Score de dérive moyen: {drift_analysis.get('drift_summary', {}).get('mean_drift_score', 0):.3f}")
            
            # Rapport complet
            report = monitoring.generate_model_report(
                ml_result['model'],
                ml_result['X_test'],
                ml_result['y_test'],
                ml_result['X_train'],
                ml_result['features']
            )
            
            logger.info(f"📋 Rapport généré:")
            logger.info(f"  - Recommandations: {len(report.get('recommendations', []))}")
            
            for rec in report.get('recommendations', [])[:3]:
                logger.info(f"    💡 {rec}")
            
            # Dashboard data
            dashboard_data = monitoring.get_monitoring_dashboard_data()
            health_score = dashboard_data.get('model_health_score', 0)
            
            logger.info(f"🏥 Score de santé du modèle: {health_score:.3f}")
            
            # Vérifications
            assert 'evaluation' in report, "Évaluation manquante dans le rapport"
            assert 'recommendations' in report, "Recommandations manquantes"
            assert health_score > 0, f"Score de santé invalide: {health_score}"
            
            return {
                'evaluation': evaluation,
                'explanation': explanation,
                'drift_analysis': drift_analysis,
                'report': report,
                'health_score': health_score,
                'monitoring_system': monitoring
            }
            
        except Exception as e:
            logger.error(f"Erreur monitoring ML: {e}")
            pytest.fail(f"Monitoring ML échoué: {e}")
    
    def test_complete_v2_pipeline(self):
        """Test du pipeline complet v2.0."""
        try:
            logger.info("🎯 Test du pipeline complet v2.0...")
            
            # 1. Feature engineering avancé
            feature_result = self.test_advanced_feature_engineering()
            logger.info("✅ 1/3 - Feature engineering avancé OK")
            
            # 2. ML avec features avancées
            ml_result = self.test_ml_model_with_advanced_features()
            logger.info("✅ 2/3 - ML avec features avancées OK")
            
            # 3. Monitoring complet
            monitoring_result = self.test_ml_monitoring_system()
            logger.info("✅ 3/3 - Monitoring complet OK")
            
            # Résumé final
            logger.info("🎉 PIPELINE V2.0 COMPLET VALIDÉ !")
            logger.info(f"📊 Résumé:")
            logger.info(f"  - Features: {feature_result['base_features']} → {feature_result['advanced_features']}")
            logger.info(f"  - AUC ML: {ml_result['auc']:.3f}")
            logger.info(f"  - Santé modèle: {monitoring_result['health_score']:.3f}")
            logger.info(f"  - Nouvelles features: {feature_result['new_features']}")
            
            return {
                'feature_engineering': feature_result,
                'ml_training': ml_result,
                'monitoring': monitoring_result,
                'pipeline_status': 'SUCCESS'
            }
            
        except Exception as e:
            logger.error(f"Erreur pipeline v2.0: {e}")
            pytest.fail(f"Pipeline v2.0 échoué: {e}")


if __name__ == "__main__":
    # Configurer les credentials
    if not os.getenv('GOOGLE_APPLICATION_CREDENTIALS'):
        credentials_path = Path(__file__).parent.parent / "credentials" / "sensei-ai-service-account.json"
        if credentials_path.exists():
            os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = str(credentials_path)
    
    pytest.main([__file__, "-v", "-s"])
