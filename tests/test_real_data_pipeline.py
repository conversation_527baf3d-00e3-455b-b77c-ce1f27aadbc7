"""
Test du pipeline avec données réelles BigQuery.

Teste le feature engineering et l'entraînement de modèles avec les vraies données.
"""

import pytest
import os
import pandas as pd
import numpy as np
from pathlib import Path
import logging
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import accuracy_score, roc_auc_score, classification_report

logger = logging.getLogger(__name__)


class TestRealDataPipeline:
    """Test du pipeline avec données réelles."""
    
    def setup_method(self):
        """Setup pour chaque test."""
        # Vérifier les credentials
        credentials_path = os.getenv('GOOGLE_APPLICATION_CREDENTIALS')
        if not credentials_path or not os.path.exists(credentials_path):
            pytest.skip("Credentials BigQuery non configurés")
        
        logger.info(f"Credentials: {credentials_path}")
    
    def test_contact_data_exploration(self):
        """Test d'exploration des données de contact."""
        try:
            from google.cloud import bigquery
            
            client = bigquery.Client(project="datalake-sensei")
            
            # Explorer la table des contacts
            query = """
            SELECT *
            FROM `datalake-sensei.serving_layer.vw_dim_contact`
            LIMIT 10
            """
            
            query_job = client.query(query)
            results = query_job.result()
            df = results.to_dataframe()
            
            logger.info(f"✓ Données contact explorées: {len(df)} lignes, {len(df.columns)} colonnes")
            logger.info(f"  Colonnes: {list(df.columns)}")
            
            # Analyser les types de données
            for col in df.columns:
                dtype = df[col].dtype
                null_count = df[col].isnull().sum()
                logger.info(f"  - {col}: {dtype}, {null_count} nulls")
            
            assert len(df) > 0, "Aucune donnée contact"
            assert len(df.columns) > 5, "Pas assez de colonnes"
            
            return df
            
        except Exception as e:
            logger.error(f"Erreur exploration contact: {e}")
            pytest.fail(f"Impossible d'explorer les données contact: {e}")
    
    def test_leads_data_exploration(self):
        """Test d'exploration des données de leads."""
        try:
            from google.cloud import bigquery
            
            client = bigquery.Client(project="datalake-sensei")
            
            # Explorer la table des leads
            query = """
            SELECT *
            FROM `datalake-sensei.serving_layer.vw_dim_leads`
            LIMIT 10
            """
            
            query_job = client.query(query)
            results = query_job.result()
            df = results.to_dataframe()
            
            logger.info(f"✓ Données leads explorées: {len(df)} lignes, {len(df.columns)} colonnes")
            logger.info(f"  Colonnes: {list(df.columns)}")
            
            # Vérifier s'il y a des colonnes de conversion
            conversion_cols = [col for col in df.columns if any(keyword in col.lower() 
                             for keyword in ['convert', 'deal', 'won', 'close', 'status'])]
            
            if conversion_cols:
                logger.info(f"  Colonnes de conversion potentielles: {conversion_cols}")
            
            assert len(df) > 0, "Aucune donnée leads"
            
            return df
            
        except Exception as e:
            logger.error(f"Erreur exploration leads: {e}")
            pytest.fail(f"Impossible d'explorer les données leads: {e}")
    
    def test_email_engagement_data(self):
        """Test des données d'engagement email."""
        try:
            from google.cloud import bigquery
            
            client = bigquery.Client(project="datalake-sensei")
            
            # Explorer la table des emails
            query = """
            SELECT *
            FROM `datalake-sensei.serving_layer.vw_dim_email`
            LIMIT 10
            """
            
            query_job = client.query(query)
            results = query_job.result()
            df = results.to_dataframe()
            
            logger.info(f"✓ Données email explorées: {len(df)} lignes, {len(df.columns)} colonnes")
            logger.info(f"  Colonnes: {list(df.columns)}")
            
            # Chercher des métriques d'engagement
            engagement_cols = [col for col in df.columns if any(keyword in col.lower() 
                             for keyword in ['open', 'click', 'reply', 'bounce', 'sent'])]
            
            if engagement_cols:
                logger.info(f"  Colonnes d'engagement: {engagement_cols}")
            
            assert len(df) > 0, "Aucune donnée email"
            
            return df
            
        except Exception as e:
            logger.error(f"Erreur exploration email: {e}")
            pytest.fail(f"Impossible d'explorer les données email: {e}")
    
    def test_call_data_exploration(self):
        """Test des données d'appels."""
        try:
            from google.cloud import bigquery
            
            client = bigquery.Client(project="datalake-sensei")
            
            # Explorer la table des appels
            query = """
            SELECT *
            FROM `datalake-sensei.serving_layer.vw_dim_appel`
            LIMIT 10
            """
            
            query_job = client.query(query)
            results = query_job.result()
            df = results.to_dataframe()
            
            logger.info(f"✓ Données appel explorées: {len(df)} lignes, {len(df.columns)} colonnes")
            logger.info(f"  Colonnes: {list(df.columns)}")
            
            # Chercher des métriques d'appels
            call_cols = [col for col in df.columns if any(keyword in col.lower() 
                        for keyword in ['duration', 'outcome', 'status', 'result'])]
            
            if call_cols:
                logger.info(f"  Colonnes d'appel: {call_cols}")
            
            assert len(df) > 0, "Aucune donnée appel"
            
            return df
            
        except Exception as e:
            logger.error(f"Erreur exploration appel: {e}")
            pytest.fail(f"Impossible d'explorer les données appel: {e}")
    
    def test_feature_engineering_basic(self):
        """Test de feature engineering de base avec données réelles."""
        try:
            from google.cloud import bigquery
            
            client = bigquery.Client(project="datalake-sensei")
            
            # Créer un dataset simple pour le ML
            query = """
            SELECT 
                c.id as contact_id,
                c.* EXCEPT(id),
                l.* EXCEPT(id) as lead_data
            FROM `datalake-sensei.serving_layer.vw_dim_contact` c
            LEFT JOIN `datalake-sensei.serving_layer.vw_dim_leads` l 
                ON c.id = l.contact_id
            LIMIT 100
            """
            
            query_job = client.query(query)
            results = query_job.result()
            df = results.to_dataframe()
            
            logger.info(f"✓ Dataset ML créé: {len(df)} lignes, {len(df.columns)} colonnes")
            
            # Feature engineering de base
            numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
            categorical_cols = df.select_dtypes(include=['object', 'string']).columns.tolist()
            
            logger.info(f"  - Colonnes numériques: {len(numeric_cols)}")
            logger.info(f"  - Colonnes catégorielles: {len(categorical_cols)}")
            
            # Créer des features dérivées
            if len(numeric_cols) > 0:
                # Normalisation simple
                for col in numeric_cols[:3]:  # Première 3 colonnes numériques
                    if df[col].std() > 0:
                        df[f'{col}_normalized'] = (df[col] - df[col].mean()) / df[col].std()
            
            # Encodage catégoriel simple
            if len(categorical_cols) > 0:
                for col in categorical_cols[:2]:  # Première 2 colonnes catégorielles
                    if df[col].nunique() < 10:  # Seulement si peu de valeurs uniques
                        df = pd.get_dummies(df, columns=[col], prefix=col)
            
            logger.info(f"✓ Feature engineering: {len(df.columns)} colonnes finales")
            
            assert len(df) > 50, "Pas assez de données après feature engineering"
            
            return df
            
        except Exception as e:
            logger.error(f"Erreur feature engineering: {e}")
            pytest.fail(f"Feature engineering échoué: {e}")
    
    def test_ml_model_with_real_data(self):
        """Test d'entraînement ML avec données réelles."""
        try:
            from google.cloud import bigquery
            
            client = bigquery.Client(project="datalake-sensei")
            
            # Créer un dataset avec une variable cible synthétique
            query = """
            SELECT 
                c.*,
                CASE 
                    WHEN RAND() > 0.7 THEN 1 
                    ELSE 0 
                END as synthetic_target
            FROM `datalake-sensei.serving_layer.vw_dim_contact` c
            LIMIT 200
            """
            
            query_job = client.query(query)
            results = query_job.result()
            df = results.to_dataframe()
            
            logger.info(f"✓ Dataset ML avec target: {len(df)} lignes")
            
            # Préparer les features
            numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
            numeric_cols = [col for col in numeric_cols if col != 'synthetic_target']
            
            if len(numeric_cols) < 2:
                logger.warning("⚠ Pas assez de features numériques pour ML")
                return None
            
            # Préparer X et y
            X = df[numeric_cols].fillna(0)
            y = df['synthetic_target']
            
            logger.info(f"  - Features: {len(numeric_cols)}")
            logger.info(f"  - Distribution target: {dict(y.value_counts())}")
            
            # Split des données
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.3, random_state=42, stratify=y
            )
            
            # Entraîner un modèle simple
            model = RandomForestClassifier(n_estimators=50, random_state=42)
            model.fit(X_train, y_train)
            
            # Évaluer
            y_pred = model.predict(X_test)
            y_pred_proba = model.predict_proba(X_test)[:, 1]
            
            accuracy = accuracy_score(y_test, y_pred)
            auc = roc_auc_score(y_test, y_pred_proba)
            
            logger.info(f"✓ Modèle entraîné avec données réelles:")
            logger.info(f"  - Accuracy: {accuracy:.3f}")
            logger.info(f"  - AUC: {auc:.3f}")
            
            # Feature importance
            feature_importance = pd.DataFrame({
                'feature': numeric_cols,
                'importance': model.feature_importances_
            }).sort_values('importance', ascending=False)
            
            logger.info("  - Top 5 features importantes:")
            for _, row in feature_importance.head().iterrows():
                logger.info(f"    * {row['feature']}: {row['importance']:.3f}")
            
            # Vérifications
            assert accuracy > 0.3, f"Accuracy trop faible: {accuracy}"
            assert auc > 0.4, f"AUC trop faible: {auc}"
            
            return {
                'accuracy': accuracy,
                'auc': auc,
                'feature_importance': feature_importance
            }
            
        except Exception as e:
            logger.error(f"Erreur ML avec données réelles: {e}")
            pytest.fail(f"ML avec données réelles échoué: {e}")
    
    def test_data_quality_real(self):
        """Test de qualité des données réelles."""
        try:
            from google.cloud import bigquery
            
            client = bigquery.Client(project="datalake-sensei")
            
            # Analyser la qualité des données principales
            tables_to_check = ['vw_dim_contact', 'vw_dim_leads', 'vw_dim_email']
            
            quality_report = {}
            
            for table in tables_to_check:
                try:
                    query = f"""
                    SELECT COUNT(*) as total_rows
                    FROM `datalake-sensei.serving_layer.{table}`
                    """
                    
                    query_job = client.query(query)
                    results = query_job.result()
                    df = results.to_dataframe()
                    
                    total_rows = df.iloc[0]['total_rows']
                    
                    quality_report[table] = {
                        'total_rows': total_rows,
                        'accessible': True
                    }
                    
                    logger.info(f"✓ {table}: {total_rows:,} lignes")
                    
                except Exception as e:
                    quality_report[table] = {
                        'total_rows': 0,
                        'accessible': False,
                        'error': str(e)
                    }
                    logger.warning(f"⚠ {table}: non accessible - {e}")
            
            # Vérifications globales
            accessible_tables = [t for t, info in quality_report.items() if info['accessible']]
            total_data_rows = sum(info['total_rows'] for info in quality_report.values() if info['accessible'])
            
            logger.info(f"✓ Qualité des données:")
            logger.info(f"  - Tables accessibles: {len(accessible_tables)}/{len(tables_to_check)}")
            logger.info(f"  - Total lignes: {total_data_rows:,}")
            
            assert len(accessible_tables) > 0, "Aucune table accessible"
            assert total_data_rows > 100, f"Pas assez de données: {total_data_rows} lignes"
            
            return quality_report
            
        except Exception as e:
            logger.error(f"Erreur qualité données réelles: {e}")
            pytest.fail(f"Test qualité données échoué: {e}")


if __name__ == "__main__":
    # Configurer les credentials si pas déjà fait
    if not os.getenv('GOOGLE_APPLICATION_CREDENTIALS'):
        credentials_path = Path(__file__).parent.parent / "credentials" / "sensei-ai-service-account.json"
        if credentials_path.exists():
            os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = str(credentials_path)
    
    pytest.main([__file__, "-v", "-s"])
