"""
Feature validation for Sensei AI.

Provides comprehensive validation of feature quality, consistency, and suitability for ML.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from scipy import stats
from sklearn.feature_selection import mutual_info_classif, mutual_info_regression

from ..utils import get_logger

logger = get_logger(__name__)


@dataclass
class FeatureValidationResult:
    """Results of feature validation."""
    feature_name: str
    is_valid: bool
    quality_score: float
    issues: List[str]
    warnings: List[str]
    statistics: Dict[str, Any]


@dataclass
class DatasetValidationResult:
    """Results of dataset validation."""
    is_valid: bool
    overall_quality_score: float
    feature_results: List[FeatureValidationResult]
    dataset_issues: List[str]
    dataset_warnings: List[str]
    summary_statistics: Dict[str, Any]


class FeatureValidator:
    """
    Comprehensive feature validation for ML readiness.
    
    Validates features for:
    - Data quality (missing values, outliers, etc.)
    - Statistical properties (distribution, variance, etc.)
    - ML suitability (correlation, information content, etc.)
    """
    
    def __init__(
        self,
        missing_threshold: float = 0.5,
        low_variance_threshold: float = 0.01,
        high_correlation_threshold: float = 0.95,
        outlier_z_threshold: float = 3.0,
    ):
        """
        Initialize feature validator.
        
        Args:
            missing_threshold: Maximum allowed missing value ratio
            low_variance_threshold: Minimum variance threshold
            high_correlation_threshold: Maximum correlation threshold
            outlier_z_threshold: Z-score threshold for outlier detection
        """
        self.missing_threshold = missing_threshold
        self.low_variance_threshold = low_variance_threshold
        self.high_correlation_threshold = high_correlation_threshold
        self.outlier_z_threshold = outlier_z_threshold
    
    def validate_dataset(
        self,
        X: pd.DataFrame,
        y: Optional[pd.Series] = None,
        feature_names: Optional[List[str]] = None,
    ) -> DatasetValidationResult:
        """
        Validate entire dataset.
        
        Args:
            X: Feature matrix
            y: Target variable (optional)
            feature_names: Feature names (optional)
            
        Returns:
            Dataset validation results
        """
        logger.info("Starting dataset validation", shape=X.shape)
        
        # Use provided feature names or generate them
        if feature_names is None:
            if hasattr(X, 'columns'):
                feature_names = list(X.columns)
            else:
                feature_names = [f"feature_{i}" for i in range(X.shape[1])]
        
        # Convert to DataFrame if needed
        if not isinstance(X, pd.DataFrame):
            X = pd.DataFrame(X, columns=feature_names)
        
        # Validate individual features
        feature_results = []
        for i, feature_name in enumerate(feature_names):
            if i < X.shape[1]:
                feature_data = X.iloc[:, i] if isinstance(X, pd.DataFrame) else X[:, i]
                result = self.validate_feature(
                    feature_data, 
                    feature_name, 
                    y
                )
                feature_results.append(result)
        
        # Dataset-level validation
        dataset_issues = []
        dataset_warnings = []
        
        # Check dataset size
        if len(X) < 100:
            dataset_warnings.append(f"Small dataset size: {len(X)} samples")
        elif len(X) < 1000:
            dataset_warnings.append(f"Moderate dataset size: {len(X)} samples")
        
        # Check feature count
        if X.shape[1] > len(X):
            dataset_issues.append(
                f"More features ({X.shape[1]}) than samples ({len(X)}) - risk of overfitting"
            )
        
        # Check for duplicate features
        duplicate_features = self._find_duplicate_features(X)
        if duplicate_features:
            dataset_warnings.append(f"Duplicate features found: {duplicate_features}")
        
        # Check for highly correlated features
        high_corr_pairs = self._find_high_correlation_pairs(X)
        if high_corr_pairs:
            dataset_warnings.append(f"Highly correlated feature pairs: {len(high_corr_pairs)}")
        
        # Calculate overall quality score
        valid_features = [r for r in feature_results if r.is_valid]
        if feature_results:
            overall_quality_score = np.mean([r.quality_score for r in feature_results])
        else:
            overall_quality_score = 0.0
        
        # Dataset is valid if no critical issues and reasonable quality
        is_valid = (
            len(dataset_issues) == 0 and
            len(valid_features) >= 0.5 * len(feature_results) and
            overall_quality_score >= 0.5
        )
        
        # Summary statistics
        summary_statistics = {
            'total_features': len(feature_results),
            'valid_features': len(valid_features),
            'invalid_features': len(feature_results) - len(valid_features),
            'average_quality_score': overall_quality_score,
            'missing_value_features': sum(1 for r in feature_results if 'missing values' in ' '.join(r.issues + r.warnings)),
            'low_variance_features': sum(1 for r in feature_results if 'low variance' in ' '.join(r.issues + r.warnings)),
            'high_correlation_pairs': len(high_corr_pairs),
            'duplicate_features': len(duplicate_features),
        }
        
        result = DatasetValidationResult(
            is_valid=is_valid,
            overall_quality_score=overall_quality_score,
            feature_results=feature_results,
            dataset_issues=dataset_issues,
            dataset_warnings=dataset_warnings,
            summary_statistics=summary_statistics,
        )
        
        logger.info(
            "Dataset validation completed",
            is_valid=is_valid,
            quality_score=overall_quality_score,
            valid_features=len(valid_features),
            total_features=len(feature_results),
        )
        
        return result
    
    def validate_feature(
        self,
        feature_data: pd.Series,
        feature_name: str,
        target: Optional[pd.Series] = None,
    ) -> FeatureValidationResult:
        """
        Validate individual feature.
        
        Args:
            feature_data: Feature values
            feature_name: Name of the feature
            target: Target variable (optional)
            
        Returns:
            Feature validation results
        """
        issues = []
        warnings = []
        statistics = {}
        
        # Convert to pandas Series if needed
        if not isinstance(feature_data, pd.Series):
            feature_data = pd.Series(feature_data, name=feature_name)
        
        # Basic statistics
        statistics.update({
            'count': len(feature_data),
            'missing_count': feature_data.isna().sum(),
            'missing_ratio': feature_data.isna().sum() / len(feature_data),
            'unique_count': feature_data.nunique(),
            'unique_ratio': feature_data.nunique() / len(feature_data),
        })
        
        # Check for missing values
        if statistics['missing_ratio'] > self.missing_threshold:
            issues.append(f"High missing value ratio: {statistics['missing_ratio']:.3f}")
        elif statistics['missing_ratio'] > 0.1:
            warnings.append(f"Moderate missing values: {statistics['missing_ratio']:.3f}")
        
        # Check data type and handle accordingly
        if pd.api.types.is_numeric_dtype(feature_data):
            self._validate_numeric_feature(feature_data, statistics, issues, warnings)
        else:
            self._validate_categorical_feature(feature_data, statistics, issues, warnings)
        
        # Check information content with target
        if target is not None:
            info_score = self._calculate_information_score(feature_data, target)
            statistics['information_score'] = info_score
            
            if info_score < 0.01:
                warnings.append(f"Low information content: {info_score:.4f}")
        
        # Calculate quality score
        quality_score = self._calculate_feature_quality_score(statistics, issues, warnings)
        
        # Feature is valid if no critical issues
        is_valid = len(issues) == 0
        
        return FeatureValidationResult(
            feature_name=feature_name,
            is_valid=is_valid,
            quality_score=quality_score,
            issues=issues,
            warnings=warnings,
            statistics=statistics,
        )
    
    def _validate_numeric_feature(
        self,
        feature_data: pd.Series,
        statistics: Dict[str, Any],
        issues: List[str],
        warnings: List[str],
    ) -> None:
        """Validate numeric feature."""
        # Remove missing values for calculations
        clean_data = feature_data.dropna()
        
        if len(clean_data) == 0:
            issues.append("No valid numeric values")
            return
        
        # Basic numeric statistics
        statistics.update({
            'mean': clean_data.mean(),
            'std': clean_data.std(),
            'min': clean_data.min(),
            'max': clean_data.max(),
            'median': clean_data.median(),
            'skewness': stats.skew(clean_data),
            'kurtosis': stats.kurtosis(clean_data),
        })
        
        # Check for constant features (zero variance)
        if statistics['std'] < self.low_variance_threshold:
            issues.append(f"Low variance: {statistics['std']:.6f}")
        
        # Check for infinite values
        inf_count = np.isinf(clean_data).sum()
        if inf_count > 0:
            issues.append(f"Contains {inf_count} infinite values")
        
        # Check for outliers
        z_scores = np.abs(stats.zscore(clean_data))
        outlier_count = (z_scores > self.outlier_z_threshold).sum()
        outlier_ratio = outlier_count / len(clean_data)
        
        statistics['outlier_count'] = outlier_count
        statistics['outlier_ratio'] = outlier_ratio
        
        if outlier_ratio > 0.1:
            warnings.append(f"High outlier ratio: {outlier_ratio:.3f}")
        
        # Check distribution properties
        if abs(statistics['skewness']) > 2:
            warnings.append(f"Highly skewed distribution: skewness = {statistics['skewness']:.3f}")
        
        if statistics['kurtosis'] > 7:
            warnings.append(f"Heavy-tailed distribution: kurtosis = {statistics['kurtosis']:.3f}")
    
    def _validate_categorical_feature(
        self,
        feature_data: pd.Series,
        statistics: Dict[str, Any],
        issues: List[str],
        warnings: List[str],
    ) -> None:
        """Validate categorical feature."""
        # Remove missing values for calculations
        clean_data = feature_data.dropna()
        
        if len(clean_data) == 0:
            issues.append("No valid categorical values")
            return
        
        # Categorical statistics
        value_counts = clean_data.value_counts()
        statistics.update({
            'most_frequent_value': value_counts.index[0] if len(value_counts) > 0 else None,
            'most_frequent_count': value_counts.iloc[0] if len(value_counts) > 0 else 0,
            'most_frequent_ratio': value_counts.iloc[0] / len(clean_data) if len(value_counts) > 0 else 0,
            'category_counts': value_counts.to_dict(),
        })
        
        # Check for high cardinality
        if statistics['unique_count'] > len(clean_data) * 0.5:
            warnings.append(f"High cardinality: {statistics['unique_count']} unique values")
        
        # Check for dominant category
        if statistics['most_frequent_ratio'] > 0.95:
            warnings.append(f"Dominant category: {statistics['most_frequent_ratio']:.3f} ratio")
    
    def _calculate_information_score(
        self,
        feature_data: pd.Series,
        target: pd.Series,
    ) -> float:
        """Calculate information score between feature and target."""
        try:
            # Remove missing values
            mask = ~(feature_data.isna() | target.isna())
            clean_feature = feature_data[mask]
            clean_target = target[mask]
            
            if len(clean_feature) < 10:
                return 0.0
            
            # Choose appropriate mutual information function
            if pd.api.types.is_numeric_dtype(clean_target):
                # Regression case
                if pd.api.types.is_numeric_dtype(clean_feature):
                    # Both numeric - use mutual info regression
                    mi_score = mutual_info_regression(
                        clean_feature.values.reshape(-1, 1),
                        clean_target.values,
                        random_state=42
                    )[0]
                else:
                    # Categorical feature, numeric target
                    # Convert categorical to numeric for MI calculation
                    from sklearn.preprocessing import LabelEncoder
                    le = LabelEncoder()
                    encoded_feature = le.fit_transform(clean_feature.astype(str))
                    mi_score = mutual_info_regression(
                        encoded_feature.reshape(-1, 1),
                        clean_target.values,
                        random_state=42
                    )[0]
            else:
                # Classification case
                if pd.api.types.is_numeric_dtype(clean_feature):
                    mi_score = mutual_info_classif(
                        clean_feature.values.reshape(-1, 1),
                        clean_target.values,
                        random_state=42
                    )[0]
                else:
                    # Both categorical
                    from sklearn.preprocessing import LabelEncoder
                    le_feature = LabelEncoder()
                    le_target = LabelEncoder()
                    encoded_feature = le_feature.fit_transform(clean_feature.astype(str))
                    encoded_target = le_target.fit_transform(clean_target.astype(str))
                    mi_score = mutual_info_classif(
                        encoded_feature.reshape(-1, 1),
                        encoded_target,
                        random_state=42
                    )[0]
            
            return float(mi_score)
            
        except Exception as e:
            logger.warning(f"Failed to calculate information score: {e}")
            return 0.0
    
    def _calculate_feature_quality_score(
        self,
        statistics: Dict[str, Any],
        issues: List[str],
        warnings: List[str],
    ) -> float:
        """Calculate overall feature quality score."""
        score = 1.0
        
        # Penalize for issues (critical)
        score -= len(issues) * 0.3
        
        # Penalize for warnings (moderate)
        score -= len(warnings) * 0.1
        
        # Bonus for good properties
        if statistics.get('missing_ratio', 1.0) < 0.05:
            score += 0.1
        
        if statistics.get('information_score', 0.0) > 0.1:
            score += 0.1
        
        # Ensure score is between 0 and 1
        return max(0.0, min(1.0, score))
    
    def _find_duplicate_features(self, X: pd.DataFrame) -> List[Tuple[str, str]]:
        """Find duplicate features."""
        duplicates = []
        
        for i in range(X.shape[1]):
            for j in range(i + 1, X.shape[1]):
                if X.iloc[:, i].equals(X.iloc[:, j]):
                    col_i = X.columns[i] if hasattr(X, 'columns') else f"feature_{i}"
                    col_j = X.columns[j] if hasattr(X, 'columns') else f"feature_{j}"
                    duplicates.append((col_i, col_j))
        
        return duplicates
    
    def _find_high_correlation_pairs(self, X: pd.DataFrame) -> List[Tuple[str, str, float]]:
        """Find highly correlated feature pairs."""
        # Only check numeric features
        numeric_X = X.select_dtypes(include=[np.number])
        
        if numeric_X.shape[1] < 2:
            return []
        
        corr_matrix = numeric_X.corr().abs()
        high_corr_pairs = []
        
        for i in range(len(corr_matrix.columns)):
            for j in range(i + 1, len(corr_matrix.columns)):
                corr_value = corr_matrix.iloc[i, j]
                if corr_value > self.high_correlation_threshold:
                    col_i = corr_matrix.columns[i]
                    col_j = corr_matrix.columns[j]
                    high_corr_pairs.append((col_i, col_j, corr_value))
        
        return high_corr_pairs


def validate_features(
    X: pd.DataFrame,
    y: Optional[pd.Series] = None,
    feature_names: Optional[List[str]] = None,
    validator_params: Optional[Dict[str, Any]] = None,
) -> DatasetValidationResult:
    """
    Validate features for ML readiness.
    
    Args:
        X: Feature matrix
        y: Target variable (optional)
        feature_names: Feature names (optional)
        validator_params: Validator parameters (optional)
        
    Returns:
        Validation results
    """
    validator_params = validator_params or {}
    validator = FeatureValidator(**validator_params)
    
    return validator.validate_dataset(X, y, feature_names)
