"""
Feature engineering pipeline for Sensei AI.

Provides end-to-end feature processing pipelines for different ML tasks.
"""

import numpy as np
import pandas as pd
from typing import Dict, Any, Optional, Tuple, List
from sklearn.pipeline import Pipeline
from sklearn.model_selection import train_test_split

from .feature_store import FeatureStore, get_feature_store
from .transformers import (
    ConversionFeatureTransformer,
    ChannelTimingFeatureTransformer,
    NLPFeatureTransformer,
)
from ..utils import get_logger, get_config
from ..utils.monitoring import track_performance

logger = get_logger(__name__)


class FeaturePipeline:
    """
    End-to-end feature processing pipeline.
    
    Handles data extraction, transformation, and preparation for ML models.
    """
    
    def __init__(
        self,
        feature_type: str,
        feature_store: Optional[FeatureStore] = None,
        transformer_params: Optional[Dict[str, Any]] = None,
    ):
        """
        Initialize feature pipeline.
        
        Args:
            feature_type: Type of features ('conversion', 'channel_timing', 'nlp')
            feature_store: Feature store instance
            transformer_params: Parameters for the transformer
        """
        self.feature_type = feature_type
        self.feature_store = feature_store or get_feature_store()
        self.config = get_config()
        
        # Initialize transformer based on feature type
        transformer_params = transformer_params or {}
        
        if feature_type == "conversion":
            self.transformer = ConversionFeatureTransformer(**transformer_params)
            self.target_column = "converted"
        elif feature_type == "channel_timing":
            self.transformer = ChannelTimingFeatureTransformer(**transformer_params)
            self.target_column = "recommended_channel"
        elif feature_type == "nlp":
            self.transformer = NLPFeatureTransformer(**transformer_params)
            self.target_column = "prequalification_quality"  # or another target
        else:
            raise ValueError(f"Unknown feature type: {feature_type}")
        
        # Pipeline components
        self.pipeline = Pipeline([
            ('transformer', self.transformer)
        ])
        
        self.is_fitted = False
        
        logger.info(
            "Feature pipeline initialized",
            feature_type=feature_type,
            transformer=type(self.transformer).__name__,
        )
    
    def extract_and_prepare_data(
        self,
        limit: Optional[int] = None,
        test_size: float = 0.2,
        random_state: int = 42,
        use_cache: bool = True,
    ) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray, pd.DataFrame]:
        """
        Extract data and prepare train/test splits.
        
        Args:
            limit: Maximum number of samples to extract
            test_size: Proportion of data for testing
            random_state: Random state for reproducibility
            use_cache: Whether to use cached features
            
        Returns:
            Tuple of (X_train, X_test, y_train, y_test, metadata_df)
        """
        with track_performance(
            "feature_extraction_and_preparation",
            labels={"feature_type": self.feature_type}
        ):
            logger.info(
                "Extracting and preparing data",
                feature_type=self.feature_type,
                limit=limit,
                test_size=test_size,
            )
            
            # Extract features based on type
            if self.feature_type == "conversion":
                df, metadata = self.feature_store.get_conversion_features(
                    limit=limit, use_cache=use_cache
                )
            elif self.feature_type == "channel_timing":
                df, metadata = self.feature_store.get_channel_timing_features(
                    limit=limit, use_cache=use_cache
                )
            elif self.feature_type == "nlp":
                df, metadata = self.feature_store.get_nlp_features(
                    limit=limit, use_cache=use_cache
                )
            else:
                raise ValueError(f"Unknown feature type: {self.feature_type}")
            
            if df.empty:
                raise ValueError(f"No data available for feature type: {self.feature_type}")
            
            # Prepare features and target
            if self.target_column not in df.columns:
                raise ValueError(f"Target column '{self.target_column}' not found in data")
            
            # Separate features and target
            X = df.drop(columns=[self.target_column])
            y = df[self.target_column]
            
            # Create train/test split
            X_train, X_test, y_train, y_test = train_test_split(
                X, y,
                test_size=test_size,
                random_state=random_state,
                stratify=y if self.feature_type != "nlp" else None,  # No stratify for NLP
            )
            
            logger.info(
                "Data prepared successfully",
                total_samples=len(df),
                train_samples=len(X_train),
                test_samples=len(X_test),
                feature_columns=len(X.columns),
                data_quality_score=metadata.data_quality_score,
            )
            
            # Create metadata DataFrame
            metadata_df = pd.DataFrame([{
                'feature_type': metadata.feature_type,
                'created_at': metadata.created_at,
                'row_count': metadata.row_count,
                'column_count': metadata.column_count,
                'data_quality_score': metadata.data_quality_score,
                'source_tables': ','.join(metadata.source_tables),
            }])
            
            return X_train, X_test, y_train, y_test, metadata_df
    
    def fit_transform(
        self,
        X_train: pd.DataFrame,
        y_train: Optional[pd.Series] = None,
    ) -> np.ndarray:
        """
        Fit the pipeline and transform training data.
        
        Args:
            X_train: Training features
            y_train: Training target (not used by transformers)
            
        Returns:
            Transformed training features
        """
        with track_performance(
            "feature_pipeline_fit_transform",
            labels={"feature_type": self.feature_type}
        ):
            logger.info("Fitting and transforming features", samples=len(X_train))
            
            # Fit and transform
            X_train_transformed = self.pipeline.fit_transform(X_train)
            self.is_fitted = True
            
            logger.info(
                "Features fitted and transformed",
                input_shape=X_train.shape,
                output_shape=X_train_transformed.shape,
            )
            
            return X_train_transformed
    
    def transform(self, X: pd.DataFrame) -> np.ndarray:
        """
        Transform features using fitted pipeline.
        
        Args:
            X: Features to transform
            
        Returns:
            Transformed features
        """
        if not self.is_fitted:
            raise ValueError("Pipeline must be fitted before transform")
        
        with track_performance(
            "feature_pipeline_transform",
            labels={"feature_type": self.feature_type}
        ):
            logger.debug("Transforming features", samples=len(X))
            
            X_transformed = self.pipeline.transform(X)
            
            logger.debug(
                "Features transformed",
                input_shape=X.shape,
                output_shape=X_transformed.shape,
            )
            
            return X_transformed
    
    def get_feature_names(self) -> List[str]:
        """Get feature names from the fitted transformer."""
        if not self.is_fitted:
            raise ValueError("Pipeline must be fitted to get feature names")
        
        return self.transformer.get_feature_names_out()
    
    def get_feature_importance_data(self, X: pd.DataFrame) -> pd.DataFrame:
        """
        Get data for feature importance analysis.
        
        Args:
            X: Input features
            
        Returns:
            DataFrame with feature names and basic statistics
        """
        if not self.is_fitted:
            raise ValueError("Pipeline must be fitted to get feature importance data")
        
        # Transform features
        X_transformed = self.transform(X)
        feature_names = self.get_feature_names()
        
        # Calculate basic statistics
        feature_stats = []
        for i, name in enumerate(feature_names):
            if i < X_transformed.shape[1]:
                feature_values = X_transformed[:, i]
                stats = {
                    'feature_name': name,
                    'mean': np.mean(feature_values),
                    'std': np.std(feature_values),
                    'min': np.min(feature_values),
                    'max': np.max(feature_values),
                    'non_zero_ratio': np.count_nonzero(feature_values) / len(feature_values),
                }
                feature_stats.append(stats)
        
        return pd.DataFrame(feature_stats)


def create_feature_pipeline(
    feature_type: str,
    transformer_params: Optional[Dict[str, Any]] = None,
    feature_store: Optional[FeatureStore] = None,
) -> FeaturePipeline:
    """
    Create a feature pipeline for a specific feature type.
    
    Args:
        feature_type: Type of features ('conversion', 'channel_timing', 'nlp')
        transformer_params: Parameters for the transformer
        feature_store: Feature store instance
        
    Returns:
        Configured feature pipeline
    """
    return FeaturePipeline(
        feature_type=feature_type,
        transformer_params=transformer_params,
        feature_store=feature_store,
    )


def get_default_transformer_params(feature_type: str) -> Dict[str, Any]:
    """
    Get default transformer parameters for a feature type.
    
    Args:
        feature_type: Type of features
        
    Returns:
        Default parameters dictionary
    """
    config = get_config()
    
    if feature_type == "conversion":
        return {
            'scale_numeric': True,
            'encode_categorical': True,
            'create_interaction_features': True,
            'max_categorical_cardinality': 50,
        }
    elif feature_type == "channel_timing":
        return {
            'scale_numeric': True,
            'encode_categorical': True,
            'create_time_features': True,
        }
    elif feature_type == "nlp":
        return {
            'max_features': min(1000, config.training.max_features),
            'ngram_range': (1, 2),
            'min_df': 2,
            'max_df': 0.95,
            'use_pca': True,
            'pca_components': min(100, config.training.max_features // 10),
            'extract_sentiment_features': True,
        }
    else:
        raise ValueError(f"Unknown feature type: {feature_type}")


def validate_pipeline_data(
    X_train: np.ndarray,
    X_test: np.ndarray,
    y_train: np.ndarray,
    y_test: np.ndarray,
    feature_type: str,
) -> Dict[str, Any]:
    """
    Validate pipeline data quality and consistency.
    
    Args:
        X_train: Training features
        X_test: Test features
        y_train: Training target
        y_test: Test target
        feature_type: Type of features
        
    Returns:
        Validation results dictionary
    """
    validation_results = {
        'valid': True,
        'warnings': [],
        'errors': [],
        'statistics': {},
    }
    
    # Check data shapes
    if X_train.shape[1] != X_test.shape[1]:
        validation_results['errors'].append(
            f"Feature dimension mismatch: train={X_train.shape[1]}, test={X_test.shape[1]}"
        )
        validation_results['valid'] = False
    
    # Check for missing values
    if np.isnan(X_train).any():
        validation_results['warnings'].append("Training features contain NaN values")
    
    if np.isnan(X_test).any():
        validation_results['warnings'].append("Test features contain NaN values")
    
    # Check target distribution
    if feature_type in ["conversion", "channel_timing"]:
        train_class_dist = np.bincount(y_train) / len(y_train)
        test_class_dist = np.bincount(y_test) / len(y_test)
        
        # Check for class imbalance
        min_class_ratio = np.min(train_class_dist)
        if min_class_ratio < 0.05:
            validation_results['warnings'].append(
                f"Severe class imbalance detected: minimum class ratio = {min_class_ratio:.3f}"
            )
    
    # Calculate statistics
    validation_results['statistics'] = {
        'train_samples': len(X_train),
        'test_samples': len(X_test),
        'feature_count': X_train.shape[1],
        'train_feature_mean': np.mean(X_train),
        'test_feature_mean': np.mean(X_test),
        'train_feature_std': np.std(X_train),
        'test_feature_std': np.std(X_test),
    }
    
    logger.info(
        "Pipeline data validation completed",
        valid=validation_results['valid'],
        warnings=len(validation_results['warnings']),
        errors=len(validation_results['errors']),
    )
    
    return validation_results
