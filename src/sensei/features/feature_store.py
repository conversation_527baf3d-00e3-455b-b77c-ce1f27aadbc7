"""
Feature store for Sensei AI.

Manages feature extraction, caching, and serving for ML models.
Provides a unified interface for accessing features from multiple data sources.
"""

import pickle
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
from dataclasses import dataclass

from ..data import get_bigquery_client, get_feature_query
from ..utils import get_logger, get_config
from ..utils.monitoring import track_performance

logger = get_logger(__name__)


@dataclass
class FeatureMetadata:
    """Metadata for feature sets."""
    feature_type: str
    created_at: datetime
    row_count: int
    column_count: int
    data_quality_score: float
    source_tables: List[str]
    feature_names: List[str]


class FeatureStore:
    """
    Feature store for managing ML features.
    
    Provides caching, validation, and serving of features for ML models.
    """
    
    def __init__(self, cache_dir: Optional[Path] = None):
        """
        Initialize feature store.
        
        Args:
            cache_dir: Directory for caching features
        """
        self.config = get_config()
        self.bq_client = get_bigquery_client()
        
        # Setup cache directory
        self.cache_dir = cache_dir or Path("features_cache")
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # Feature metadata cache
        self.metadata_cache: Dict[str, FeatureMetadata] = {}
        
        logger.info(
            "Feature store initialized",
            cache_dir=str(self.cache_dir),
        )
    
    def get_conversion_features(
        self,
        limit: Optional[int] = None,
        use_cache: bool = True,
        cache_ttl_hours: int = 24,
    ) -> Tuple[pd.DataFrame, FeatureMetadata]:
        """
        Get features for conversion prediction.
        
        Args:
            limit: Maximum number of rows to return
            use_cache: Whether to use cached features
            cache_ttl_hours: Cache time-to-live in hours
            
        Returns:
            Tuple of (features DataFrame, metadata)
        """
        cache_key = f"conversion_features_{limit or 'all'}"
        
        # Check cache first
        if use_cache:
            cached_data = self._get_from_cache(cache_key, cache_ttl_hours)
            if cached_data is not None:
                return cached_data
        
        # Extract features from BigQuery
        with track_performance(
            "feature_extraction",
            labels={"feature_type": "conversion", "limit": str(limit)}
        ):
            logger.info("Extracting conversion features", limit=limit)
            
            query = get_feature_query(
                "conversion",
                limit=limit,
                date_filter_days=self.config.training.max_samples // 100,  # Adaptive date filter
            )
            
            df = self.bq_client.query_dataframe(query)
            
            # Validate and clean features
            df_clean = self._clean_conversion_features(df)
            
            # Create metadata
            metadata = FeatureMetadata(
                feature_type="conversion",
                created_at=datetime.now(),
                row_count=len(df_clean),
                column_count=len(df_clean.columns),
                data_quality_score=self._calculate_data_quality_score(df_clean),
                source_tables=["vw_dim_contact", "vw_reponses_typeform_deno", "vw_dim_modjo_call_summary"],
                feature_names=list(df_clean.columns),
            )
            
            # Cache results
            if use_cache:
                self._save_to_cache(cache_key, (df_clean, metadata))
            
            logger.info(
                "Conversion features extracted",
                rows=len(df_clean),
                columns=len(df_clean.columns),
                quality_score=metadata.data_quality_score,
            )
            
            return df_clean, metadata
    
    def get_channel_timing_features(
        self,
        limit: Optional[int] = None,
        use_cache: bool = True,
        cache_ttl_hours: int = 24,
    ) -> Tuple[pd.DataFrame, FeatureMetadata]:
        """
        Get features for channel and timing optimization.
        
        Args:
            limit: Maximum number of rows to return
            use_cache: Whether to use cached features
            cache_ttl_hours: Cache time-to-live in hours
            
        Returns:
            Tuple of (features DataFrame, metadata)
        """
        cache_key = f"channel_timing_features_{limit or 'all'}"
        
        # Check cache first
        if use_cache:
            cached_data = self._get_from_cache(cache_key, cache_ttl_hours)
            if cached_data is not None:
                return cached_data
        
        # Extract features from BigQuery
        with track_performance(
            "feature_extraction",
            labels={"feature_type": "channel_timing", "limit": str(limit)}
        ):
            logger.info("Extracting channel timing features", limit=limit)
            
            query = get_feature_query(
                "channel_timing",
                limit=limit,
                date_filter_days=self.config.training.max_samples // 100,
            )
            
            df = self.bq_client.query_dataframe(query)
            
            # Validate and clean features
            df_clean = self._clean_channel_timing_features(df)
            
            # Create metadata
            metadata = FeatureMetadata(
                feature_type="channel_timing",
                created_at=datetime.now(),
                row_count=len(df_clean),
                column_count=len(df_clean.columns),
                data_quality_score=self._calculate_data_quality_score(df_clean),
                source_tables=["vw_fact_transaction", "vw_dim_email", "vw_dim_appel", "vw_dim_reunion"],
                feature_names=list(df_clean.columns),
            )
            
            # Cache results
            if use_cache:
                self._save_to_cache(cache_key, (df_clean, metadata))
            
            logger.info(
                "Channel timing features extracted",
                rows=len(df_clean),
                columns=len(df_clean.columns),
                quality_score=metadata.data_quality_score,
            )
            
            return df_clean, metadata
    
    def get_nlp_features(
        self,
        limit: Optional[int] = None,
        use_cache: bool = True,
        cache_ttl_hours: int = 12,  # Shorter TTL for NLP features
    ) -> Tuple[pd.DataFrame, FeatureMetadata]:
        """
        Get features for NLP analysis.
        
        Args:
            limit: Maximum number of rows to return
            use_cache: Whether to use cached features
            cache_ttl_hours: Cache time-to-live in hours
            
        Returns:
            Tuple of (features DataFrame, metadata)
        """
        cache_key = f"nlp_features_{limit or 'all'}"
        
        # Check cache first
        if use_cache:
            cached_data = self._get_from_cache(cache_key, cache_ttl_hours)
            if cached_data is not None:
                return cached_data
        
        # Extract features from BigQuery
        with track_performance(
            "feature_extraction",
            labels={"feature_type": "nlp", "limit": str(limit)}
        ):
            logger.info("Extracting NLP features", limit=limit)
            
            query = get_feature_query(
                "nlp_transcript",
                limit=limit,
                date_filter_days=30,  # Recent transcripts for NLP
            )
            
            df = self.bq_client.query_dataframe(query)
            
            # Validate and clean features
            df_clean = self._clean_nlp_features(df)
            
            # Create metadata
            metadata = FeatureMetadata(
                feature_type="nlp",
                created_at=datetime.now(),
                row_count=len(df_clean),
                column_count=len(df_clean.columns),
                data_quality_score=self._calculate_data_quality_score(df_clean),
                source_tables=["vw_dim_modjo_call_summary", "vw_dim_modjo_transcript"],
                feature_names=list(df_clean.columns),
            )
            
            # Cache results
            if use_cache:
                self._save_to_cache(cache_key, (df_clean, metadata))
            
            logger.info(
                "NLP features extracted",
                rows=len(df_clean),
                columns=len(df_clean.columns),
                quality_score=metadata.data_quality_score,
            )
            
            return df_clean, metadata
    
    def _clean_conversion_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Clean and validate conversion features."""
        if df.empty:
            return df
        
        # Remove rows with missing target variable
        df = df.dropna(subset=['converted'])
        
        # Handle missing values in features
        numeric_columns = df.select_dtypes(include=[np.number]).columns
        for col in numeric_columns:
            if col != 'converted':  # Don't fill target variable
                df[col] = df[col].fillna(0)
        
        # Handle categorical features
        categorical_columns = df.select_dtypes(include=['object']).columns
        for col in categorical_columns:
            df[col] = df[col].fillna('unknown')
        
        # Remove duplicates
        df = df.drop_duplicates(subset=['id_contact'])
        
        return df
    
    def _clean_channel_timing_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Clean and validate channel timing features."""
        if df.empty:
            return df
        
        # Remove rows with missing essential fields
        df = df.dropna(subset=['id_transaction', 'recommended_channel'])
        
        # Handle missing values
        numeric_columns = df.select_dtypes(include=[np.number]).columns
        for col in numeric_columns:
            df[col] = df[col].fillna(0)
        
        categorical_columns = df.select_dtypes(include=['object']).columns
        for col in categorical_columns:
            if col != 'recommended_channel':  # Don't fill target variable
                df[col] = df[col].fillna('unknown')
        
        # Remove duplicates
        df = df.drop_duplicates(subset=['id_transaction'])
        
        return df
    
    def _clean_nlp_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Clean and validate NLP features."""
        if df.empty:
            return df
        
        # Remove rows with missing transcript
        df = df.dropna(subset=['full_transcript'])
        
        # Remove very short transcripts (likely incomplete)
        df = df[df['word_count'] >= 10]
        
        # Handle missing scores
        score_columns = [col for col in df.columns if 'score' in col.lower()]
        for col in score_columns:
            df[col] = df[col].fillna(0)
        
        # Handle other missing values
        numeric_columns = df.select_dtypes(include=[np.number]).columns
        for col in numeric_columns:
            df[col] = df[col].fillna(0)
        
        categorical_columns = df.select_dtypes(include=['object']).columns
        for col in categorical_columns:
            if col not in ['full_transcript', 'summary_content', 'aiSummary_content']:
                df[col] = df[col].fillna('unknown')
        
        # Remove duplicates
        df = df.drop_duplicates(subset=['callId'])
        
        return df
    
    def _calculate_data_quality_score(self, df: pd.DataFrame) -> float:
        """Calculate data quality score for a DataFrame."""
        if df.empty:
            return 0.0
        
        # Calculate completeness (percentage of non-null values)
        completeness = (df.count().sum() / (len(df) * len(df.columns))) * 100
        
        # Calculate uniqueness (percentage of unique rows)
        uniqueness = (len(df.drop_duplicates()) / len(df)) * 100
        
        # Simple quality score (can be enhanced with more sophisticated metrics)
        quality_score = (completeness + uniqueness) / 2
        
        return round(quality_score, 2)
    
    def _get_cache_path(self, cache_key: str) -> Path:
        """Get cache file path for a given key."""
        return self.cache_dir / f"{cache_key}.pkl"
    
    def _get_from_cache(
        self,
        cache_key: str,
        ttl_hours: int,
    ) -> Optional[Tuple[pd.DataFrame, FeatureMetadata]]:
        """Get data from cache if valid."""
        cache_path = self._get_cache_path(cache_key)
        
        if not cache_path.exists():
            return None
        
        # Check if cache is still valid
        cache_age = datetime.now() - datetime.fromtimestamp(cache_path.stat().st_mtime)
        if cache_age > timedelta(hours=ttl_hours):
            logger.debug("Cache expired", cache_key=cache_key, age_hours=cache_age.total_seconds() / 3600)
            return None
        
        try:
            with open(cache_path, 'rb') as f:
                data = pickle.load(f)
            
            logger.debug("Cache hit", cache_key=cache_key)
            return data
            
        except Exception as e:
            logger.warning("Failed to load from cache", cache_key=cache_key, error=str(e))
            return None
    
    def _save_to_cache(
        self,
        cache_key: str,
        data: Tuple[pd.DataFrame, FeatureMetadata],
    ) -> None:
        """Save data to cache."""
        cache_path = self._get_cache_path(cache_key)
        
        try:
            with open(cache_path, 'wb') as f:
                pickle.dump(data, f)
            
            logger.debug("Data cached", cache_key=cache_key, path=str(cache_path))
            
        except Exception as e:
            logger.warning("Failed to save to cache", cache_key=cache_key, error=str(e))
    
    def clear_cache(self, feature_type: Optional[str] = None) -> None:
        """
        Clear feature cache.
        
        Args:
            feature_type: Specific feature type to clear, or None for all
        """
        if feature_type:
            pattern = f"{feature_type}_features_*.pkl"
        else:
            pattern = "*.pkl"
        
        cache_files = list(self.cache_dir.glob(pattern))
        
        for cache_file in cache_files:
            try:
                cache_file.unlink()
                logger.debug("Cache file removed", file=str(cache_file))
            except Exception as e:
                logger.warning("Failed to remove cache file", file=str(cache_file), error=str(e))
        
        logger.info("Cache cleared", feature_type=feature_type, files_removed=len(cache_files))
    
    def get_feature_metadata(self, feature_type: str) -> Optional[FeatureMetadata]:
        """Get metadata for a feature type."""
        return self.metadata_cache.get(feature_type)


# Global feature store instance
_feature_store: Optional[FeatureStore] = None


def get_feature_store() -> FeatureStore:
    """Get the global feature store instance."""
    global _feature_store
    if _feature_store is None:
        _feature_store = FeatureStore()
    return _feature_store
