"""
Feature transformers for Sensei AI.

Provides specialized transformers for different types of features:
- Conversion prediction features
- Channel and timing optimization features  
- NLP features from conversation transcripts
"""

import numpy as np
import pandas as pd
from typing import List, Dict, Any, Optional, Tuple
from sklearn.base import BaseEstimator, TransformerMixin
from sklearn.preprocessing import StandardScaler, LabelEncoder, OneHotEncoder
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.decomposition import PCA
import re

from ..utils import get_logger

logger = get_logger(__name__)


class ConversionFeatureTransformer(BaseEstimator, TransformerMixin):
    """
    Feature transformer for conversion prediction.
    
    Handles preprocessing of features from Typeform, HubSpot, and Modjo data
    for conversion probability prediction.
    """
    
    def __init__(
        self,
        scale_numeric: bool = True,
        encode_categorical: bool = True,
        create_interaction_features: bool = True,
        max_categorical_cardinality: int = 50,
    ):
        """
        Initialize conversion feature transformer.
        
        Args:
            scale_numeric: Whether to scale numeric features
            encode_categorical: Whether to encode categorical features
            create_interaction_features: Whether to create interaction features
            max_categorical_cardinality: Maximum cardinality for categorical encoding
        """
        self.scale_numeric = scale_numeric
        self.encode_categorical = encode_categorical
        self.create_interaction_features = create_interaction_features
        self.max_categorical_cardinality = max_categorical_cardinality
        
        # Transformers
        self.numeric_scaler = StandardScaler() if scale_numeric else None
        self.categorical_encoders: Dict[str, Any] = {}
        self.feature_names_: List[str] = []
        
        # Feature definitions
        self.numeric_features = [
            'hubspotscore', 'num_unique_conversion_events', 'num_conversion_events',
            'num_associated_deals', 'nombre_de_transactions_terminees', 
            'hs_analytics_num_page_views', 'typeform_submissions_count',
            'avg_response_time_minutes', 'unique_forms_count', 'response_completion_rate',
            'email_count', 'call_count', 'avg_call_duration', 'meeting_count',
            'ticket_count', 'days_since_last_engagement', 'modjo_call_count',
            'avg_call_duration_modjo', 'avg_prequalification_score', 'avg_ice_breaker_score',
            'avg_discovery_score', 'avg_sales_consolidation_score', 'avg_sales_video_score',
            'associated_deals_count', 'total_engagement_score'
        ]
        
        self.categorical_features = [
            'statut_du_lead', 'ip_country', 'source_lead', 'hubspot_score_category',
            'engagement_recency'
        ]
        
        self.target_column = 'converted'
    
    def fit(self, X: pd.DataFrame, y: Optional[pd.Series] = None) -> 'ConversionFeatureTransformer':
        """
        Fit the transformer on training data.
        
        Args:
            X: Input features DataFrame
            y: Target variable (not used)
            
        Returns:
            Self
        """
        logger.info("Fitting conversion feature transformer")
        
        # Identify available features
        available_numeric = [col for col in self.numeric_features if col in X.columns]
        available_categorical = [col for col in self.categorical_features if col in X.columns]
        
        logger.info(
            "Available features",
            numeric_count=len(available_numeric),
            categorical_count=len(available_categorical),
        )
        
        # Fit numeric scaler
        if self.scale_numeric and available_numeric:
            numeric_data = X[available_numeric].fillna(0)
            self.numeric_scaler.fit(numeric_data)
            logger.debug("Numeric scaler fitted", features=available_numeric)
        
        # Fit categorical encoders
        if self.encode_categorical and available_categorical:
            for col in available_categorical:
                # Check cardinality
                unique_values = X[col].nunique()
                if unique_values <= self.max_categorical_cardinality:
                    # Use one-hot encoding for low cardinality
                    encoder = OneHotEncoder(sparse_output=False, handle_unknown='ignore')
                    encoder.fit(X[[col]].fillna('unknown'))
                    self.categorical_encoders[col] = encoder
                    logger.debug(f"OneHot encoder fitted for {col}", unique_values=unique_values)
                else:
                    # Use label encoding for high cardinality
                    encoder = LabelEncoder()
                    encoder.fit(X[col].fillna('unknown'))
                    self.categorical_encoders[col] = encoder
                    logger.debug(f"Label encoder fitted for {col}", unique_values=unique_values)
        
        # Store feature names for later use
        self._build_feature_names(X)
        
        logger.info("Conversion feature transformer fitted successfully")
        return self
    
    def transform(self, X: pd.DataFrame) -> np.ndarray:
        """
        Transform features.
        
        Args:
            X: Input features DataFrame
            
        Returns:
            Transformed features array
        """
        logger.debug("Transforming conversion features", rows=len(X))
        
        transformed_features = []
        
        # Transform numeric features
        available_numeric = [col for col in self.numeric_features if col in X.columns]
        if available_numeric:
            numeric_data = X[available_numeric].fillna(0)
            if self.scale_numeric and self.numeric_scaler:
                numeric_transformed = self.numeric_scaler.transform(numeric_data)
            else:
                numeric_transformed = numeric_data.values
            transformed_features.append(numeric_transformed)
        
        # Transform categorical features
        available_categorical = [col for col in self.categorical_features if col in X.columns]
        if available_categorical and self.encode_categorical:
            for col in available_categorical:
                if col in self.categorical_encoders:
                    encoder = self.categorical_encoders[col]
                    col_data = X[[col]].fillna('unknown')
                    
                    if isinstance(encoder, OneHotEncoder):
                        encoded = encoder.transform(col_data)
                    else:  # LabelEncoder
                        # Handle unknown categories for LabelEncoder
                        col_series = col_data[col]
                        encoded_series = col_series.map(
                            lambda x: encoder.transform([x])[0] 
                            if x in encoder.classes_ else -1
                        )
                        encoded = encoded_series.values.reshape(-1, 1)
                    
                    transformed_features.append(encoded)
        
        # Create interaction features
        if self.create_interaction_features and len(transformed_features) > 0:
            base_features = np.concatenate(transformed_features, axis=1)
            interaction_features = self._create_interaction_features(base_features, X)
            if interaction_features.size > 0:
                transformed_features.append(interaction_features)
        
        # Combine all features
        if transformed_features:
            result = np.concatenate(transformed_features, axis=1)
        else:
            result = np.empty((len(X), 0))
        
        logger.debug("Conversion features transformed", output_shape=result.shape)
        return result
    
    def _create_interaction_features(self, base_features: np.ndarray, X: pd.DataFrame) -> np.ndarray:
        """Create interaction features."""
        interactions = []
        
        # Engagement score interactions
        if 'total_engagement_score' in X.columns and 'hubspotscore' in X.columns:
            engagement_score = X['total_engagement_score'].fillna(0).values
            hubspot_score = X['hubspotscore'].fillna(0).values
            interactions.append((engagement_score * hubspot_score).reshape(-1, 1))
        
        # Response quality interactions
        if 'response_completion_rate' in X.columns and 'typeform_submissions_count' in X.columns:
            completion_rate = X['response_completion_rate'].fillna(0).values
            submission_count = X['typeform_submissions_count'].fillna(0).values
            interactions.append((completion_rate * submission_count).reshape(-1, 1))
        
        # Modjo score interactions
        modjo_score_cols = [col for col in X.columns if 'avg_' in col and 'score' in col]
        if len(modjo_score_cols) >= 2:
            modjo_scores = X[modjo_score_cols].fillna(0).values
            avg_modjo_score = np.mean(modjo_scores, axis=1).reshape(-1, 1)
            interactions.append(avg_modjo_score)
        
        if interactions:
            return np.concatenate(interactions, axis=1)
        else:
            return np.empty((len(X), 0))
    
    def _build_feature_names(self, X: pd.DataFrame) -> None:
        """Build feature names for the transformed output."""
        feature_names = []
        
        # Numeric feature names
        available_numeric = [col for col in self.numeric_features if col in X.columns]
        if self.scale_numeric:
            feature_names.extend([f"{col}_scaled" for col in available_numeric])
        else:
            feature_names.extend(available_numeric)
        
        # Categorical feature names
        available_categorical = [col for col in self.categorical_features if col in X.columns]
        if self.encode_categorical:
            for col in available_categorical:
                if col in self.categorical_encoders:
                    encoder = self.categorical_encoders[col]
                    if isinstance(encoder, OneHotEncoder):
                        categories = encoder.get_feature_names_out([col])
                        feature_names.extend(categories)
                    else:  # LabelEncoder
                        feature_names.append(f"{col}_encoded")
        
        # Interaction feature names
        if self.create_interaction_features:
            feature_names.extend([
                "engagement_hubspot_interaction",
                "response_quality_interaction", 
                "avg_modjo_score"
            ])
        
        self.feature_names_ = feature_names
    
    def get_feature_names_out(self, input_features: Optional[List[str]] = None) -> List[str]:
        """Get output feature names."""
        return self.feature_names_


class ChannelTimingFeatureTransformer(BaseEstimator, TransformerMixin):
    """
    Feature transformer for channel and timing optimization.
    
    Handles preprocessing of communication patterns and timing data
    for optimal channel and timing prediction.
    """
    
    def __init__(
        self,
        scale_numeric: bool = True,
        encode_categorical: bool = True,
        create_time_features: bool = True,
    ):
        """
        Initialize channel timing feature transformer.
        
        Args:
            scale_numeric: Whether to scale numeric features
            encode_categorical: Whether to encode categorical features
            create_time_features: Whether to create time-based features
        """
        self.scale_numeric = scale_numeric
        self.encode_categorical = encode_categorical
        self.create_time_features = create_time_features
        
        # Transformers
        self.numeric_scaler = StandardScaler() if scale_numeric else None
        self.categorical_encoders: Dict[str, Any] = {}
        self.feature_names_: List[str] = []
        
        # Feature definitions
        self.numeric_features = [
            'montant', 'days_to_close', 'total_emails', 'avg_email_hour',
            'total_calls', 'avg_call_duration', 'inbound_calls', 'outbound_calls',
            'total_meetings', 'creation_hour', 'creation_day_of_week', 'creation_month'
        ]
        
        self.categorical_features = [
            'dealstage', 'preferred_channel', 'optimal_time_slot', 'optimal_day_type'
        ]
        
        self.target_column = 'recommended_channel'
    
    def fit(self, X: pd.DataFrame, y: Optional[pd.Series] = None) -> 'ChannelTimingFeatureTransformer':
        """Fit the transformer on training data."""
        logger.info("Fitting channel timing feature transformer")
        
        # Identify available features
        available_numeric = [col for col in self.numeric_features if col in X.columns]
        available_categorical = [col for col in self.categorical_features if col in X.columns]
        
        # Fit numeric scaler
        if self.scale_numeric and available_numeric:
            numeric_data = X[available_numeric].fillna(0)
            self.numeric_scaler.fit(numeric_data)
        
        # Fit categorical encoders
        if self.encode_categorical and available_categorical:
            for col in available_categorical:
                encoder = OneHotEncoder(sparse_output=False, handle_unknown='ignore')
                encoder.fit(X[[col]].fillna('unknown'))
                self.categorical_encoders[col] = encoder
        
        self._build_feature_names(X)
        
        logger.info("Channel timing feature transformer fitted successfully")
        return self
    
    def transform(self, X: pd.DataFrame) -> np.ndarray:
        """Transform features."""
        logger.debug("Transforming channel timing features", rows=len(X))
        
        transformed_features = []
        
        # Transform numeric features
        available_numeric = [col for col in self.numeric_features if col in X.columns]
        if available_numeric:
            numeric_data = X[available_numeric].fillna(0)
            if self.scale_numeric and self.numeric_scaler:
                numeric_transformed = self.numeric_scaler.transform(numeric_data)
            else:
                numeric_transformed = numeric_data.values
            transformed_features.append(numeric_transformed)
        
        # Transform categorical features
        available_categorical = [col for col in self.categorical_features if col in X.columns]
        if available_categorical and self.encode_categorical:
            for col in available_categorical:
                if col in self.categorical_encoders:
                    encoder = self.categorical_encoders[col]
                    col_data = X[[col]].fillna('unknown')
                    encoded = encoder.transform(col_data)
                    transformed_features.append(encoded)
        
        # Create time-based features
        if self.create_time_features:
            time_features = self._create_time_features(X)
            if time_features.size > 0:
                transformed_features.append(time_features)
        
        # Combine all features
        if transformed_features:
            result = np.concatenate(transformed_features, axis=1)
        else:
            result = np.empty((len(X), 0))
        
        logger.debug("Channel timing features transformed", output_shape=result.shape)
        return result
    
    def _create_time_features(self, X: pd.DataFrame) -> np.ndarray:
        """Create time-based features."""
        time_features = []
        
        # Hour-based features (cyclical encoding)
        if 'creation_hour' in X.columns:
            hours = X['creation_hour'].fillna(12).values
            hour_sin = np.sin(2 * np.pi * hours / 24).reshape(-1, 1)
            hour_cos = np.cos(2 * np.pi * hours / 24).reshape(-1, 1)
            time_features.extend([hour_sin, hour_cos])
        
        # Day of week features (cyclical encoding)
        if 'creation_day_of_week' in X.columns:
            days = X['creation_day_of_week'].fillna(1).values
            day_sin = np.sin(2 * np.pi * days / 7).reshape(-1, 1)
            day_cos = np.cos(2 * np.pi * days / 7).reshape(-1, 1)
            time_features.extend([day_sin, day_cos])
        
        # Month features (cyclical encoding)
        if 'creation_month' in X.columns:
            months = X['creation_month'].fillna(6).values
            month_sin = np.sin(2 * np.pi * months / 12).reshape(-1, 1)
            month_cos = np.cos(2 * np.pi * months / 12).reshape(-1, 1)
            time_features.extend([month_sin, month_cos])
        
        if time_features:
            return np.concatenate(time_features, axis=1)
        else:
            return np.empty((len(X), 0))
    
    def _build_feature_names(self, X: pd.DataFrame) -> None:
        """Build feature names for the transformed output."""
        feature_names = []
        
        # Numeric feature names
        available_numeric = [col for col in self.numeric_features if col in X.columns]
        if self.scale_numeric:
            feature_names.extend([f"{col}_scaled" for col in available_numeric])
        else:
            feature_names.extend(available_numeric)
        
        # Categorical feature names
        available_categorical = [col for col in self.categorical_features if col in X.columns]
        if self.encode_categorical:
            for col in available_categorical:
                if col in self.categorical_encoders:
                    encoder = self.categorical_encoders[col]
                    categories = encoder.get_feature_names_out([col])
                    feature_names.extend(categories)
        
        # Time feature names
        if self.create_time_features:
            time_feature_names = [
                "hour_sin", "hour_cos", "day_sin", "day_cos", "month_sin", "month_cos"
            ]
            feature_names.extend(time_feature_names)
        
        self.feature_names_ = feature_names
    
    def get_feature_names_out(self, input_features: Optional[List[str]] = None) -> List[str]:
        """Get output feature names."""
        return self.feature_names_
