"""
Unified Feature Engineering Pipeline for Sensei AI v1.0

Consolidates all feature engineering capabilities into a single, optimized pipeline.
Combines basic transformations, advanced features, and multi-source data integration.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
import logging
from datetime import datetime, timedelta
from google.cloud import bigquery
from sklearn.preprocessing import LabelEncoder, StandardScaler
import warnings
warnings.filterwarnings("ignore")

logger = logging.getLogger(__name__)


class UnifiedFeatureEngineer:
    """
    Unified feature engineering pipeline combining all data sources and transformations.
    
    This class consolidates:
    - Basic data type fixes and transformations
    - Derived features creation
    - Typeform data integration
    - Modjo conversation analysis
    - Multi-channel engagement features
    - Advanced synthesis features
    """
    
    def __init__(self, client: bigquery.Client):
        self.client = client
        self.label_encoders = {}
        self.scaler = StandardScaler()
        self.feature_metadata = {}
        
    def process_pipeline(self, base_df: pd.DataFrame, 
                        include_advanced: bool = True) -> pd.DataFrame:
        """
        Complete feature engineering pipeline.
        
        Args:
            base_df: Base DataFrame with contact data
            include_advanced: Whether to include advanced features (Typeform, Modjo)
            
        Returns:
            DataFrame with all engineered features
        """
        logger.info("🚀 Starting unified feature engineering pipeline...")
        
        # Step 1: Basic data processing
        df = self._fix_data_types(base_df.copy())
        logger.info("✅ Step 1: Data types fixed")
        
        # Step 2: Create derived features
        df = self._create_derived_features(df)
        logger.info("✅ Step 2: Derived features created")
        
        # Step 3: Encode categorical features
        df = self._encode_categorical_features(df)
        logger.info("✅ Step 3: Categorical features encoded")
        
        # Step 4: Create target variable
        df = self._create_target_variable(df)
        logger.info("✅ Step 4: Target variable created")
        
        # Step 5: Advanced features (if requested)
        if include_advanced and 'id_contact' in df.columns:
            df = self._add_advanced_features(df)
            logger.info("✅ Step 5: Advanced features added")
        
        # Step 6: Synthesis features
        df = self._create_synthesis_features(df)
        logger.info("✅ Step 6: Synthesis features created")
        
        logger.info(f"🎯 Pipeline complete: {len(df)} rows, {len(df.columns)} features")
        return df
    
    def _fix_data_types(self, df: pd.DataFrame) -> pd.DataFrame:
        """Fix data types for numeric columns."""
        numeric_columns = [
            'hubspotscore', 'num_conversion_events', 'num_unique_conversion_events',
            'num_associated_deals', 'hs_analytics_num_page_views', 
            'nombre_de_transactions_terminees'
        ]
        
        for col in numeric_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
        
        return df
    
    def _create_derived_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create derived features from existing columns."""
        # Engagement ratio
        if 'num_conversion_events' in df.columns and 'hs_analytics_num_page_views' in df.columns:
            df['engagement_ratio'] = df['num_conversion_events'] / (df['hs_analytics_num_page_views'] + 1)
        
        # Composite score
        if 'hubspotscore' in df.columns and 'num_conversion_events' in df.columns:
            df['composite_score'] = (
                df['hubspotscore'].fillna(0) * 0.7 + 
                df['num_conversion_events'].fillna(0) * 10
            )
        
        # Temporal features
        if 'dt_creation_contact' in df.columns:
            now = pd.Timestamp.now(tz='UTC')
            df['dt_creation_contact'] = pd.to_datetime(df['dt_creation_contact'], utc=True)
            df['days_since_creation'] = (now - df['dt_creation_contact']).dt.days
            df['creation_month'] = df['dt_creation_contact'].dt.month
            df['creation_weekday'] = df['dt_creation_contact'].dt.weekday
        
        # HubSpot score categorization
        if 'hubspotscore' in df.columns:
            df['hubspot_category'] = pd.cut(
                df['hubspotscore'].fillna(0),
                bins=[0, 30, 70, 100],
                labels=['low', 'medium', 'high']
            )
        
        return df
    
    def _encode_categorical_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Encode categorical features."""
        categorical_columns = ['statut_du_lead', 'ip_country', 'hubspot_category']
        
        for col in categorical_columns:
            if col in df.columns:
                if col not in self.label_encoders:
                    self.label_encoders[col] = LabelEncoder()
                
                # Handle categorical data
                if hasattr(df[col], 'cat'):
                    if 'unknown' not in df[col].cat.categories:
                        df[col] = df[col].cat.add_categories(['unknown'])
                    col_values = df[col].fillna('unknown').astype(str)
                else:
                    col_values = df[col].fillna('unknown').astype(str)
                
                df[f'{col}_encoded'] = self.label_encoders[col].fit_transform(col_values)
        
        return df
    
    def _create_target_variable(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create target variable for ML."""
        if 'num_associated_deals' in df.columns:
            df['converted'] = (df['num_associated_deals'].fillna(0) > 0).astype(int)
        else:
            # Fallback: synthetic target based on composite score
            if 'composite_score' in df.columns:
                threshold = df['composite_score'].quantile(0.8)
                df['converted'] = (df['composite_score'] > threshold).astype(int)
            else:
                # Last resort: intelligent synthetic target
                np.random.seed(42)
                base_prob = 0.15
                if 'hubspotscore' in df.columns:
                    prob = base_prob + (df['hubspotscore'].fillna(0) / 100) * 0.3
                else:
                    prob = base_prob
                df['converted'] = np.random.binomial(1, prob, len(df))
        
        return df
    
    def _add_advanced_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add advanced features from external sources."""
        contact_ids = df['id_contact'].astype(str).tolist()
        
        # Typeform features
        typeform_df = self._extract_typeform_features(contact_ids)
        if len(typeform_df) > 0:
            df = df.merge(typeform_df, on='id_contact', how='left')
        
        # Modjo features
        modjo_df = self._extract_modjo_features(contact_ids)
        if len(modjo_df) > 0:
            df = df.merge(modjo_df, on='id_contact', how='left')
        
        # Multi-channel features
        multichannel_df = self._extract_multichannel_features(contact_ids)
        if len(multichannel_df) > 0:
            df = df.merge(multichannel_df, on='id_contact', how='left')
        
        return df
    
    def _extract_typeform_features(self, contact_ids: List[str]) -> pd.DataFrame:
        """Extract Typeform features."""
        if not contact_ids:
            return pd.DataFrame()
        
        ids_str = "', '".join(contact_ids)
        query = f"""
        SELECT 
            CAST(sf.id_prospect AS STRING) as id_contact,
            COUNT(DISTINCT sf.id_soumission_formulaire) as nb_soumissions_typeform,
            AVG(sf.duree_reponses_minutes) as duree_moyenne_typeform,
            COUNT(DISTINCT r.id_reponse) as nb_reponses_typeform
        FROM `datalake-sensei.serving_layer.vw_fact_soumission_formulaire` sf
        LEFT JOIN `datalake-sensei.serving_layer.vw_dim_reponse` r 
            ON sf.id_soumission_formulaire = r.id_soumission_formulaire
        WHERE sf.id_prospect IN ('{ids_str}')
        GROUP BY sf.id_prospect
        """
        
        try:
            result = self.client.query(query).result()
            df = result.to_dataframe()
            if len(df) > 0:
                df['id_contact'] = df['id_contact'].astype(str)
            return df
        except Exception as e:
            logger.warning(f"⚠️ Typeform extraction failed: {e}")
            return pd.DataFrame()
    
    def _extract_modjo_features(self, contact_ids: List[str]) -> pd.DataFrame:
        """Extract Modjo features."""
        if not contact_ids:
            return pd.DataFrame()
        
        ids_str = "', '".join(contact_ids)
        query = f"""
        SELECT 
            CAST(fc.contactCrmId AS STRING) as id_contact,
            COUNT(DISTINCT fc.callId) as nb_appels_modjo,
            AVG(fc.duration) as duree_moyenne_modjo,
            COUNT(DISTINCT fc.dealId) as nb_deals_modjo
        FROM `datalake-sensei.serving_layer.vw_fact_modjo_call` fc
        WHERE CAST(fc.contactCrmId AS STRING) IN ('{ids_str}')
        GROUP BY fc.contactCrmId
        """
        
        try:
            result = self.client.query(query).result()
            df = result.to_dataframe()
            if len(df) > 0:
                df['id_contact'] = df['id_contact'].astype(str)
            return df
        except Exception as e:
            logger.warning(f"⚠️ Modjo extraction failed: {e}")
            return pd.DataFrame()
    
    def _extract_multichannel_features(self, contact_ids: List[str]) -> pd.DataFrame:
        """Extract multi-channel engagement features."""
        if not contact_ids:
            return pd.DataFrame()
        
        ids_str = "', '".join(contact_ids)
        query = f"""
        SELECT 
            CAST(c.id_contact AS STRING) as id_contact,
            COUNT(DISTINCT e.id_email) as nb_emails_total,
            COUNT(DISTINCT a.id_appel) as nb_appels_hubspot,
            COUNT(DISTINCT r.id_reunion) as nb_reunions
        FROM `datalake-sensei.serving_layer.vw_dim_contact` c
        LEFT JOIN `datalake-sensei.serving_layer.vw_dim_email` e 
            ON CAST(c.id_contact AS STRING) IN (CAST(e.id_contact_emetteur AS STRING), CAST(e.id_contact_destinataire AS STRING))
        LEFT JOIN `datalake-sensei.serving_layer.vw_dim_appel` a 
            ON CAST(c.id_contact AS STRING) = CAST(a.hs_call_callee_object_type AS STRING)
        LEFT JOIN `datalake-sensei.serving_layer.vw_dim_reunion` r 
            ON CAST(c.id_contact AS STRING) = r.hs_created_by
        WHERE CAST(c.id_contact AS STRING) IN ('{ids_str}')
        GROUP BY c.id_contact
        """
        
        try:
            result = self.client.query(query).result()
            df = result.to_dataframe()
            if len(df) > 0:
                df['id_contact'] = df['id_contact'].astype(str)
            return df
        except Exception as e:
            logger.warning(f"⚠️ Multi-channel extraction failed: {e}")
            return pd.DataFrame()
    
    def _create_synthesis_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create synthesis features combining multiple sources."""
        # Global engagement score
        engagement_cols = ['nb_soumissions_typeform', 'nb_appels_modjo', 'nb_emails_total', 'nb_reunions']
        available_engagement_cols = [col for col in engagement_cols if col in df.columns]
        
        if available_engagement_cols:
            df['engagement_global_score'] = df[available_engagement_cols].fillna(0).sum(axis=1)
        
        # Quality interaction score
        quality_cols = ['duree_moyenne_typeform', 'duree_moyenne_modjo']
        available_quality_cols = [col for col in quality_cols if col in df.columns]
        
        if len(available_quality_cols) >= 1:
            for col in available_quality_cols:
                if col in df.columns and df[col].max() > df[col].min():
                    df[f'{col}_normalized'] = (df[col] - df[col].min()) / (df[col].max() - df[col].min())
            
            normalized_cols = [f'{col}_normalized' for col in available_quality_cols if f'{col}_normalized' in df.columns]
            if normalized_cols:
                df['qualite_interactions_score'] = df[normalized_cols].fillna(0.5).mean(axis=1)
        
        # Prospect maturity
        maturity_indicators = ['days_since_creation']
        available_maturity = [col for col in maturity_indicators if col in df.columns]
        
        if available_maturity:
            df['maturite_prospect'] = df[available_maturity].fillna(0).max(axis=1)
        
        return df
    
    def get_feature_names(self, include_advanced: bool = True) -> List[str]:
        """Get list of all feature names that will be created."""
        base_features = [
            'hubspotscore', 'num_conversion_events', 'engagement_ratio',
            'days_since_creation', 'composite_score', 'creation_month',
            'statut_du_lead_encoded', 'ip_country_encoded'
        ]
        
        if include_advanced:
            advanced_features = [
                'nb_soumissions_typeform', 'nb_appels_modjo', 'nb_emails_total',
                'engagement_global_score', 'qualite_interactions_score', 'maturite_prospect'
            ]
            base_features.extend(advanced_features)
        
        return base_features
    
    def get_feature_importance_metadata(self) -> Dict[str, str]:
        """Get metadata about feature importance and descriptions."""
        return {
            'hubspotscore': 'Score de qualification HubSpot',
            'engagement_ratio': 'Ratio d\'engagement calculé (conversions/vues)',
            'days_since_creation': 'Ancienneté du contact (jours)',
            'composite_score': 'Score composite HubSpot + engagement',
            'nb_soumissions_typeform': 'Nombre de formulaires Typeform soumis',
            'nb_appels_modjo': 'Nombre d\'appels Modjo enregistrés',
            'engagement_global_score': 'Score d\'engagement global tous canaux',
            'qualite_interactions_score': 'Score de qualité des interactions',
            'maturite_prospect': 'Score de maturité du prospect'
        }
