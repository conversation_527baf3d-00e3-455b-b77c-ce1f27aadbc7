"""
Feature engineering pipeline for Sensei AI.

This package provides feature extraction and transformation for:
- Conversion prediction features from Typeform, HubSpot, and Modjo
- Channel and timing optimization features
- NLP features from conversation transcripts
"""

from .feature_store import FeatureStore, get_feature_store
from .transformers import (
    ConversionFeatureTransformer,
    ChannelTimingFeatureTransformer,
    NLPFeatureTransformer,
)
from .pipeline import FeaturePipeline, create_feature_pipeline
from .validation import validate_features, FeatureValidator

__all__ = [
    "FeatureStore",
    "get_feature_store",
    "ConversionFeatureTransformer",
    "ChannelTimingFeatureTransformer", 
    "NLPFeatureTransformer",
    "FeaturePipeline",
    "create_feature_pipeline",
    "validate_features",
    "FeatureValidator",
]
