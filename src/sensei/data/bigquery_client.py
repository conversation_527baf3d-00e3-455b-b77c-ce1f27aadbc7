"""
Secure BigQuery client for Sensei AI.

Provides read-only access to BigQuery with comprehensive security controls:
- Query validation and SQL injection protection
- Cost controls with configurable limits
- Dataset access restrictions
- Comprehensive audit logging
- Performance monitoring
"""

import os
from typing import Optional, Dict, Any, List
import json
from pathlib import Path

import pandas as pd
from google.cloud import bigquery
from google.oauth2 import service_account
from google.cloud.exceptions import GoogleCloudError

from ..utils import get_logger, get_config
from ..utils.security import validate_sql_query, SecurityError, generate_audit_log_entry
from ..utils.monitoring import track_performance, track_bigquery_query

logger = get_logger(__name__)


class BigQueryError(Exception):
    """Custom exception for BigQuery operations."""
    pass


class BigQueryClient:
    """
    Secure BigQuery client with read-only access and cost controls.
    
    Features:
    - Read-only access to specified datasets
    - SQL injection protection
    - Cost controls with configurable limits
    - Comprehensive audit logging
    - Performance monitoring
    """
    
    def __init__(
        self,
        project_id: Optional[str] = None,
        credentials_path: Optional[Path] = None,
        max_bytes_billed: Optional[int] = None,
        allowed_read_datasets: Optional[List[str]] = None,
    ):
        """
        Initialize BigQuery client.
        
        Args:
            project_id: GCP project ID
            credentials_path: Path to service account credentials
            max_bytes_billed: Maximum bytes that can be billed per query
            allowed_read_datasets: List of datasets allowed for reading
        """
        self.config = get_config()
        
        # Configuration
        self.project_id = project_id or self.config.bigquery.project_id
        self.max_bytes_billed = max_bytes_billed or self.config.bigquery.max_bytes_billed
        self.allowed_read_datasets = (
            allowed_read_datasets or self.config.bigquery.allowed_read_datasets
        )
        
        if not self.project_id:
            raise BigQueryError("Project ID is required")
        
        # Setup credentials
        self._setup_credentials(credentials_path)
        
        # Initialize BigQuery client
        self.client = bigquery.Client(
            project=self.project_id,
            credentials=self.credentials,
            location=self.config.bigquery.location,
        )
        
        logger.info(
            "BigQuery client initialized",
            project_id=self.project_id,
            max_bytes_billed=self.max_bytes_billed,
            allowed_datasets=self.allowed_read_datasets,
        )
    
    def _setup_credentials(self, credentials_path: Optional[Path]) -> None:
        """Setup Google Cloud credentials."""
        try:
            if credentials_path:
                self.credentials = service_account.Credentials.from_service_account_file(
                    str(credentials_path)
                )
                logger.info("Credentials loaded from file", path=str(credentials_path))
            else:
                # Try to get credentials from config
                creds_path = self.config.get_credentials_path()
                self.credentials = service_account.Credentials.from_service_account_file(
                    str(creds_path)
                )
                logger.info("Credentials loaded from config", path=str(creds_path))
                
        except Exception as e:
            logger.error("Failed to load credentials", error=str(e))
            raise BigQueryError(f"Failed to load credentials: {e}")
    
    def query_dataframe(
        self,
        sql: str,
        params: Optional[Dict[str, Any]] = None,
        max_bytes_billed: Optional[int] = None,
        dry_run: bool = False,
    ) -> pd.DataFrame:
        """
        Execute SQL query and return results as pandas DataFrame.
        
        Args:
            sql: SQL query to execute
            params: Query parameters for parameterized queries
            max_bytes_billed: Override default max bytes billed
            dry_run: If True, validate query without executing
            
        Returns:
            DataFrame with query results
            
        Raises:
            BigQueryError: If query fails or violates security policies
            SecurityError: If query violates security constraints
        """
        # Validate query security
        try:
            validate_sql_query(sql, self.allowed_read_datasets)
        except SecurityError as e:
            self._log_security_violation(sql, str(e))
            raise BigQueryError(f"Security violation: {e}")
        
        # Configure query job
        job_config = bigquery.QueryJobConfig()
        job_config.maximum_bytes_billed = max_bytes_billed or self.max_bytes_billed
        job_config.dry_run = dry_run
        job_config.use_query_cache = True
        
        # Add query parameters if provided
        if params:
            job_config.query_parameters = self._build_query_parameters(params)
        
        # Execute query with monitoring
        with track_performance(
            "bigquery_query",
            labels={
                "dataset": self._extract_dataset_from_query(sql),
                "dry_run": str(dry_run),
            }
        ):
            try:
                logger.info(
                    "Executing BigQuery query",
                    sql_preview=sql[:200] + "..." if len(sql) > 200 else sql,
                    max_bytes_billed=job_config.maximum_bytes_billed,
                    dry_run=dry_run,
                )
                
                query_job = self.client.query(sql, job_config=job_config)
                
                if dry_run:
                    logger.info(
                        "Query validation successful",
                        total_bytes_processed=query_job.total_bytes_processed,
                        job_id=query_job.job_id,
                    )
                    return pd.DataFrame()  # Return empty DataFrame for dry run
                
                # Get results
                df = query_job.to_dataframe()
                
                # Log successful execution
                self._log_successful_query(query_job, df)
                
                # Track metrics
                track_bigquery_query(
                    dataset=self._extract_dataset_from_query(sql),
                    status="success",
                    bytes_processed=query_job.total_bytes_processed,
                )
                
                return df
                
            except GoogleCloudError as e:
                self._log_query_error(sql, e)
                track_bigquery_query(
                    dataset=self._extract_dataset_from_query(sql),
                    status="error",
                )
                raise BigQueryError(f"BigQuery error: {e}")
            
            except Exception as e:
                self._log_query_error(sql, e)
                track_bigquery_query(
                    dataset=self._extract_dataset_from_query(sql),
                    status="error",
                )
                raise BigQueryError(f"Unexpected error: {e}")
    
    def validate_query(self, sql: str) -> Dict[str, Any]:
        """
        Validate query without executing it.
        
        Args:
            sql: SQL query to validate
            
        Returns:
            Dictionary with validation results
        """
        try:
            # Security validation
            validate_sql_query(sql, self.allowed_read_datasets)
            
            # BigQuery validation (dry run)
            job_config = bigquery.QueryJobConfig()
            job_config.dry_run = True
            job_config.use_query_cache = True
            
            query_job = self.client.query(sql, job_config=job_config)
            
            return {
                "valid": True,
                "total_bytes_processed": query_job.total_bytes_processed,
                "estimated_cost_usd": self._estimate_query_cost(
                    query_job.total_bytes_processed
                ),
                "job_id": query_job.job_id,
            }
            
        except (SecurityError, GoogleCloudError, BigQueryError) as e:
            return {
                "valid": False,
                "error": str(e),
                "error_type": type(e).__name__,
            }
    
    def get_table_schema(self, dataset_id: str, table_id: str) -> List[Dict[str, Any]]:
        """
        Get schema information for a table.
        
        Args:
            dataset_id: Dataset ID
            table_id: Table ID
            
        Returns:
            List of field schemas
        """
        if dataset_id not in self.allowed_read_datasets:
            raise BigQueryError(
                f"Access to dataset '{dataset_id}' not allowed. "
                f"Allowed datasets: {self.allowed_read_datasets}"
            )
        
        try:
            table_ref = self.client.dataset(dataset_id).table(table_id)
            table = self.client.get_table(table_ref)
            
            schema = []
            for field in table.schema:
                schema.append({
                    "name": field.name,
                    "type": field.field_type,
                    "mode": field.mode,
                    "description": field.description,
                })
            
            logger.info(
                "Retrieved table schema",
                dataset_id=dataset_id,
                table_id=table_id,
                field_count=len(schema),
            )
            
            return schema
            
        except GoogleCloudError as e:
            logger.error(
                "Failed to get table schema",
                dataset_id=dataset_id,
                table_id=table_id,
                error=str(e),
            )
            raise BigQueryError(f"Failed to get table schema: {e}")
    
    def list_tables(self, dataset_id: str) -> List[str]:
        """
        List tables in a dataset.
        
        Args:
            dataset_id: Dataset ID
            
        Returns:
            List of table names
        """
        if dataset_id not in self.allowed_read_datasets:
            raise BigQueryError(
                f"Access to dataset '{dataset_id}' not allowed. "
                f"Allowed datasets: {self.allowed_read_datasets}"
            )
        
        try:
            dataset_ref = self.client.dataset(dataset_id)
            tables = list(self.client.list_tables(dataset_ref))
            table_names = [table.table_id for table in tables]
            
            logger.info(
                "Listed tables in dataset",
                dataset_id=dataset_id,
                table_count=len(table_names),
            )
            
            return table_names
            
        except GoogleCloudError as e:
            logger.error(
                "Failed to list tables",
                dataset_id=dataset_id,
                error=str(e),
            )
            raise BigQueryError(f"Failed to list tables: {e}")
    
    def _build_query_parameters(self, params: Dict[str, Any]) -> List[bigquery.QueryParameter]:
        """Build BigQuery query parameters from dictionary."""
        query_params = []
        for key, value in params.items():
            if isinstance(value, str):
                param_type = "STRING"
            elif isinstance(value, int):
                param_type = "INT64"
            elif isinstance(value, float):
                param_type = "FLOAT64"
            elif isinstance(value, bool):
                param_type = "BOOL"
            else:
                param_type = "STRING"
                value = str(value)
            
            query_params.append(
                bigquery.ScalarQueryParameter(key, param_type, value)
            )
        
        return query_params
    
    def _extract_dataset_from_query(self, sql: str) -> str:
        """Extract primary dataset from SQL query."""
        import re
        
        # Look for dataset.table patterns
        pattern = r"\b(\w+)\.(\w+)"
        matches = re.findall(pattern, sql, re.IGNORECASE)
        
        if matches:
            return matches[0][0]  # Return first dataset found
        
        return "unknown"
    
    def _estimate_query_cost(self, bytes_processed: int) -> float:
        """Estimate query cost in USD based on bytes processed."""
        # BigQuery pricing: $5 per TB processed
        tb_processed = bytes_processed / (1024**4)
        return tb_processed * 5.0
    
    def _log_successful_query(self, query_job: bigquery.QueryJob, df: pd.DataFrame) -> None:
        """Log successful query execution."""
        logger.info(
            "Query executed successfully",
            job_id=query_job.job_id,
            rows_returned=len(df),
            bytes_processed=query_job.total_bytes_processed,
            bytes_billed=query_job.total_bytes_billed,
            estimated_cost_usd=self._estimate_query_cost(query_job.total_bytes_processed),
        )
        
        # Generate audit log
        audit_entry = generate_audit_log_entry(
            action="bigquery_query_success",
            resource=f"project:{self.project_id}",
            details={
                "job_id": query_job.job_id,
                "rows_returned": len(df),
                "bytes_processed": query_job.total_bytes_processed,
                "bytes_billed": query_job.total_bytes_billed,
            },
        )
        logger.info("Audit log", **audit_entry)
    
    def _log_query_error(self, sql: str, error: Exception) -> None:
        """Log query execution error."""
        logger.error(
            "Query execution failed",
            sql_preview=sql[:200] + "..." if len(sql) > 200 else sql,
            error=str(error),
            error_type=type(error).__name__,
        )
        
        # Generate audit log
        audit_entry = generate_audit_log_entry(
            action="bigquery_query_error",
            resource=f"project:{self.project_id}",
            details={
                "error": str(error),
                "error_type": type(error).__name__,
                "sql_preview": sql[:200],
            },
        )
        audit_entry["severity"] = "ERROR"
        logger.error("Audit log", **audit_entry)
    
    def _log_security_violation(self, sql: str, violation: str) -> None:
        """Log security violation."""
        logger.warning(
            "Security violation detected",
            sql_preview=sql[:200] + "..." if len(sql) > 200 else sql,
            violation=violation,
        )
        
        # Generate audit log
        audit_entry = generate_audit_log_entry(
            action="security_violation",
            resource=f"project:{self.project_id}",
            details={
                "violation": violation,
                "sql_preview": sql[:200],
            },
        )
        audit_entry["severity"] = "WARNING"
        logger.warning("Audit log", **audit_entry)


# Global client instance
_bigquery_client: Optional[BigQueryClient] = None


def get_bigquery_client() -> BigQueryClient:
    """
    Get the global BigQuery client instance.
    
    Returns:
        BigQuery client instance
    """
    global _bigquery_client
    if _bigquery_client is None:
        _bigquery_client = BigQueryClient()
    return _bigquery_client
