"""
SQL query builder for Sensei AI feature engineering.

Provides pre-built, validated SQL queries for extracting features from the serving layer.
All queries are designed for read-only access with security validation.
"""

from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import textwrap

from ..utils import get_logger

logger = get_logger(__name__)


class QueryBuilder:
    """Builder for SQL queries with security validation."""
    
    def __init__(self, dataset_name: str = "serving_layer"):
        """
        Initialize query builder.
        
        Args:
            dataset_name: Name of the BigQuery dataset
        """
        self.dataset = dataset_name
    
    def build_conversion_features_query(
        self,
        limit: Optional[int] = None,
        date_filter_days: Optional[int] = 90,
    ) -> str:
        """
        Build query for conversion prediction features.
        
        Combines data from Typeform, HubSpot, and Modjo to create features
        for predicting prospect conversion probability.
        
        Args:
            limit: Maximum number of rows to return
            date_filter_days: Number of days to look back for data
            
        Returns:
            SQL query string
        """
        date_filter = ""
        if date_filter_days:
            date_filter = f"""
            AND c.dt_creation_contact >= DATE_SUB(CURRENT_DATE(), INTERVAL {date_filter_days} DAY)
            """
        
        limit_clause = f"LIMIT {limit}" if limit else ""
        
        query = f"""
        WITH prospect_base AS (
            SELECT DISTINCT
                c.id_contact,
                c.statut_du_lead,
                c.hubspotscore,
                c.num_unique_conversion_events,
                c.num_conversion_events,
                c.num_associated_deals,
                c.nombre_de_transactions_terminees,
                c.hs_analytics_num_page_views,
                c.ip_country,
                c.dt_creation_contact,
                c.dt_modification_contact,
                -- Target variable: has completed transaction
                CASE 
                    WHEN c.nombre_de_transactions_terminees > 0 THEN 1 
                    ELSE 0 
                END AS converted
            FROM `{self.dataset}.vw_dim_contact` c
            WHERE c.id_contact IS NOT NULL
            {date_filter}
        ),
        
        typeform_features AS (
            SELECT 
                tf.id_prospect,
                COUNT(DISTINCT tf.id_soumission_formulaire) AS typeform_submissions_count,
                AVG(sf.duree_reponses_minutes) AS avg_response_time_minutes,
                COUNT(DISTINCT tf.nom_formulaire) AS unique_forms_count,
                -- Response quality indicators
                COUNT(CASE WHEN tf.valeur_reponse IS NOT NULL AND tf.valeur_reponse != '' THEN 1 END) AS completed_responses,
                COUNT(*) AS total_responses,
                SAFE_DIVIDE(
                    COUNT(CASE WHEN tf.valeur_reponse IS NOT NULL AND tf.valeur_reponse != '' THEN 1 END),
                    COUNT(*)
                ) AS response_completion_rate
            FROM `{self.dataset}.vw_reponses_typeform_deno` tf
            LEFT JOIN `{self.dataset}.vw_fact_soumission_formulaire` sf
                ON tf.id_soumission_formulaire = sf.id_soumission_formulaire
            GROUP BY tf.id_prospect
        ),
        
        hubspot_engagement AS (
            SELECT 
                c.id_contact,
                -- Email engagement
                COUNT(DISTINCT e.id_email) AS email_count,
                -- Call engagement  
                COUNT(DISTINCT a.id_appel) AS call_count,
                AVG(a.hs_call_duration) AS avg_call_duration,
                -- Meeting engagement
                COUNT(DISTINCT r.id_reunion) AS meeting_count,
                -- Ticket/support engagement
                COUNT(DISTINCT t.id_ticket) AS ticket_count,
                -- Lead source
                l.source_lead,
                COALESCE(l.dt_dernier_echange, c.dt_creation_contact) AS last_engagement_date,
                DATE_DIFF(CURRENT_DATE(), DATE(COALESCE(l.dt_dernier_echange, c.dt_creation_contact)), DAY) AS days_since_last_engagement
            FROM `{self.dataset}.vw_dim_contact` c
            LEFT JOIN `{self.dataset}.vw_dim_email` e ON c.id_contact = e.id_contact_destinataire
            LEFT JOIN `{self.dataset}.vw_dim_appel` a ON c.id_contact = a.id_appel  -- Assuming contact linkage
            LEFT JOIN `{self.dataset}.vw_dim_reunion` r ON c.id_contact = r.id_reunion  -- Assuming contact linkage
            LEFT JOIN `{self.dataset}.vw_dim_ticket` t ON c.id_contact = t.id_ticket  -- Assuming contact linkage
            LEFT JOIN `{self.dataset}.vw_dim_leads` l ON c.id_contact = l.id_lead  -- Assuming contact linkage
            GROUP BY c.id_contact, l.source_lead, l.dt_dernier_echange, c.dt_creation_contact
        ),
        
        modjo_features AS (
            SELECT 
                mc.contactCrmId AS id_contact,
                COUNT(DISTINCT fc.callId) AS modjo_call_count,
                AVG(fc.duration) AS avg_call_duration_modjo,
                AVG(cs.score_appel_prequai) AS avg_prequalification_score,
                AVG(cs.score_ice_breaker) AS avg_ice_breaker_score,
                AVG(cs.score_phase_decouverte) AS avg_discovery_score,
                AVG(cs.score_consolider_vente) AS avg_sales_consolidation_score,
                AVG(cs.score_visio_vente) AS avg_sales_video_score,
                COUNT(DISTINCT cs.dealId) AS associated_deals_count
            FROM `{self.dataset}.vw_dim_modjo_contact` mc
            LEFT JOIN `{self.dataset}.vw_fact_modjo_call` fc ON mc.contactId = fc.contactId
            LEFT JOIN `{self.dataset}.vw_dim_modjo_call_summary` cs ON fc.callId = cs.callId
            GROUP BY mc.contactCrmId
        )
        
        SELECT 
            pb.*,
            -- Typeform features
            COALESCE(tf.typeform_submissions_count, 0) AS typeform_submissions_count,
            COALESCE(tf.avg_response_time_minutes, 0) AS avg_response_time_minutes,
            COALESCE(tf.unique_forms_count, 0) AS unique_forms_count,
            COALESCE(tf.response_completion_rate, 0) AS response_completion_rate,
            
            -- HubSpot engagement features
            COALESCE(he.email_count, 0) AS email_count,
            COALESCE(he.call_count, 0) AS call_count,
            COALESCE(he.avg_call_duration, 0) AS avg_call_duration,
            COALESCE(he.meeting_count, 0) AS meeting_count,
            COALESCE(he.ticket_count, 0) AS ticket_count,
            COALESCE(he.source_lead, 'unknown') AS source_lead,
            COALESCE(he.days_since_last_engagement, 0) AS days_since_last_engagement,
            
            -- Modjo features
            COALESCE(mf.modjo_call_count, 0) AS modjo_call_count,
            COALESCE(mf.avg_call_duration_modjo, 0) AS avg_call_duration_modjo,
            COALESCE(mf.avg_prequalification_score, 0) AS avg_prequalification_score,
            COALESCE(mf.avg_ice_breaker_score, 0) AS avg_ice_breaker_score,
            COALESCE(mf.avg_discovery_score, 0) AS avg_discovery_score,
            COALESCE(mf.avg_sales_consolidation_score, 0) AS avg_sales_consolidation_score,
            COALESCE(mf.avg_sales_video_score, 0) AS avg_sales_video_score,
            COALESCE(mf.associated_deals_count, 0) AS associated_deals_count,
            
            -- Derived features
            CASE 
                WHEN pb.hubspotscore >= 80 THEN 'high'
                WHEN pb.hubspotscore >= 50 THEN 'medium'
                ELSE 'low'
            END AS hubspot_score_category,
            
            CASE 
                WHEN COALESCE(he.days_since_last_engagement, 0) <= 7 THEN 'recent'
                WHEN COALESCE(he.days_since_last_engagement, 0) <= 30 THEN 'moderate'
                ELSE 'old'
            END AS engagement_recency,
            
            -- Total engagement score
            (COALESCE(tf.typeform_submissions_count, 0) * 2 +
             COALESCE(he.email_count, 0) * 1 +
             COALESCE(he.call_count, 0) * 3 +
             COALESCE(he.meeting_count, 0) * 5 +
             COALESCE(mf.modjo_call_count, 0) * 4) AS total_engagement_score
            
        FROM prospect_base pb
        LEFT JOIN typeform_features tf ON CAST(pb.id_contact AS STRING) = tf.id_prospect
        LEFT JOIN hubspot_engagement he ON pb.id_contact = he.id_contact
        LEFT JOIN modjo_features mf ON pb.id_contact = mf.id_contact
        
        ORDER BY pb.dt_creation_contact DESC
        {limit_clause}
        """
        
        return textwrap.dedent(query).strip()
    
    def build_channel_timing_features_query(
        self,
        limit: Optional[int] = None,
        date_filter_days: Optional[int] = 90,
    ) -> str:
        """
        Build query for channel and timing optimization features.
        
        Args:
            limit: Maximum number of rows to return
            date_filter_days: Number of days to look back for data
            
        Returns:
            SQL query string
        """
        date_filter = ""
        if date_filter_days:
            date_filter = f"""
            AND t.dt_creation_transaction >= DATE_SUB(CURRENT_DATE(), INTERVAL {date_filter_days} DAY)
            """
        
        limit_clause = f"LIMIT {limit}" if limit else ""
        
        query = f"""
        WITH successful_interactions AS (
            SELECT 
                t.id_transaction,
                t.id_contact,
                t.closedate,
                t.createdate,
                t.dealstage,
                t.montant,
                -- Extract timing features
                EXTRACT(HOUR FROM t.createdate) AS creation_hour,
                EXTRACT(DAYOFWEEK FROM t.createdate) AS creation_day_of_week,
                EXTRACT(MONTH FROM t.createdate) AS creation_month,
                
                -- Time to close
                DATE_DIFF(DATE(t.closedate), DATE(t.createdate), DAY) AS days_to_close,
                
                -- Success indicator
                CASE 
                    WHEN t.dealstage IN ('closedwon', 'closed-won', 'won') THEN 1
                    ELSE 0
                END AS deal_won
            FROM `{self.dataset}.vw_fact_transaction` t
            WHERE t.closedate IS NOT NULL
            {date_filter}
        ),
        
        communication_patterns AS (
            SELECT 
                c.id_contact,
                -- Email patterns
                COUNT(DISTINCT e.id_email) AS total_emails,
                AVG(EXTRACT(HOUR FROM e.hs_createdate)) AS avg_email_hour,
                MODE(EXTRACT(DAYOFWEEK FROM e.hs_createdate)) AS most_common_email_day,
                
                -- Call patterns
                COUNT(DISTINCT a.id_appel) AS total_calls,
                AVG(a.hs_call_duration) AS avg_call_duration,
                COUNT(CASE WHEN a.hs_call_direction = 'INBOUND' THEN 1 END) AS inbound_calls,
                COUNT(CASE WHEN a.hs_call_direction = 'OUTBOUND' THEN 1 END) AS outbound_calls,
                
                -- Meeting patterns
                COUNT(DISTINCT r.id_reunion) AS total_meetings,
                
                -- Preferred communication channel (most used)
                CASE 
                    WHEN COUNT(DISTINCT e.id_email) > COUNT(DISTINCT a.id_appel) 
                         AND COUNT(DISTINCT e.id_email) > COUNT(DISTINCT r.id_reunion) THEN 'email'
                    WHEN COUNT(DISTINCT a.id_appel) > COUNT(DISTINCT r.id_reunion) THEN 'call'
                    WHEN COUNT(DISTINCT r.id_reunion) > 0 THEN 'meeting'
                    ELSE 'unknown'
                END AS preferred_channel
                
            FROM `{self.dataset}.vw_dim_contact` c
            LEFT JOIN `{self.dataset}.vw_dim_email` e ON c.id_contact = e.id_contact_destinataire
            LEFT JOIN `{self.dataset}.vw_dim_appel` a ON c.id_contact = a.id_appel
            LEFT JOIN `{self.dataset}.vw_dim_reunion` r ON c.id_contact = r.id_reunion
            GROUP BY c.id_contact
        )
        
        SELECT 
            si.*,
            cp.total_emails,
            cp.avg_email_hour,
            cp.most_common_email_day,
            cp.total_calls,
            cp.avg_call_duration,
            cp.inbound_calls,
            cp.outbound_calls,
            cp.total_meetings,
            cp.preferred_channel,
            
            -- Optimal timing features
            CASE 
                WHEN si.creation_hour BETWEEN 9 AND 11 THEN 'morning'
                WHEN si.creation_hour BETWEEN 12 AND 14 THEN 'lunch'
                WHEN si.creation_hour BETWEEN 15 AND 17 THEN 'afternoon'
                ELSE 'other'
            END AS optimal_time_slot,
            
            CASE 
                WHEN si.creation_day_of_week IN (2, 3, 4) THEN 'weekday_peak'
                WHEN si.creation_day_of_week IN (1, 5) THEN 'weekday_edge'
                ELSE 'weekend'
            END AS optimal_day_type,
            
            -- Success rate by channel (target for recommendation)
            cp.preferred_channel AS recommended_channel
            
        FROM successful_interactions si
        LEFT JOIN communication_patterns cp ON si.id_contact = cp.id_contact
        
        ORDER BY si.createdate DESC
        {limit_clause}
        """
        
        return textwrap.dedent(query).strip()
    
    def build_nlp_transcript_query(
        self,
        limit: Optional[int] = None,
        date_filter_days: Optional[int] = 30,
    ) -> str:
        """
        Build query for NLP analysis of Modjo transcripts.
        
        Args:
            limit: Maximum number of rows to return
            date_filter_days: Number of days to look back for data
            
        Returns:
            SQL query string
        """
        date_filter = ""
        if date_filter_days:
            date_filter = f"""
            AND cs.startDate >= DATE_SUB(CURRENT_DATE(), INTERVAL {date_filter_days} DAY)
            """
        
        limit_clause = f"LIMIT {limit}" if limit else ""
        
        query = f"""
        WITH call_transcripts AS (
            SELECT 
                cs.callId,
                cs.startDate,
                cs.title,
                cs.duration,
                cs.summary_content,
                cs.aiSummary_content,
                cs.score_appel_prequai,
                cs.score_ice_breaker,
                cs.score_phase_decouverte,
                cs.score_consolider_vente,
                cs.score_visio_vente,
                cs.dealId,
                cs.deal_name,
                
                -- Aggregate transcript content
                STRING_AGG(t.content, ' ' ORDER BY t.startTime) AS full_transcript,
                COUNT(DISTINCT t.speakerId) AS speaker_count,
                SUM(t.endTime - t.startTime) AS total_speaking_time,
                
                -- Speaking time distribution
                SUM(CASE WHEN s.type = 'user' THEN t.endTime - t.startTime ELSE 0 END) AS user_speaking_time,
                SUM(CASE WHEN s.type = 'client' THEN t.endTime - t.startTime ELSE 0 END) AS client_speaking_time
                
            FROM `{self.dataset}.vw_dim_modjo_call_summary` cs
            LEFT JOIN `{self.dataset}.vw_dim_modjo_transcript` t ON cs.callId = t.callId
            LEFT JOIN `{self.dataset}.vw_dim_modjo_speaker` s ON t.speakerId = s.speakerId
            WHERE cs.callId IS NOT NULL
            {date_filter}
            GROUP BY 
                cs.callId, cs.startDate, cs.title, cs.duration,
                cs.summary_content, cs.aiSummary_content,
                cs.score_appel_prequai, cs.score_ice_breaker,
                cs.score_phase_decouverte, cs.score_consolider_vente,
                cs.score_visio_vente, cs.dealId, cs.deal_name
        )
        
        SELECT 
            *,
            -- Derived NLP features
            LENGTH(full_transcript) AS transcript_length,
            ARRAY_LENGTH(SPLIT(full_transcript, ' ')) AS word_count,
            
            -- Speaking time ratios
            SAFE_DIVIDE(user_speaking_time, total_speaking_time) AS user_speaking_ratio,
            SAFE_DIVIDE(client_speaking_time, total_speaking_time) AS client_speaking_ratio,
            
            -- Call quality indicators
            CASE 
                WHEN score_appel_prequai >= 80 THEN 'excellent'
                WHEN score_appel_prequai >= 60 THEN 'good'
                WHEN score_appel_prequai >= 40 THEN 'average'
                ELSE 'poor'
            END AS prequalification_quality,
            
            -- Overall call score (average of all scores)
            (COALESCE(score_appel_prequai, 0) + 
             COALESCE(score_ice_breaker, 0) + 
             COALESCE(score_phase_decouverte, 0) + 
             COALESCE(score_consolider_vente, 0) + 
             COALESCE(score_visio_vente, 0)) / 5 AS overall_call_score
            
        FROM call_transcripts
        WHERE full_transcript IS NOT NULL
        
        ORDER BY startDate DESC
        {limit_clause}
        """
        
        return textwrap.dedent(query).strip()


def get_feature_query(
    query_type: str,
    dataset_name: str = "serving_layer",
    **kwargs
) -> str:
    """
    Get a pre-built feature query.
    
    Args:
        query_type: Type of query ('conversion', 'channel_timing', 'nlp_transcript')
        dataset_name: BigQuery dataset name
        **kwargs: Additional parameters for the query
        
    Returns:
        SQL query string
        
    Raises:
        ValueError: If query_type is not recognized
    """
    builder = QueryBuilder(dataset_name)
    
    if query_type == "conversion":
        return builder.build_conversion_features_query(**kwargs)
    elif query_type == "channel_timing":
        return builder.build_channel_timing_features_query(**kwargs)
    elif query_type == "nlp_transcript":
        return builder.build_nlp_transcript_query(**kwargs)
    else:
        raise ValueError(
            f"Unknown query type: {query_type}. "
            f"Supported types: conversion, channel_timing, nlp_transcript"
        )
