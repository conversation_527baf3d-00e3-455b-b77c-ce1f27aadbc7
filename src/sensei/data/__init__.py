"""
Data access layer for Sensei AI.

This package provides secure access to BigQuery data sources with:
- Read-only access to serving_layer dataset
- Cost controls and query validation
- Comprehensive audit logging
- Performance monitoring
"""

from .bigquery_client import BigQueryClient, get_bigquery_client
from .schemas import (
    TypeformSchema,
    HubspotSchema, 
    ModjoSchema,
    validate_data_schema,
)
from .queries import QueryBuilder, get_feature_query

__all__ = [
    "BigQueryClient",
    "get_bigquery_client",
    "TypeformSchema",
    "HubspotSchema",
    "ModjoSchema", 
    "validate_data_schema",
    "QueryBuilder",
    "get_feature_query",
]
