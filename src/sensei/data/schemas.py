"""
Data schemas for Sensei AI serving layer.

Defines the structure and validation for data from:
- Typeform (forms and responses)
- HubSpot (CRM data)
- <PERSON><PERSON><PERSON> (call transcriptions and analytics)
"""

from dataclasses import dataclass
from typing import List, Optional, Dict, Any, Union
from datetime import datetime
import pandas as pd

from ..utils import get_logger

logger = get_logger(__name__)


@dataclass
class TypeformSchema:
    """Schema definitions for Typeform data."""
    
    # Table: vw_dim_formulaire
    FORMULAIRE_COLUMNS = [
        "id_formulaire",
        "nom_formulaire", 
        "type_formulaire",
        "dt_creation",
        "dt_modification",
        "cd_statut_publication",
        "lb_statut_publication",
        "dt_publication",
    ]
    
    # Table: vw_dim_question
    QUESTION_COLUMNS = [
        "id_question",
        "id_formulaire",
        "texte_question",
        "type_question",
        "obligatoire",
    ]
    
    # Table: vw_dim_choix
    CHOIX_COLUMNS = [
        "id_choix",
        "texte_choix",
        "description_choix",
        "id_question",
    ]
    
    # Table: vw_dim_option
    OPTION_COLUMNS = [
        "id_option",
        "id_choix",
        "valeur_reponse",
    ]
    
    # Table: vw_fact_soumission_formulaire
    SOUMISSION_COLUMNS = [
        "id_soumission_formulaire",
        "id_prospect",
        "dt_soumission",
        "duree_reponses_minutes",
        "token",
    ]
    
    # Table: vw_dim_reponse
    REPONSE_COLUMNS = [
        "id_reponse",
        "id_soumission_formulaire",
        "id_option",
        "valeur_reponse",
        "type",
    ]
    
    # Table: vw_reponses_typeform_deno (consolidated view)
    REPONSES_DENO_COLUMNS = [
        "id_prospect",
        "id_soumission_formulaire",
        "nom_prenom",  # anonymized
        "email",  # anonymized
        "num_telephone",  # anonymized
        "nom_formulaire",
        "texte_question",
        "type_question",
        "texte_choix",
        "description_choix",
        "valeur_reponse",
    ]


@dataclass
class HubspotSchema:
    """Schema definitions for HubSpot CRM data."""
    
    # Table: vw_dim_contact
    CONTACT_COLUMNS = [
        "id_contact",
        "nom",
        "prenom",
        "email",  # anonymized
        "numero_telephone",  # anonymized
        "statut_du_lead",
        "relationship_status",
        "num_unique_conversion_events",
        "num_conversion_events",
        "num_associated_deals",
        "lead_traite",
        "ip_country",
        "hubspotscore",
        "nombre_de_transactions_terminees",
        "hs_analytics_num_page_views",
        "dt_creation_contact",
        "dt_modification_contact",
    ]
    
    # Table: vw_dim_consultant
    CONSULTANT_COLUMNS = [
        "id_consultant",
        "nom",
        "prenom",
        "email",
        "dt_creation_consultant",
        "dt_modification_consultant",
    ]
    
    # Table: vw_fact_transaction
    TRANSACTION_COLUMNS = [
        "id_transaction",
        "montant",
        "nom_consultant",
        "nom_client",
        "prenom_client",
        "nom_complet",
        "num_telephone_client",  # anonymized
        "age_client",
        "date_of_birth",  # anonymized
        "email_client",  # anonymized
        "formations_certifications_client",
        "closedate",
        "createdate",
        "nom_transaction",
        "dealstage",
        "hs_lastmodifieddate",
        "hs_object_id",
        "pipeline",
        "dt_creation_transaction",
        "dt_modification_transaction",
    ]
    
    # Table: vw_dim_leads
    LEADS_COLUMNS = [
        "id_lead",
        "libelle_lead",
        "dt_dernier_echange",
        "id_consultant",
        "source_lead",
        "dt_creation_lead",
        "dt_modification_lead",
    ]
    
    # Table: vw_dim_entreprise
    ENTREPRISE_COLUMNS = [
        "id_entreprise",
        "nom_entreprise",
        "num_tel_entreprise",
        "site_web",
        "nom_pays",
        "cd_pays",
        "lb_region",
        "cd_region",
        "dt_creation_entreprise",
        "dt_modification_entreprise",
    ]
    
    # Table: vw_dim_email
    EMAIL_COLUMNS = [
        "id_email",
        "id_contact_emetteur",
        "email_emetteur",  # anonymized
        "id_contact_destinataire",
        "email_destinataire",  # anonymized
        "texte_email",  # anonymized
        "hs_email_headers",
        "hs_createdate",
        "dt_creation_email",
        "dt_modification_email",
    ]
    
    # Table: vw_dim_reunion
    REUNION_COLUMNS = [
        "id_reunion",
        "hs_attendee_owner_ids",
        "hs_body_preview",
        "hs_created_by",
        "hs_guest_emails",
        "hs_engagement_source_id",
        "dt_creation_reunion",
        "dt_modification_reunion",
    ]
    
    # Table: vw_dim_appel
    APPEL_COLUMNS = [
        "id_appel",
        "hs_call_direction",
        "hs_call_title",
        "hs_call_duration",
        "hs_call_callee_object_type",
        "hs_call_status",
        "hs_body_preview",
        "dt_creation_appel",
        "dt_modification_appel",
    ]
    
    # Table: vw_dim_ticket
    TICKET_COLUMNS = [
        "id_ticket",
        "objet_ticket",
        "etape_dans_la_chaine",
        "id_consultant",
        "priorite",
        "source_ticket",
        "dt_creation_ticket",
        "dt_modification_ticket",
    ]
    
    # Table: vw_dim_ligne_produit
    LIGNE_PRODUIT_COLUMNS = [
        "id_ligne_produit",
        "nom_ligne_produit",
        "description",
        "prix",
        "montant",
        "prix_apres_taxe",
        "cout_production",
        "qte",
        "duree_ideale",
        "recurringbillingfrequency",
        "id_createur_produit",
        "remise",
        "pourcentage_remise",
        "dt_creation_ligne_produit",
        "dt_modification_ligne_produit",
    ]
    
    # Table: vw_dim_abonnement
    ABONNEMENT_COLUMNS = [
        "id_abonnement",
        "nom_abonnement",
        "statut_abonnement",
        "id_contact",
        "email_premier_acheteur",
        "date_debut_facturation",
        "total_montant_recupere",
        "total_montant_restant",
        "nombre_paiements_complets",
        "montant_dernier_paiement",
        "date_dernier_paiement_effectue",
        "statut_dernier_paiement",
        "montant_prochain_paiement",
        "date_prochain_paiement",
        "dt_creation_abonnement",
        "dt_modification_abonnement",
    ]


@dataclass
class ModjoSchema:
    """Schema definitions for Modjo call data."""
    
    # Table: vw_modjo_calls_exports (full call exports)
    CALLS_EXPORTS_COLUMNS = [
        "relations",  # structured interaction details
        "provider",
        "duration",
        "title",
        "startDate",
        "callCrmId",
        "language",
        "providerCallId",
        "callId",
    ]
    
    # Table: vw_dim_modjo_call_summary
    CALL_SUMMARY_COLUMNS = [
        "callId",
        "providerCallId",
        "startDate",
        "title",
        "duration",
        "provider",
        "summary_content",
        "aiSummary_content",
        "score_appel_prequai",
        "score_ice_breaker",
        "score_phase_decouverte",
        "score_consolider_vente",
        "score_visio_vente",
        "dealId",
        "dealCrmId",
        "deal_name",
        "accountId",
        "account_name",
    ]
    
    # Table: vw_dim_modjo_contact
    MODJO_CONTACT_COLUMNS = [
        "contactCrmId",
        "contactId",
        "name",  # anonymized
        "phoneNumber",  # anonymized
        "email",  # anonymized
    ]
    
    # Table: vw_dim_modjo_speaker
    SPEAKER_COLUMNS = [
        "speakerId",
        "type",
        "name",  # anonymized
        "phoneNumber",  # anonymized
        "email",  # anonymized
        "contactId",
        "contactCrmId",
        "userId",
        "userCrmId",
    ]
    
    # Table: vw_fact_modjo_call
    MODJO_CALL_COLUMNS = [
        "callId",
        "contactCrmId",
        "contactId",
        "userCrmId",
        "userId",
        "dealId",
        "dealCrmId",
        "accountId",
        "startDate",
        "title",
        "duration",
    ]
    
    # Table: vw_dim_modjo_active_user
    ACTIVE_USER_COLUMNS = [
        "userCrmId",
        "userId",
        "name",  # anonymized
        "email",  # anonymized
        "isOwner",
    ]
    
    # Table: vw_dim_modjo_transcript
    TRANSCRIPT_COLUMNS = [
        "callId",
        "speakerId",
        "content",
        "startTime",
        "endTime",
    ]


def validate_data_schema(
    df: pd.DataFrame,
    expected_columns: List[str],
    table_name: str,
    allow_missing: bool = False,
) -> bool:
    """
    Validate DataFrame schema against expected columns.
    
    Args:
        df: DataFrame to validate
        expected_columns: List of expected column names
        table_name: Name of the table for logging
        allow_missing: Whether to allow missing columns
        
    Returns:
        True if schema is valid
        
    Raises:
        ValueError: If schema validation fails
    """
    if df.empty:
        logger.warning(f"Empty DataFrame for table {table_name}")
        return True
    
    actual_columns = set(df.columns)
    expected_columns_set = set(expected_columns)
    
    # Check for missing columns
    missing_columns = expected_columns_set - actual_columns
    if missing_columns and not allow_missing:
        logger.error(
            "Missing columns in DataFrame",
            table_name=table_name,
            missing_columns=list(missing_columns),
        )
        raise ValueError(
            f"Missing columns in {table_name}: {missing_columns}"
        )
    
    # Check for extra columns
    extra_columns = actual_columns - expected_columns_set
    if extra_columns:
        logger.warning(
            "Extra columns in DataFrame",
            table_name=table_name,
            extra_columns=list(extra_columns),
        )
    
    # Log validation results
    logger.info(
        "Schema validation completed",
        table_name=table_name,
        expected_columns=len(expected_columns),
        actual_columns=len(actual_columns),
        missing_columns=len(missing_columns),
        extra_columns=len(extra_columns),
    )
    
    return True


def get_table_schema(table_name: str) -> List[str]:
    """
    Get expected columns for a table.

    Args:
        table_name: Name of the table

    Returns:
        List of expected column names

    Raises:
        ValueError: If table name is not recognized
    """
    schema_mapping = {
        # Typeform tables
        "vw_dim_formulaire": TypeformSchema.FORMULAIRE_COLUMNS,
        "vw_dim_question": TypeformSchema.QUESTION_COLUMNS,
        "vw_dim_choix": TypeformSchema.CHOIX_COLUMNS,
        "vw_dim_option": TypeformSchema.OPTION_COLUMNS,
        "vw_fact_soumission_formulaire": TypeformSchema.SOUMISSION_COLUMNS,
        "vw_dim_reponse": TypeformSchema.REPONSE_COLUMNS,
        "vw_reponses_typeform_deno": TypeformSchema.REPONSES_DENO_COLUMNS,

        # HubSpot tables
        "vw_dim_contact": HubspotSchema.CONTACT_COLUMNS,
        "vw_dim_consultant": HubspotSchema.CONSULTANT_COLUMNS,
        "vw_fact_transaction": HubspotSchema.TRANSACTION_COLUMNS,
        "vw_dim_leads": HubspotSchema.LEADS_COLUMNS,
        "vw_dim_entreprise": HubspotSchema.ENTREPRISE_COLUMNS,
        "vw_dim_email": HubspotSchema.EMAIL_COLUMNS,
        "vw_dim_reunion": HubspotSchema.REUNION_COLUMNS,
        "vw_dim_appel": HubspotSchema.APPEL_COLUMNS,
        "vw_dim_ticket": HubspotSchema.TICKET_COLUMNS,
        "vw_dim_ligne_produit": HubspotSchema.LIGNE_PRODUIT_COLUMNS,
        "vw_dim_abonnement": HubspotSchema.ABONNEMENT_COLUMNS,

        # Modjo tables
        "vw_modjo_calls_exports": ModjoSchema.CALLS_EXPORTS_COLUMNS,
        "vw_dim_modjo_call_summary": ModjoSchema.CALL_SUMMARY_COLUMNS,
        "vw_dim_modjo_contact": ModjoSchema.MODJO_CONTACT_COLUMNS,
        "vw_dim_modjo_speaker": ModjoSchema.SPEAKER_COLUMNS,
        "vw_fact_modjo_call": ModjoSchema.MODJO_CALL_COLUMNS,
        "vw_dim_modjo_active_user": ModjoSchema.ACTIVE_USER_COLUMNS,
        "vw_dim_modjo_transcript": ModjoSchema.TRANSCRIPT_COLUMNS,
    }

    if table_name not in schema_mapping:
        raise ValueError(f"Unknown table name: {table_name}")

    return schema_mapping[table_name]
