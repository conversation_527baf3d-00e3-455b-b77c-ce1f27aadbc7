"""
Configuration management for Sensei AI.

Provides environment-based configuration with validation and type safety.
"""

import os
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path
from typing import Dict, Any, Optional, List
import json

from .logging import get_logger

logger = get_logger(__name__)


class Environment(str, Enum):
    """Environment types."""
    DEVELOPMENT = "development"
    STAGING = "staging"
    PRODUCTION = "production"


class ServerCapacity(str, Enum):
    """Server capacity levels for resource allocation."""
    SMALL = "small"      # Development/testing: 1K samples, 10 features
    MEDIUM = "medium"    # Small production: 10K samples, 50 features  
    LARGE = "large"      # Standard production: 100K samples, 100 features
    XLARGE = "xlarge"    # Enterprise: 1M+ samples, 200+ features


@dataclass
class BigQueryConfig:
    """BigQuery configuration."""
    project_id: str = "datalake-sensei"
    dataset_id: str = "serving_layer"
    location: str = "europe-west1"
    max_bytes_billed: int = 21474836480  # 20GB
    timeout_seconds: int = 300
    allowed_read_datasets: List[str] = field(default_factory=lambda: ["serving_layer"])
    allowed_write_datasets: List[str] = field(default_factory=lambda: ["serving_layer_ml"])


@dataclass
class APIConfig:
    """API server configuration."""
    host: str = "0.0.0.0"
    port: int = 8000
    workers: int = 1
    reload: bool = False
    log_level: str = "info"
    cors_origins: List[str] = field(default_factory=lambda: ["*"])
    request_timeout: int = 30
    max_request_size: int = 16 * 1024 * 1024  # 16MB


@dataclass
class ModelConfig:
    """Model configuration."""
    cache_size_mb: int = 1000
    timeout_seconds: int = 30
    max_versions: int = 3
    auto_cleanup: bool = True
    model_dir: Path = field(default_factory=lambda: Path("models"))
    
    def __post_init__(self) -> None:
        """Ensure model directory exists."""
        self.model_dir.mkdir(parents=True, exist_ok=True)


@dataclass
class TrainingConfig:
    """Training configuration based on server capacity."""
    max_samples: int = 10000
    max_features: int = 50
    hyperopt_trials: int = 50
    timeout_minutes: int = 30
    cross_validation_folds: int = 5
    test_size: float = 0.2
    random_state: int = 42


@dataclass
class SecurityConfig:
    """Security configuration."""
    enable_audit_logging: bool = True
    max_query_complexity: int = 1000
    rate_limit_requests_per_minute: int = 1000
    enable_sql_injection_protection: bool = True
    allowed_file_extensions: List[str] = field(
        default_factory=lambda: [".json", ".yaml", ".yml"]
    )


@dataclass
class MonitoringConfig:
    """Monitoring and observability configuration."""
    enable_metrics: bool = True
    enable_tracing: bool = False
    prometheus_port: int = 9090
    log_level: str = "INFO"
    structured_logging: bool = True
    metrics_retention_days: int = 30


@dataclass
class Config:
    """Main configuration class."""
    
    # Environment
    environment: Environment = Environment.DEVELOPMENT
    debug: bool = False
    
    # Project metadata
    project_name: str = "sensei-ai"
    version: str = "1.0.0"
    
    # Sub-configurations
    bigquery: BigQueryConfig = field(default_factory=BigQueryConfig)
    api: APIConfig = field(default_factory=APIConfig)
    models: ModelConfig = field(default_factory=ModelConfig)
    training: TrainingConfig = field(default_factory=TrainingConfig)
    security: SecurityConfig = field(default_factory=SecurityConfig)
    monitoring: MonitoringConfig = field(default_factory=MonitoringConfig)
    
    def __post_init__(self) -> None:
        """Initialize configuration from environment variables."""
        self._load_from_environment()
        self._adjust_for_environment()
        self._validate_config()
    
    def _load_from_environment(self) -> None:
        """Load configuration from environment variables."""
        # Environment
        env_str = os.getenv("ENVIRONMENT", self.environment.value)
        try:
            self.environment = Environment(env_str)
        except ValueError:
            logger.warning(f"Invalid environment '{env_str}', using development")
            self.environment = Environment.DEVELOPMENT
        
        self.debug = os.getenv("DEBUG", "false").lower() == "true"
        
        # BigQuery
        self.bigquery.project_id = os.getenv("GCP_PROJECT_ID", self.bigquery.project_id)
        self.bigquery.dataset_id = os.getenv("DATASET_ID", self.bigquery.dataset_id)
        self.bigquery.location = os.getenv("BQ_LOCATION", self.bigquery.location)
        
        if max_bytes := os.getenv("BQ_MAX_BYTES_BILLED"):
            self.bigquery.max_bytes_billed = int(max_bytes)
        
        # API
        self.api.host = os.getenv("API_HOST", self.api.host)
        self.api.port = int(os.getenv("API_PORT", str(self.api.port)))
        self.api.workers = int(os.getenv("API_WORKERS", str(self.api.workers)))
        self.api.log_level = os.getenv("LOG_LEVEL", self.api.log_level)
        
        # Models
        if cache_size := os.getenv("MODEL_CACHE_SIZE_MB"):
            self.models.cache_size_mb = int(cache_size)
        
        if timeout := os.getenv("MODEL_TIMEOUT_SECONDS"):
            self.models.timeout_seconds = int(timeout)
        
        if max_versions := os.getenv("MAX_MODEL_VERSIONS"):
            self.models.max_versions = int(max_versions)
        
        # Training (based on server capacity)
        capacity_str = os.getenv("SERVER_CAPACITY", "medium")
        try:
            capacity = ServerCapacity(capacity_str)
            self._set_training_config_for_capacity(capacity)
        except ValueError:
            logger.warning(f"Invalid server capacity '{capacity_str}', using medium")
            self._set_training_config_for_capacity(ServerCapacity.MEDIUM)
        
        # Monitoring
        self.monitoring.log_level = os.getenv("LOG_LEVEL", self.monitoring.log_level)
        self.monitoring.enable_metrics = os.getenv("ENABLE_METRICS", "true").lower() == "true"
        self.monitoring.enable_tracing = os.getenv("ENABLE_TRACING", "false").lower() == "true"
    
    def _set_training_config_for_capacity(self, capacity: ServerCapacity) -> None:
        """Set training configuration based on server capacity."""
        capacity_configs = {
            ServerCapacity.SMALL: {
                "max_samples": 1000,
                "max_features": 10,
                "hyperopt_trials": 10,
                "timeout_minutes": 5,
            },
            ServerCapacity.MEDIUM: {
                "max_samples": 10000,
                "max_features": 50,
                "hyperopt_trials": 50,
                "timeout_minutes": 30,
            },
            ServerCapacity.LARGE: {
                "max_samples": 100000,
                "max_features": 100,
                "hyperopt_trials": 100,
                "timeout_minutes": 120,
            },
            ServerCapacity.XLARGE: {
                "max_samples": 1000000,
                "max_features": 200,
                "hyperopt_trials": 200,
                "timeout_minutes": 480,
            },
        }
        
        config = capacity_configs[capacity]
        self.training.max_samples = config["max_samples"]
        self.training.max_features = config["max_features"]
        self.training.hyperopt_trials = config["hyperopt_trials"]
        self.training.timeout_minutes = config["timeout_minutes"]
    
    def _adjust_for_environment(self) -> None:
        """Adjust configuration based on environment."""
        if self.environment == Environment.PRODUCTION:
            self.debug = False
            self.api.reload = False
            self.api.workers = max(2, self.api.workers)
            self.security.enable_audit_logging = True
            self.monitoring.enable_metrics = True
            
        elif self.environment == Environment.DEVELOPMENT:
            self.debug = True
            self.api.reload = True
            self.api.workers = 1
            self.security.enable_audit_logging = False
            
        elif self.environment == Environment.STAGING:
            self.debug = False
            self.api.reload = False
            self.api.workers = 2
            self.security.enable_audit_logging = True
    
    def _validate_config(self) -> None:
        """Validate configuration values."""
        # Validate BigQuery configuration
        if not self.bigquery.project_id:
            raise ValueError("BigQuery project_id is required")
        
        if self.bigquery.max_bytes_billed <= 0:
            raise ValueError("BigQuery max_bytes_billed must be positive")
        
        # Validate API configuration
        if not (1 <= self.api.port <= 65535):
            raise ValueError("API port must be between 1 and 65535")
        
        if self.api.workers < 1:
            raise ValueError("API workers must be at least 1")
        
        # Validate training configuration
        if self.training.max_samples <= 0:
            raise ValueError("Training max_samples must be positive")
        
        if not (0.0 < self.training.test_size < 1.0):
            raise ValueError("Training test_size must be between 0 and 1")
    
    def get_credentials_path(self) -> Path:
        """Get path to GCP credentials."""
        creds_path = os.getenv("GOOGLE_APPLICATION_CREDENTIALS")
        if creds_path:
            path = Path(creds_path)
            if path.exists():
                return path
        
        # Default paths
        default_paths = [
            Path("credentials/sensei-ai-service-account.json"),
            Path("../credentials/sensei-ai-service-account.json"),
            Path.home() / ".config" / "gcloud" / "application_default_credentials.json"
        ]
        
        for path in default_paths:
            if path.exists():
                return path
        
        raise FileNotFoundError(
            "No GCP credentials found. Set GOOGLE_APPLICATION_CREDENTIALS or "
            "place credentials in credentials/sensei-ai-service-account.json"
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary."""
        return {
            "environment": self.environment.value,
            "debug": self.debug,
            "project_name": self.project_name,
            "version": self.version,
            "bigquery": {
                "project_id": self.bigquery.project_id,
                "dataset_id": self.bigquery.dataset_id,
                "location": self.bigquery.location,
                "max_bytes_billed": self.bigquery.max_bytes_billed,
            },
            "api": {
                "host": self.api.host,
                "port": self.api.port,
                "workers": self.api.workers,
                "reload": self.api.reload,
            },
            "models": {
                "cache_size_mb": self.models.cache_size_mb,
                "timeout_seconds": self.models.timeout_seconds,
                "max_versions": self.models.max_versions,
            },
            "training": {
                "max_samples": self.training.max_samples,
                "max_features": self.training.max_features,
                "hyperopt_trials": self.training.hyperopt_trials,
                "timeout_minutes": self.training.timeout_minutes,
            },
        }


# Global configuration instance
_config: Optional[Config] = None


def get_config() -> Config:
    """Get the global configuration instance."""
    global _config
    if _config is None:
        _config = Config()
        logger.info("Configuration loaded", config=_config.to_dict())
    return _config


def reload_config() -> Config:
    """Reload configuration from environment."""
    global _config
    _config = None
    return get_config()
