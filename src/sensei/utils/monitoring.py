"""
Monitoring and metrics utilities for Sensei AI.

Provides Prometheus metrics, performance tracking, and observability.
"""

import time
import functools
from typing import Dict, Any, Optional, Callable
from contextlib import contextmanager
from prometheus_client import Counter, Histogram, Gauge, CollectorRegistry, generate_latest
import threading

from .logging import get_logger

logger = get_logger(__name__)


class MetricsRegistry:
    """Registry for application metrics."""
    
    def __init__(self) -> None:
        """Initialize metrics registry."""
        self.registry = CollectorRegistry()
        self._setup_metrics()
    
    def _setup_metrics(self) -> None:
        """Setup application metrics."""
        # API metrics
        self.api_requests_total = Counter(
            'sensei_api_requests_total',
            'Total number of API requests',
            ['method', 'endpoint', 'status'],
            registry=self.registry
        )
        
        self.api_request_duration = Histogram(
            'sensei_api_request_duration_seconds',
            'API request duration in seconds',
            ['method', 'endpoint'],
            registry=self.registry
        )
        
        # Model metrics
        self.model_predictions_total = Counter(
            'sensei_model_predictions_total',
            'Total number of model predictions',
            ['model_name', 'model_version'],
            registry=self.registry
        )
        
        self.model_prediction_duration = Histogram(
            'sensei_model_prediction_duration_seconds',
            'Model prediction duration in seconds',
            ['model_name'],
            registry=self.registry
        )
        
        self.model_accuracy = Gauge(
            'sensei_model_accuracy',
            'Model accuracy score',
            ['model_name', 'model_version'],
            registry=self.registry
        )
        
        # Data metrics
        self.bigquery_queries_total = Counter(
            'sensei_bigquery_queries_total',
            'Total number of BigQuery queries',
            ['dataset', 'status'],
            registry=self.registry
        )
        
        self.bigquery_query_duration = Histogram(
            'sensei_bigquery_query_duration_seconds',
            'BigQuery query duration in seconds',
            ['dataset'],
            registry=self.registry
        )
        
        self.bigquery_bytes_processed = Histogram(
            'sensei_bigquery_bytes_processed',
            'BigQuery bytes processed',
            ['dataset'],
            registry=self.registry
        )
        
        # Training metrics
        self.model_training_duration = Histogram(
            'sensei_model_training_duration_seconds',
            'Model training duration in seconds',
            ['model_name'],
            registry=self.registry
        )
        
        self.hyperparameter_optimization_trials = Counter(
            'sensei_hyperopt_trials_total',
            'Total number of hyperparameter optimization trials',
            ['model_name', 'status'],
            registry=self.registry
        )
        
        # System metrics
        self.active_models = Gauge(
            'sensei_active_models',
            'Number of active models in memory',
            registry=self.registry
        )
        
        self.memory_usage_bytes = Gauge(
            'sensei_memory_usage_bytes',
            'Memory usage in bytes',
            ['component'],
            registry=self.registry
        )
    
    def get_metrics(self) -> str:
        """Get metrics in Prometheus format."""
        return generate_latest(self.registry).decode('utf-8')


# Global metrics registry
_metrics_registry: Optional[MetricsRegistry] = None
_registry_lock = threading.Lock()


def get_metrics_registry() -> MetricsRegistry:
    """Get the global metrics registry."""
    global _metrics_registry
    if _metrics_registry is None:
        with _registry_lock:
            if _metrics_registry is None:
                _metrics_registry = MetricsRegistry()
    return _metrics_registry


@contextmanager
def track_performance(operation: str, labels: Optional[Dict[str, str]] = None):
    """
    Context manager to track operation performance.
    
    Args:
        operation: Operation name
        labels: Additional labels for metrics
    """
    start_time = time.time()
    labels = labels or {}
    
    try:
        yield
        duration = time.time() - start_time
        logger.debug(
            "Operation completed",
            operation=operation,
            duration=duration,
            **labels
        )
        
    except Exception as e:
        duration = time.time() - start_time
        logger.error(
            "Operation failed",
            operation=operation,
            duration=duration,
            error=str(e),
            **labels
        )
        raise
    
    finally:
        duration = time.time() - start_time
        # Record metrics based on operation type
        metrics = get_metrics_registry()
        
        if operation.startswith("api_"):
            method = labels.get("method", "unknown")
            endpoint = labels.get("endpoint", "unknown")
            metrics.api_request_duration.labels(
                method=method,
                endpoint=endpoint
            ).observe(duration)
        
        elif operation.startswith("model_"):
            model_name = labels.get("model_name", "unknown")
            if "prediction" in operation:
                metrics.model_prediction_duration.labels(
                    model_name=model_name
                ).observe(duration)
            elif "training" in operation:
                metrics.model_training_duration.labels(
                    model_name=model_name
                ).observe(duration)
        
        elif operation.startswith("bigquery_"):
            dataset = labels.get("dataset", "unknown")
            metrics.bigquery_query_duration.labels(
                dataset=dataset
            ).observe(duration)


def track_api_request(method: str, endpoint: str, status_code: int):
    """
    Track API request metrics.
    
    Args:
        method: HTTP method
        endpoint: API endpoint
        status_code: HTTP status code
    """
    metrics = get_metrics_registry()
    metrics.api_requests_total.labels(
        method=method,
        endpoint=endpoint,
        status=str(status_code)
    ).inc()


def track_model_prediction(model_name: str, model_version: str):
    """
    Track model prediction metrics.
    
    Args:
        model_name: Name of the model
        model_version: Version of the model
    """
    metrics = get_metrics_registry()
    metrics.model_predictions_total.labels(
        model_name=model_name,
        model_version=model_version
    ).inc()


def track_bigquery_query(
    dataset: str,
    status: str,
    bytes_processed: Optional[int] = None
):
    """
    Track BigQuery query metrics.
    
    Args:
        dataset: Dataset name
        status: Query status (success/error)
        bytes_processed: Number of bytes processed
    """
    metrics = get_metrics_registry()
    metrics.bigquery_queries_total.labels(
        dataset=dataset,
        status=status
    ).inc()
    
    if bytes_processed is not None:
        metrics.bigquery_bytes_processed.labels(
            dataset=dataset
        ).observe(bytes_processed)


def update_model_accuracy(model_name: str, model_version: str, accuracy: float):
    """
    Update model accuracy metric.
    
    Args:
        model_name: Name of the model
        model_version: Version of the model
        accuracy: Accuracy score
    """
    metrics = get_metrics_registry()
    metrics.model_accuracy.labels(
        model_name=model_name,
        model_version=model_version
    ).set(accuracy)


def track_hyperopt_trial(model_name: str, status: str):
    """
    Track hyperparameter optimization trial.
    
    Args:
        model_name: Name of the model
        status: Trial status (success/failed/pruned)
    """
    metrics = get_metrics_registry()
    metrics.hyperparameter_optimization_trials.labels(
        model_name=model_name,
        status=status
    ).inc()


def update_active_models(count: int):
    """
    Update active models count.
    
    Args:
        count: Number of active models
    """
    metrics = get_metrics_registry()
    metrics.active_models.set(count)


def update_memory_usage(component: str, bytes_used: int):
    """
    Update memory usage metric.
    
    Args:
        component: Component name
        bytes_used: Memory usage in bytes
    """
    metrics = get_metrics_registry()
    metrics.memory_usage_bytes.labels(component=component).set(bytes_used)


def performance_monitor(operation: str, labels: Optional[Dict[str, str]] = None):
    """
    Decorator to monitor function performance.
    
    Args:
        operation: Operation name
        labels: Additional labels for metrics
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            with track_performance(operation, labels):
                return func(*args, **kwargs)
        return wrapper
    return decorator
