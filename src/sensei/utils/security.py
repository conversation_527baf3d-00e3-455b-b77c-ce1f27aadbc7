"""
Security utilities for Sensei AI.

Provides SQL injection protection, input validation, and security checks.
"""

import re
from typing import List, Set, Optional
from .logging import get_logger

logger = get_logger(__name__)


class SecurityError(Exception):
    """Raised when a security violation is detected."""
    pass


# SQL keywords that are forbidden in queries
FORBIDDEN_SQL_KEYWORDS = {
    "DELETE", "DROP", "TRUNCATE", "ALTER", "CREATE", "INSERT", "UPDATE",
    "GRANT", "REVOKE", "EXEC", "EXECUTE", "CALL", "MERGE", "REPLACE",
    "LOAD", "EXPORT", "IMPORT", "BA<PERSON>K<PERSON>", "RESTORE"
}

# Allowed SQL keywords for read operations
ALLOWED_SQL_KEYWORDS = {
    "SELECT", "FROM", "WHERE", "JOIN", "INNER", "LEFT", "RIGHT", "OUTER",
    "ON", "AND", "OR", "NOT", "IN", "EXISTS", "BETWEEN", "LIKE", "IS",
    "NULL", "ORDER", "BY", "GROUP", "HAVING", "LIMIT", "OFFSET", "UNION",
    "DISTINCT", "AS", "CASE", "WHEN", "THEN", "ELSE", "END", "COUNT",
    "SUM", "AVG", "MIN", "MAX", "CAST", "CONVERT", "SUBSTRING", "CONCAT",
    "COALESCE", "ISNULL", "NULLIF", "WITH", "CTE", "OVER", "PARTITION",
    "ROW_NUMBER", "RANK", "DENSE_RANK", "LAG", "LEAD", "FIRST_VALUE",
    "LAST_VALUE", "EXTRACT", "DATE", "TIMESTAMP", "DATETIME"
}

# Patterns that indicate potential SQL injection
SQL_INJECTION_PATTERNS = [
    r";\s*(DELETE|DROP|TRUNCATE|ALTER|CREATE|INSERT|UPDATE)",
    r"UNION\s+ALL\s+SELECT",
    r"--\s*$",
    r"/\*.*\*/",
    r"'\s*OR\s+'.*'='",
    r"'\s*AND\s+'.*'='",
    r"1\s*=\s*1",
    r"1\s*OR\s*1",
    r"EXEC\s*\(",
    r"EXECUTE\s*\(",
    r"xp_cmdshell",
    r"sp_executesql",
]


def validate_sql_query(sql: str, allowed_datasets: Optional[List[str]] = None) -> None:
    """
    Validate SQL query for security violations.
    
    Args:
        sql: SQL query to validate
        allowed_datasets: List of allowed dataset names
        
    Raises:
        SecurityError: If security violation is detected
    """
    if not sql or not sql.strip():
        raise SecurityError("Empty SQL query not allowed")
    
    sql_upper = sql.upper().strip()
    
    # Check for forbidden keywords
    for keyword in FORBIDDEN_SQL_KEYWORDS:
        if re.search(rf"\b{keyword}\b", sql_upper):
            logger.warning(
                "Forbidden SQL keyword detected",
                keyword=keyword,
                sql_preview=sql[:100]
            )
            raise SecurityError(f"Forbidden SQL operation detected: {keyword}")
    
    # Check for SQL injection patterns
    for pattern in SQL_INJECTION_PATTERNS:
        if re.search(pattern, sql, re.IGNORECASE | re.MULTILINE):
            logger.warning(
                "Potential SQL injection detected",
                pattern=pattern,
                sql_preview=sql[:100]
            )
            raise SecurityError("Potential SQL injection detected")
    
    # Validate dataset access
    if allowed_datasets:
        _validate_dataset_access(sql, allowed_datasets)
    
    # Check query complexity
    _validate_query_complexity(sql)
    
    logger.debug("SQL query validation passed", sql_preview=sql[:100])


def _validate_dataset_access(sql: str, allowed_datasets: List[str]) -> None:
    """
    Validate that query only accesses allowed datasets.
    
    Args:
        sql: SQL query to validate
        allowed_datasets: List of allowed dataset names
        
    Raises:
        SecurityError: If unauthorized dataset access is detected
    """
    # Extract dataset references from SQL
    dataset_pattern = r"\b(\w+)\.(\w+)"
    matches = re.findall(dataset_pattern, sql, re.IGNORECASE)
    
    for project_or_dataset, table in matches:
        # Check if this looks like a dataset.table reference
        if project_or_dataset not in allowed_datasets:
            # Could be project.dataset.table format, extract dataset
            full_pattern = rf"\b\w+\.{re.escape(project_or_dataset)}\.(\w+)"
            if not re.search(full_pattern, sql, re.IGNORECASE):
                logger.warning(
                    "Unauthorized dataset access attempt",
                    dataset=project_or_dataset,
                    allowed_datasets=allowed_datasets
                )
                raise SecurityError(
                    f"Access to dataset '{project_or_dataset}' not allowed. "
                    f"Allowed datasets: {allowed_datasets}"
                )


def _validate_query_complexity(sql: str, max_complexity: int = 1000) -> None:
    """
    Validate query complexity to prevent resource exhaustion.
    
    Args:
        sql: SQL query to validate
        max_complexity: Maximum allowed complexity score
        
    Raises:
        SecurityError: If query is too complex
    """
    complexity_score = 0
    
    # Count complexity indicators
    complexity_indicators = {
        r"\bJOIN\b": 10,
        r"\bUNION\b": 15,
        r"\bSUBQUERY\b": 20,
        r"\bWITH\b": 25,
        r"\bWINDOW\b": 30,
        r"\bRECURSIVE\b": 50,
    }
    
    for pattern, score in complexity_indicators.items():
        matches = len(re.findall(pattern, sql, re.IGNORECASE))
        complexity_score += matches * score
    
    # Add base complexity for query length
    complexity_score += len(sql) // 100
    
    if complexity_score > max_complexity:
        logger.warning(
            "Query complexity too high",
            complexity_score=complexity_score,
            max_complexity=max_complexity,
            sql_preview=sql[:100]
        )
        raise SecurityError(
            f"Query complexity ({complexity_score}) exceeds maximum ({max_complexity})"
        )


def sanitize_input(value: str, max_length: int = 1000) -> str:
    """
    Sanitize user input to prevent injection attacks.
    
    Args:
        value: Input value to sanitize
        max_length: Maximum allowed length
        
    Returns:
        Sanitized input value
        
    Raises:
        SecurityError: If input is invalid
    """
    if not isinstance(value, str):
        raise SecurityError("Input must be a string")
    
    if len(value) > max_length:
        raise SecurityError(f"Input length ({len(value)}) exceeds maximum ({max_length})")
    
    # Remove potentially dangerous characters
    sanitized = re.sub(r'[<>"\';\\]', '', value)
    
    # Remove control characters
    sanitized = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', sanitized)
    
    return sanitized.strip()


def validate_file_path(file_path: str, allowed_extensions: Optional[List[str]] = None) -> None:
    """
    Validate file path for security.
    
    Args:
        file_path: File path to validate
        allowed_extensions: List of allowed file extensions
        
    Raises:
        SecurityError: If file path is invalid
    """
    if not file_path:
        raise SecurityError("File path cannot be empty")
    
    # Check for path traversal attempts
    if ".." in file_path or file_path.startswith("/"):
        raise SecurityError("Path traversal not allowed")
    
    # Check file extension
    if allowed_extensions:
        file_extension = "." + file_path.split(".")[-1] if "." in file_path else ""
        if file_extension not in allowed_extensions:
            raise SecurityError(
                f"File extension '{file_extension}' not allowed. "
                f"Allowed extensions: {allowed_extensions}"
            )


def generate_audit_log_entry(
    action: str,
    user_id: Optional[str] = None,
    resource: Optional[str] = None,
    details: Optional[dict] = None,
) -> dict:
    """
    Generate audit log entry for security events.
    
    Args:
        action: Action performed
        user_id: User identifier
        resource: Resource accessed
        details: Additional details
        
    Returns:
        Audit log entry
    """
    import time
    import uuid
    
    return {
        "audit_id": str(uuid.uuid4()),
        "timestamp": time.time(),
        "action": action,
        "user_id": user_id,
        "resource": resource,
        "details": details or {},
        "severity": "INFO",
    }
