"""
Structured logging configuration for Sensei AI.

Provides consistent, structured logging across the application with:
- JSON formatting for production
- Human-readable formatting for development
- Correlation IDs for request tracing
- Performance metrics integration
"""

import logging
import logging.config
import sys
from typing import Any, Dict, Optional
import structlog
from pathlib import Path
import os


def setup_logging(
    log_level: str = "INFO",
    environment: str = "development",
    log_file: Optional[Path] = None,
) -> None:
    """
    Setup structured logging configuration.
    
    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR)
        environment: Environment (development, staging, production)
        log_file: Optional log file path
    """
    # Create logs directory if needed
    if log_file:
        log_file.parent.mkdir(parents=True, exist_ok=True)
    
    # Configure structlog
    if environment == "production":
        # JSON logging for production
        structlog.configure(
            processors=[
                structlog.stdlib.filter_by_level,
                structlog.stdlib.add_logger_name,
                structlog.stdlib.add_log_level,
                structlog.stdlib.PositionalArgumentsFormatter(),
                structlog.processors.TimeStamper(fmt="iso"),
                structlog.processors.StackInfoRenderer(),
                structlog.processors.format_exc_info,
                structlog.processors.UnicodeDecoder(),
                structlog.processors.JSONRenderer()
            ],
            context_class=dict,
            logger_factory=structlog.stdlib.LoggerFactory(),
            wrapper_class=structlog.stdlib.BoundLogger,
            cache_logger_on_first_use=True,
        )
    else:
        # Human-readable logging for development
        structlog.configure(
            processors=[
                structlog.stdlib.filter_by_level,
                structlog.stdlib.add_logger_name,
                structlog.stdlib.add_log_level,
                structlog.stdlib.PositionalArgumentsFormatter(),
                structlog.processors.TimeStamper(fmt="%Y-%m-%d %H:%M:%S"),
                structlog.processors.StackInfoRenderer(),
                structlog.processors.format_exc_info,
                structlog.processors.UnicodeDecoder(),
                structlog.dev.ConsoleRenderer(colors=True)
            ],
            context_class=dict,
            logger_factory=structlog.stdlib.LoggerFactory(),
            wrapper_class=structlog.stdlib.BoundLogger,
            cache_logger_on_first_use=True,
        )
    
    # Configure standard library logging
    logging_config = {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {
            "json": {
                "()": structlog.stdlib.ProcessorFormatter,
                "processor": structlog.processors.JSONRenderer(),
            },
            "console": {
                "()": structlog.stdlib.ProcessorFormatter,
                "processor": structlog.dev.ConsoleRenderer(colors=True),
            },
        },
        "handlers": {
            "console": {
                "class": "logging.StreamHandler",
                "stream": sys.stdout,
                "formatter": "console" if environment != "production" else "json",
                "level": log_level,
            },
        },
        "loggers": {
            "": {
                "handlers": ["console"],
                "level": log_level,
                "propagate": True,
            },
            "sensei": {
                "handlers": ["console"],
                "level": log_level,
                "propagate": False,
            },
            # Suppress noisy third-party loggers
            "google.cloud": {
                "level": "WARNING",
            },
            "urllib3": {
                "level": "WARNING",
            },
            "requests": {
                "level": "WARNING",
            },
        },
    }
    
    # Add file handler if specified
    if log_file:
        logging_config["handlers"]["file"] = {
            "class": "logging.handlers.RotatingFileHandler",
            "filename": str(log_file),
            "maxBytes": 10 * 1024 * 1024,  # 10MB
            "backupCount": 5,
            "formatter": "json",
            "level": log_level,
        }
        
        # Add file handler to loggers
        for logger_name in ["", "sensei"]:
            logging_config["loggers"][logger_name]["handlers"].append("file")
    
    logging.config.dictConfig(logging_config)


def get_logger(name: str) -> structlog.stdlib.BoundLogger:
    """
    Get a structured logger instance.
    
    Args:
        name: Logger name (typically __name__)
        
    Returns:
        Configured structlog logger
    """
    return structlog.get_logger(name)


def add_correlation_id(correlation_id: str) -> None:
    """
    Add correlation ID to the logging context.
    
    Args:
        correlation_id: Unique identifier for request tracing
    """
    structlog.contextvars.bind_contextvars(correlation_id=correlation_id)


def clear_correlation_id() -> None:
    """Clear correlation ID from logging context."""
    structlog.contextvars.clear_contextvars()


# Initialize logging on module import
_log_level = os.getenv("LOG_LEVEL", "INFO")
_environment = os.getenv("ENVIRONMENT", "development")
_log_file = None

if _environment == "production":
    _log_file = Path("logs/sensei.log")

setup_logging(
    log_level=_log_level,
    environment=_environment,
    log_file=_log_file,
)
