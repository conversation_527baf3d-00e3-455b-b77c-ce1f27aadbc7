"""
Unified Monitoring System for Sensei AI v1.0

Consolidates all monitoring capabilities including ML metrics, data quality,
performance monitoring, and alerting into a single optimized system.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
import logging
from datetime import datetime, timedelta
import json
from pathlib import Path
from sklearn.metrics import (
    accuracy_score, precision_score, recall_score, f1_score, roc_auc_score,
    confusion_matrix, classification_report
)
import warnings
warnings.filterwarnings("ignore")

logger = logging.getLogger(__name__)


class UnifiedMonitoringSystem:
    """
    Unified monitoring system combining ML metrics, data quality, and performance monitoring.
    
    Features:
    - Comprehensive ML model evaluation
    - Data quality monitoring
    - Performance tracking
    - Automated alerting
    - Report generation
    """
    
    def __init__(self, model_name: str = "sensei_v1", output_dir: str = "monitoring_output"):
        self.model_name = model_name
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # Monitoring history
        self.metrics_history = []
        self.alerts_history = []
        
        # Alert thresholds
        self.thresholds = {
            'auc_min': 0.75,
            'accuracy_min': 0.80,
            'precision_min': 0.70,
            'recall_min': 0.60,
            'f1_min': 0.65,
            'data_completeness_min': 0.80,
            'prediction_time_max': 1.0  # seconds
        }
    
    def evaluate_model_complete(self, model, X_test: pd.DataFrame, y_test: pd.Series,
                               feature_names: List[str] = None) -> Dict[str, Any]:
        """
        Complete model evaluation with all metrics.
        
        Args:
            model: Trained ML model
            X_test: Test features
            y_test: Test targets
            feature_names: List of feature names
            
        Returns:
            Dictionary with comprehensive evaluation metrics
        """
        logger.info("📊 Starting comprehensive model evaluation...")
        
        if feature_names is None:
            feature_names = X_test.columns.tolist() if hasattr(X_test, 'columns') else [f'feature_{i}' for i in range(X_test.shape[1])]
        
        # Predictions
        y_pred = model.predict(X_test)
        y_pred_proba = model.predict_proba(X_test)[:, 1] if hasattr(model, 'predict_proba') else None
        
        # Core metrics
        metrics = {
            'timestamp': datetime.now().isoformat(),
            'model_name': self.model_name,
            'test_size': len(X_test),
            'feature_count': len(feature_names),
            'positive_rate': float(y_test.mean()),
            'accuracy': float(accuracy_score(y_test, y_pred)),
            'precision': float(precision_score(y_test, y_pred, average='weighted', zero_division=0)),
            'recall': float(recall_score(y_test, y_pred, average='weighted', zero_division=0)),
            'f1_score': float(f1_score(y_test, y_pred, average='weighted', zero_division=0))
        }
        
        # AUC if probabilities available
        if y_pred_proba is not None and y_test.nunique() > 1:
            metrics['auc_roc'] = float(roc_auc_score(y_test, y_pred_proba))
        else:
            metrics['auc_roc'] = None
        
        # Confusion matrix
        cm = confusion_matrix(y_test, y_pred)
        if cm.shape == (2, 2):
            metrics['confusion_matrix'] = {
                'tn': int(cm[0, 0]),
                'fp': int(cm[0, 1]),
                'fn': int(cm[1, 0]),
                'tp': int(cm[1, 1])
            }
        else:
            metrics['confusion_matrix'] = cm.tolist()
        
        # Feature importance
        if hasattr(model, 'feature_importances_'):
            importance_dict = dict(zip(feature_names, model.feature_importances_))
            metrics['feature_importance'] = {k: float(v) for k, v in importance_dict.items()}
            
            # Top features
            sorted_features = sorted(importance_dict.items(), key=lambda x: x[1], reverse=True)
            metrics['top_features'] = dict(sorted_features[:10])
        
        # Classification report
        try:
            class_report = classification_report(y_test, y_pred, output_dict=True, zero_division=0)
            metrics['classification_report'] = class_report
        except Exception as e:
            logger.warning(f"⚠️ Classification report failed: {e}")
        
        # Check alerts
        alerts = self._check_model_alerts(metrics)
        if alerts:
            metrics['alerts'] = alerts
            self.alerts_history.extend(alerts)
        
        # Save metrics
        self.metrics_history.append(metrics)
        self._save_metrics(metrics)
        
        logger.info(f"✅ Model evaluation complete - AUC: {metrics.get('auc_roc', 'N/A')}")
        return metrics
    
    def monitor_data_quality(self, df: pd.DataFrame, 
                           required_columns: List[str] = None) -> Dict[str, Any]:
        """
        Monitor data quality metrics.
        
        Args:
            df: DataFrame to analyze
            required_columns: List of required columns
            
        Returns:
            Data quality metrics
        """
        logger.info("🔍 Monitoring data quality...")
        
        quality_metrics = {
            'timestamp': datetime.now().isoformat(),
            'total_rows': len(df),
            'total_columns': len(df.columns),
            'memory_usage_mb': float(df.memory_usage(deep=True).sum() / 1024 / 1024)
        }
        
        # Missing values analysis
        missing_analysis = {}
        for col in df.columns:
            missing_count = df[col].isnull().sum()
            missing_pct = (missing_count / len(df)) * 100
            missing_analysis[col] = {
                'missing_count': int(missing_count),
                'missing_percentage': float(missing_pct)
            }
        
        quality_metrics['missing_values'] = missing_analysis
        
        # Data completeness
        overall_completeness = (1 - df.isnull().sum().sum() / (len(df) * len(df.columns))) * 100
        quality_metrics['overall_completeness'] = float(overall_completeness)
        
        # Data types
        dtype_counts = df.dtypes.value_counts().to_dict()
        quality_metrics['data_types'] = {str(k): int(v) for k, v in dtype_counts.items()}
        
        # Duplicate rows
        duplicate_count = df.duplicated().sum()
        quality_metrics['duplicate_rows'] = int(duplicate_count)
        quality_metrics['duplicate_percentage'] = float((duplicate_count / len(df)) * 100)
        
        # Required columns check
        if required_columns:
            missing_required = [col for col in required_columns if col not in df.columns]
            quality_metrics['missing_required_columns'] = missing_required
            quality_metrics['required_columns_complete'] = len(missing_required) == 0
        
        # Numeric columns statistics
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        if len(numeric_cols) > 0:
            numeric_stats = {}
            for col in numeric_cols:
                numeric_stats[col] = {
                    'mean': float(df[col].mean()) if not df[col].isnull().all() else None,
                    'std': float(df[col].std()) if not df[col].isnull().all() else None,
                    'min': float(df[col].min()) if not df[col].isnull().all() else None,
                    'max': float(df[col].max()) if not df[col].isnull().all() else None,
                    'zeros_count': int((df[col] == 0).sum()),
                    'outliers_count': int(self._count_outliers(df[col]))
                }
            quality_metrics['numeric_statistics'] = numeric_stats
        
        # Check data quality alerts
        data_alerts = self._check_data_quality_alerts(quality_metrics)
        if data_alerts:
            quality_metrics['alerts'] = data_alerts
            self.alerts_history.extend(data_alerts)
        
        logger.info(f"✅ Data quality analysis complete - Completeness: {overall_completeness:.1f}%")
        return quality_metrics
    
    def monitor_prediction_performance(self, prediction_times: List[float],
                                     prediction_counts: List[int]) -> Dict[str, Any]:
        """
        Monitor prediction performance metrics.
        
        Args:
            prediction_times: List of prediction times in seconds
            prediction_counts: List of prediction batch sizes
            
        Returns:
            Performance metrics
        """
        logger.info("⚡ Monitoring prediction performance...")
        
        if not prediction_times:
            return {'error': 'No prediction times provided'}
        
        performance_metrics = {
            'timestamp': datetime.now().isoformat(),
            'total_predictions': sum(prediction_counts) if prediction_counts else len(prediction_times),
            'avg_prediction_time': float(np.mean(prediction_times)),
            'max_prediction_time': float(np.max(prediction_times)),
            'min_prediction_time': float(np.min(prediction_times)),
            'std_prediction_time': float(np.std(prediction_times)),
            'p95_prediction_time': float(np.percentile(prediction_times, 95)),
            'p99_prediction_time': float(np.percentile(prediction_times, 99))
        }
        
        # Throughput calculation
        if prediction_counts:
            total_predictions = sum(prediction_counts)
            total_time = sum(prediction_times)
            performance_metrics['throughput_per_second'] = float(total_predictions / total_time) if total_time > 0 else 0
        
        # Performance alerts
        perf_alerts = self._check_performance_alerts(performance_metrics)
        if perf_alerts:
            performance_metrics['alerts'] = perf_alerts
            self.alerts_history.extend(perf_alerts)
        
        logger.info(f"✅ Performance monitoring complete - Avg time: {performance_metrics['avg_prediction_time']:.3f}s")
        return performance_metrics
    
    def generate_comprehensive_report(self, model_metrics: Dict = None,
                                    data_quality: Dict = None,
                                    performance: Dict = None) -> Dict[str, Any]:
        """
        Generate comprehensive monitoring report.
        
        Args:
            model_metrics: Model evaluation metrics
            data_quality: Data quality metrics
            performance: Performance metrics
            
        Returns:
            Comprehensive report
        """
        logger.info("📋 Generating comprehensive monitoring report...")
        
        report = {
            'metadata': {
                'report_timestamp': datetime.now().isoformat(),
                'model_name': self.model_name,
                'report_version': '1.0'
            }
        }
        
        # Include provided metrics
        if model_metrics:
            report['model_evaluation'] = model_metrics
        
        if data_quality:
            report['data_quality'] = data_quality
        
        if performance:
            report['performance'] = performance
        
        # Overall health score
        health_score = self._calculate_health_score(model_metrics, data_quality, performance)
        report['overall_health_score'] = health_score
        
        # Recommendations
        recommendations = self._generate_recommendations(model_metrics, data_quality, performance)
        report['recommendations'] = recommendations
        
        # All alerts
        if self.alerts_history:
            report['recent_alerts'] = self.alerts_history[-10:]  # Last 10 alerts
        
        # Save report
        report_file = self.output_dir / f"comprehensive_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        logger.info(f"✅ Report saved: {report_file}")
        return report
    
    def _count_outliers(self, series: pd.Series) -> int:
        """Count outliers using IQR method."""
        if series.isnull().all():
            return 0
        
        Q1 = series.quantile(0.25)
        Q3 = series.quantile(0.75)
        IQR = Q3 - Q1
        lower_bound = Q1 - 1.5 * IQR
        upper_bound = Q3 + 1.5 * IQR
        
        return ((series < lower_bound) | (series > upper_bound)).sum()
    
    def _check_model_alerts(self, metrics: Dict) -> List[str]:
        """Check for model performance alerts."""
        alerts = []
        
        if metrics.get('auc_roc') and metrics['auc_roc'] < self.thresholds['auc_min']:
            alerts.append(f"Low AUC: {metrics['auc_roc']:.3f} < {self.thresholds['auc_min']}")
        
        if metrics.get('accuracy', 0) < self.thresholds['accuracy_min']:
            alerts.append(f"Low accuracy: {metrics['accuracy']:.3f} < {self.thresholds['accuracy_min']}")
        
        if metrics.get('f1_score', 0) < self.thresholds['f1_min']:
            alerts.append(f"Low F1 score: {metrics['f1_score']:.3f} < {self.thresholds['f1_min']}")
        
        return alerts
    
    def _check_data_quality_alerts(self, quality_metrics: Dict) -> List[str]:
        """Check for data quality alerts."""
        alerts = []
        
        completeness = quality_metrics.get('overall_completeness', 100)
        if completeness < self.thresholds['data_completeness_min'] * 100:
            alerts.append(f"Low data completeness: {completeness:.1f}% < {self.thresholds['data_completeness_min']*100}%")
        
        duplicate_pct = quality_metrics.get('duplicate_percentage', 0)
        if duplicate_pct > 10:
            alerts.append(f"High duplicate rate: {duplicate_pct:.1f}%")
        
        return alerts
    
    def _check_performance_alerts(self, performance_metrics: Dict) -> List[str]:
        """Check for performance alerts."""
        alerts = []
        
        avg_time = performance_metrics.get('avg_prediction_time', 0)
        if avg_time > self.thresholds['prediction_time_max']:
            alerts.append(f"Slow predictions: {avg_time:.3f}s > {self.thresholds['prediction_time_max']}s")
        
        return alerts
    
    def _calculate_health_score(self, model_metrics: Dict = None,
                               data_quality: Dict = None,
                               performance: Dict = None) -> float:
        """Calculate overall system health score (0-1)."""
        scores = []
        
        if model_metrics:
            model_score = 0
            if model_metrics.get('auc_roc'):
                model_score += model_metrics['auc_roc'] * 0.4
            if model_metrics.get('f1_score'):
                model_score += model_metrics['f1_score'] * 0.3
            if model_metrics.get('accuracy'):
                model_score += model_metrics['accuracy'] * 0.3
            scores.append(model_score)
        
        if data_quality:
            data_score = data_quality.get('overall_completeness', 0) / 100
            scores.append(data_score)
        
        if performance:
            # Performance score based on prediction time (inverted)
            avg_time = performance.get('avg_prediction_time', 1.0)
            perf_score = max(0, 1 - (avg_time / self.thresholds['prediction_time_max']))
            scores.append(perf_score)
        
        return float(np.mean(scores)) if scores else 0.0
    
    def _generate_recommendations(self, model_metrics: Dict = None,
                                 data_quality: Dict = None,
                                 performance: Dict = None) -> List[str]:
        """Generate actionable recommendations."""
        recommendations = []
        
        if model_metrics:
            if model_metrics.get('auc_roc', 1) < 0.8:
                recommendations.append("Consider feature engineering or hyperparameter tuning to improve AUC")
            
            if model_metrics.get('precision', 1) < 0.7:
                recommendations.append("Improve precision by adjusting classification threshold or balancing classes")
        
        if data_quality:
            completeness = data_quality.get('overall_completeness', 100)
            if completeness < 90:
                recommendations.append("Improve data collection to reduce missing values")
            
            if data_quality.get('duplicate_percentage', 0) > 5:
                recommendations.append("Implement data deduplication process")
        
        if performance:
            if performance.get('avg_prediction_time', 0) > 0.5:
                recommendations.append("Optimize model or infrastructure for faster predictions")
        
        if not recommendations:
            recommendations.append("System performing well - continue monitoring")
        
        return recommendations
    
    def _save_metrics(self, metrics: Dict):
        """Save metrics to file."""
        metrics_file = self.output_dir / "metrics_history.json"
        
        if metrics_file.exists():
            with open(metrics_file, 'r') as f:
                history = json.load(f)
        else:
            history = []
        
        history.append(metrics)
        
        with open(metrics_file, 'w') as f:
            json.dump(history, f, indent=2, default=str)
    
    def get_dashboard_data(self) -> Dict[str, Any]:
        """Get data for monitoring dashboard."""
        return {
            'recent_metrics': self.metrics_history[-5:] if self.metrics_history else [],
            'recent_alerts': self.alerts_history[-10:] if self.alerts_history else [],
            'health_score': self._calculate_health_score(),
            'thresholds': self.thresholds,
            'system_status': 'healthy' if not self.alerts_history else 'warning'
        }
