"""
Sensei AI - Production ML Platform for B2B Sales Optimization.

This package provides advanced machine learning algorithms for:
- Conversion scoring and prospect prioritization
- Channel and timing optimization for sales outreach
- NLP analysis of sales conversation transcripts

The platform is designed for enterprise use with:
- Secure BigQuery integration (read-only access)
- Production-ready API with FastAPI
- Comprehensive monitoring and observability
- GDPR-compliant data handling
"""

__version__ = "1.0.0"
__author__ = "Sensei AI Team"
__email__ = "<EMAIL>"

# Core exports
from .api import create_app
from .data import get_bigquery_client
from .models import ModelRegistry
from .utils import get_logger, get_config

__all__ = [
    "__version__",
    "__author__", 
    "__email__",
    "create_app",
    "get_bigquery_client",
    "ModelRegistry",
    "get_logger",
    "get_config",
]
