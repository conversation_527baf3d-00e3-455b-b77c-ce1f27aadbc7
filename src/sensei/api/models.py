"""
Pydantic models for Sensei AI API.

Defines request/response schemas for all API endpoints with comprehensive
validation, documentation, and type safety.
"""

from datetime import datetime
from typing import Dict, List, Optional, Any, Union
from pydantic import BaseModel, Field, validator
from enum import Enum


class APIResponse(BaseModel):
    """Base response model with common fields."""
    success: bool = True
    timestamp: datetime = Field(default_factory=datetime.now)
    correlation_id: Optional[str] = None


class ErrorResponse(BaseModel):
    """Error response model."""
    error: str
    message: str
    correlation_id: Optional[str] = None
    details: Optional[Dict[str, Any]] = None


# Conversion Scoring Models
class ConversionRequest(BaseModel):
    """Request model for conversion scoring."""
    
    # Prospect identification
    prospect_id: str = Field(..., description="Unique prospect identifier")
    
    # HubSpot features
    hubspot_score: Optional[float] = Field(None, ge=0, le=100, description="HubSpot lead score")
    num_unique_conversion_events: Optional[int] = Field(None, ge=0, description="Number of unique conversion events")
    num_conversion_events: Optional[int] = Field(None, ge=0, description="Total conversion events")
    num_associated_deals: Optional[int] = Field(None, ge=0, description="Number of associated deals")
    page_views: Optional[int] = Field(None, ge=0, description="Number of page views")
    lead_status: Optional[str] = Field(None, description="Current lead status")
    ip_country: Optional[str] = Field(None, description="Country from IP address")
    
    # Typeform features
    typeform_submissions: Optional[int] = Field(None, ge=0, description="Number of Typeform submissions")
    avg_response_time_minutes: Optional[float] = Field(None, ge=0, description="Average response time in minutes")
    unique_forms_count: Optional[int] = Field(None, ge=0, description="Number of unique forms completed")
    response_completion_rate: Optional[float] = Field(None, ge=0, le=1, description="Response completion rate")
    
    # Engagement features
    email_count: Optional[int] = Field(None, ge=0, description="Number of emails exchanged")
    call_count: Optional[int] = Field(None, ge=0, description="Number of calls made")
    meeting_count: Optional[int] = Field(None, ge=0, description="Number of meetings scheduled")
    days_since_last_engagement: Optional[int] = Field(None, ge=0, description="Days since last engagement")
    
    # Modjo features
    modjo_call_count: Optional[int] = Field(None, ge=0, description="Number of Modjo calls")
    avg_call_score: Optional[float] = Field(None, ge=0, le=100, description="Average call quality score")
    
    @validator('prospect_id')
    def validate_prospect_id(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError('Prospect ID cannot be empty')
        return v.strip()


class ConversionResponse(APIResponse):
    """Response model for conversion scoring."""
    
    prospect_id: str
    conversion_probability: float = Field(..., ge=0, le=1, description="Probability of conversion (0-1)")
    confidence_score: float = Field(..., ge=0, le=1, description="Model confidence (0-1)")
    risk_category: str = Field(..., description="Risk category: high, medium, low")
    
    # Detailed insights
    key_factors: List[Dict[str, Any]] = Field(default_factory=list, description="Key factors influencing the score")
    recommendations: List[str] = Field(default_factory=list, description="Actionable recommendations")
    
    # Model metadata
    model_version: str
    prediction_date: datetime = Field(default_factory=datetime.now)


# Channel & Timing Optimization Models
class ChannelTimingRequest(BaseModel):
    """Request model for channel and timing optimization."""
    
    # Prospect identification
    prospect_id: str = Field(..., description="Unique prospect identifier")
    
    # Historical interaction data
    email_interactions: Optional[int] = Field(None, ge=0, description="Number of email interactions")
    call_interactions: Optional[int] = Field(None, ge=0, description="Number of call interactions")
    meeting_interactions: Optional[int] = Field(None, ge=0, description="Number of meeting interactions")
    
    # Success rates by channel
    email_success_rate: Optional[float] = Field(None, ge=0, le=1, description="Email success rate")
    call_success_rate: Optional[float] = Field(None, ge=0, le=1, description="Call success rate")
    meeting_success_rate: Optional[float] = Field(None, ge=0, le=1, description="Meeting success rate")
    
    # Timing preferences
    preferred_contact_hours: Optional[List[int]] = Field(None, description="Preferred contact hours (0-23)")
    timezone: Optional[str] = Field(None, description="Prospect timezone")
    
    # Deal characteristics
    deal_value: Optional[float] = Field(None, ge=0, description="Potential deal value")
    deal_stage: Optional[str] = Field(None, description="Current deal stage")
    
    @validator('preferred_contact_hours')
    def validate_contact_hours(cls, v):
        if v is not None:
            for hour in v:
                if not 0 <= hour <= 23:
                    raise ValueError('Contact hours must be between 0 and 23')
        return v


class ChannelRecommendation(BaseModel):
    """Channel recommendation model."""
    channel: str = Field(..., description="Recommended channel")
    probability: float = Field(..., ge=0, le=1, description="Success probability")
    confidence: str = Field(..., description="Confidence level: high, medium, low")
    optimal_timing: Optional[str] = Field(None, description="Optimal timing for this channel")


class ChannelTimingResponse(APIResponse):
    """Response model for channel and timing optimization."""
    
    prospect_id: str
    primary_recommendation: ChannelRecommendation
    alternative_recommendations: List[ChannelRecommendation] = Field(default_factory=list)
    
    # Timing insights
    optimal_contact_times: List[str] = Field(default_factory=list, description="Best times to contact")
    avoid_times: List[str] = Field(default_factory=list, description="Times to avoid")
    
    # Model metadata
    model_version: str
    prediction_date: datetime = Field(default_factory=datetime.now)


# NLP Analysis Models
class NLPAnalysisRequest(BaseModel):
    """Request model for NLP conversation analysis."""
    
    # Conversation identification
    conversation_id: str = Field(..., description="Unique conversation identifier")
    
    # Conversation content
    transcript: str = Field(..., min_length=10, description="Conversation transcript")
    summary: Optional[str] = Field(None, description="Conversation summary")
    
    # Conversation metadata
    duration_minutes: Optional[float] = Field(None, ge=0, description="Conversation duration in minutes")
    participant_count: Optional[int] = Field(None, ge=1, description="Number of participants")
    conversation_type: Optional[str] = Field(None, description="Type of conversation")
    
    # Existing scores (if available)
    existing_scores: Optional[Dict[str, float]] = Field(None, description="Existing conversation scores")
    
    @validator('transcript')
    def validate_transcript(cls, v):
        if not v or len(v.strip()) < 10:
            raise ValueError('Transcript must be at least 10 characters long')
        return v.strip()


class ConversationInsight(BaseModel):
    """Conversation insight model."""
    category: str = Field(..., description="Insight category")
    score: float = Field(..., ge=0, le=100, description="Score for this category")
    description: str = Field(..., description="Human-readable description")
    recommendations: List[str] = Field(default_factory=list, description="Specific recommendations")


class NLPAnalysisResponse(APIResponse):
    """Response model for NLP conversation analysis."""
    
    conversation_id: str
    
    # Overall analysis
    overall_quality_score: float = Field(..., ge=0, le=100, description="Overall conversation quality")
    sentiment_score: float = Field(..., ge=-1, le=1, description="Sentiment score (-1 to 1)")
    engagement_level: str = Field(..., description="Engagement level: high, medium, low")
    
    # Detailed insights
    insights: List[ConversationInsight] = Field(default_factory=list)
    
    # Key topics and themes
    main_topics: List[str] = Field(default_factory=list, description="Main conversation topics")
    keywords: List[str] = Field(default_factory=list, description="Important keywords")
    
    # Speaking patterns
    speaking_time_distribution: Dict[str, float] = Field(default_factory=dict, description="Speaking time by participant")
    
    # Model metadata
    model_version: str
    analysis_date: datetime = Field(default_factory=datetime.now)


# Health & Monitoring Models
class HealthStatus(str, Enum):
    """Health status enumeration."""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"


class ComponentHealth(BaseModel):
    """Health status for individual components."""
    name: str
    status: HealthStatus
    message: Optional[str] = None
    response_time_ms: Optional[float] = None
    last_check: datetime = Field(default_factory=datetime.now)


class HealthResponse(BaseModel):
    """Health check response model."""
    status: HealthStatus
    timestamp: datetime = Field(default_factory=datetime.now)
    version: str = "1.0.0"
    uptime_seconds: float
    
    # Component health
    components: List[ComponentHealth] = Field(default_factory=list)
    
    # System metrics
    memory_usage_mb: Optional[float] = None
    cpu_usage_percent: Optional[float] = None
    active_models: Optional[int] = None


# Model Management Models
class ModelInfo(BaseModel):
    """Model information model."""
    name: str
    type: str
    version: str
    status: str = Field(..., description="Model status: active, inactive, training")
    
    # Performance metrics
    accuracy: Optional[float] = Field(None, ge=0, le=1)
    precision: Optional[float] = Field(None, ge=0, le=1)
    recall: Optional[float] = Field(None, ge=0, le=1)
    f1_score: Optional[float] = Field(None, ge=0, le=1)
    
    # Metadata
    created_at: datetime
    last_trained: Optional[datetime] = None
    training_samples: Optional[int] = None
    model_size_mb: Optional[float] = None


class ModelInfoResponse(APIResponse):
    """Model information response."""
    models: List[ModelInfo] = Field(default_factory=list)
    total_models: int = 0
    active_models: int = 0


# Batch Processing Models
class BatchConversionRequest(BaseModel):
    """Batch conversion scoring request."""
    prospects: List[ConversionRequest] = Field(..., max_items=100, description="List of prospects to score")
    
    @validator('prospects')
    def validate_prospects(cls, v):
        if len(v) == 0:
            raise ValueError('At least one prospect is required')
        return v


class BatchConversionResponse(APIResponse):
    """Batch conversion scoring response."""
    results: List[ConversionResponse] = Field(default_factory=list)
    processed_count: int = 0
    failed_count: int = 0
    errors: List[Dict[str, str]] = Field(default_factory=list, description="Processing errors")


# Pagination Models
class PaginationParams(BaseModel):
    """Pagination parameters."""
    page: int = Field(1, ge=1, description="Page number (1-based)")
    page_size: int = Field(20, ge=1, le=100, description="Items per page")


class PaginatedResponse(APIResponse):
    """Paginated response base model."""
    page: int
    page_size: int
    total_items: int
    total_pages: int
    has_next: bool
    has_previous: bool
