"""
Middleware for Sensei AI API.

Provides request/response processing, logging, monitoring, and security features.
"""

import time
import uuid
from typing import Callable
from fastapi import Fast<PERSON><PERSON>, Request, Response
from fastapi.middleware.base import BaseHTTPMiddleware
from starlette.middleware.base import RequestResponseEndpoint

from ..utils import get_logger, get_config
from ..utils.logging import add_correlation_id, clear_correlation_id
from ..utils.monitoring import track_api_request, track_performance

logger = get_logger(__name__)


class CorrelationIDMiddleware(BaseHTTPMiddleware):
    """Middleware to add correlation IDs to requests."""
    
    async def dispatch(
        self,
        request: Request,
        call_next: RequestResponseEndpoint,
    ) -> Response:
        """Add correlation ID to request and response."""
        # Generate or extract correlation ID
        correlation_id = request.headers.get("X-Correlation-ID", str(uuid.uuid4()))
        
        # Add to request state
        request.state.correlation_id = correlation_id
        
        # Add to logging context
        add_correlation_id(correlation_id)
        
        try:
            # Process request
            response = await call_next(request)
            
            # Add correlation ID to response headers
            response.headers["X-Correlation-ID"] = correlation_id
            
            return response
        
        finally:
            # Clear logging context
            clear_correlation_id()


class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """Middleware for request/response logging."""
    
    async def dispatch(
        self,
        request: Request,
        call_next: RequestResponseEndpoint,
    ) -> Response:
        """Log request and response details."""
        start_time = time.time()
        
        # Extract request info
        correlation_id = getattr(request.state, 'correlation_id', 'unknown')
        client_ip = request.client.host if request.client else 'unknown'
        user_agent = request.headers.get('user-agent', 'unknown')
        
        # Log request
        logger.info(
            "Request started",
            correlation_id=correlation_id,
            method=request.method,
            path=request.url.path,
            query_params=str(request.query_params),
            client_ip=client_ip,
            user_agent=user_agent,
        )
        
        # Process request
        response = await call_next(request)
        
        # Calculate duration
        duration = time.time() - start_time
        
        # Log response
        logger.info(
            "Request completed",
            correlation_id=correlation_id,
            method=request.method,
            path=request.url.path,
            status_code=response.status_code,
            duration_ms=round(duration * 1000, 2),
        )
        
        # Track metrics
        track_api_request(
            method=request.method,
            endpoint=request.url.path,
            status_code=response.status_code
        )
        
        return response


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """Middleware to add security headers."""
    
    async def dispatch(
        self,
        request: Request,
        call_next: RequestResponseEndpoint,
    ) -> Response:
        """Add security headers to response."""
        response = await call_next(request)
        
        # Add security headers
        response.headers.update({
            "X-Content-Type-Options": "nosniff",
            "X-Frame-Options": "DENY",
            "X-XSS-Protection": "1; mode=block",
            "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
            "Referrer-Policy": "strict-origin-when-cross-origin",
            "Content-Security-Policy": "default-src 'self'",
        })
        
        return response


class RateLimitMiddleware(BaseHTTPMiddleware):
    """Simple rate limiting middleware."""
    
    def __init__(self, app: FastAPI, requests_per_minute: int = 1000):
        """
        Initialize rate limiting middleware.
        
        Args:
            app: FastAPI application
            requests_per_minute: Maximum requests per minute per IP
        """
        super().__init__(app)
        self.requests_per_minute = requests_per_minute
        self.request_counts = {}
        self.window_start = {}
    
    async def dispatch(
        self,
        request: Request,
        call_next: RequestResponseEndpoint,
    ) -> Response:
        """Apply rate limiting."""
        client_ip = request.client.host if request.client else 'unknown'
        current_time = time.time()
        
        # Reset window if needed
        if client_ip not in self.window_start or current_time - self.window_start[client_ip] >= 60:
            self.window_start[client_ip] = current_time
            self.request_counts[client_ip] = 0
        
        # Check rate limit
        self.request_counts[client_ip] += 1
        
        if self.request_counts[client_ip] > self.requests_per_minute:
            logger.warning(
                "Rate limit exceeded",
                client_ip=client_ip,
                requests=self.request_counts[client_ip],
                limit=self.requests_per_minute,
            )
            
            from fastapi import HTTPException
            raise HTTPException(
                status_code=429,
                detail="Rate limit exceeded. Please try again later.",
                headers={"Retry-After": "60"}
            )
        
        return await call_next(request)


class PerformanceMiddleware(BaseHTTPMiddleware):
    """Middleware for performance monitoring."""
    
    async def dispatch(
        self,
        request: Request,
        call_next: RequestResponseEndpoint,
    ) -> Response:
        """Monitor request performance."""
        # Skip monitoring for health checks and metrics
        if request.url.path in ["/health", "/metrics"]:
            return await call_next(request)
        
        with track_performance(
            "api_request",
            labels={
                "method": request.method,
                "endpoint": request.url.path,
            }
        ):
            response = await call_next(request)
            return response


class RequestSizeMiddleware(BaseHTTPMiddleware):
    """Middleware to limit request size."""
    
    def __init__(self, app: FastAPI, max_size_mb: int = 16):
        """
        Initialize request size middleware.
        
        Args:
            app: FastAPI application
            max_size_mb: Maximum request size in MB
        """
        super().__init__(app)
        self.max_size_bytes = max_size_mb * 1024 * 1024
    
    async def dispatch(
        self,
        request: Request,
        call_next: RequestResponseEndpoint,
    ) -> Response:
        """Check request size."""
        content_length = request.headers.get('content-length')
        
        if content_length:
            content_length = int(content_length)
            if content_length > self.max_size_bytes:
                logger.warning(
                    "Request too large",
                    content_length=content_length,
                    max_size=self.max_size_bytes,
                    path=request.url.path,
                )
                
                from fastapi import HTTPException
                raise HTTPException(
                    status_code=413,
                    detail=f"Request too large. Maximum size is {self.max_size_bytes // (1024*1024)}MB."
                )
        
        return await call_next(request)


def setup_middleware(app: FastAPI) -> None:
    """
    Setup all middleware for the FastAPI application.
    
    Args:
        app: FastAPI application instance
    """
    config = get_config()
    
    # Add middleware in reverse order (last added = first executed)
    
    # Security headers (last)
    app.add_middleware(SecurityHeadersMiddleware)
    
    # Performance monitoring
    app.add_middleware(PerformanceMiddleware)
    
    # Request size limiting
    app.add_middleware(
        RequestSizeMiddleware,
        max_size_mb=config.api.max_request_size // (1024 * 1024)
    )
    
    # Rate limiting
    if config.security.rate_limit_requests_per_minute > 0:
        app.add_middleware(
            RateLimitMiddleware,
            requests_per_minute=config.security.rate_limit_requests_per_minute
        )
    
    # Request logging
    app.add_middleware(RequestLoggingMiddleware)
    
    # Correlation ID (first)
    app.add_middleware(CorrelationIDMiddleware)
    
    logger.info(
        "Middleware setup completed",
        middleware_count=6,
        rate_limit_enabled=config.security.rate_limit_requests_per_minute > 0,
    )
