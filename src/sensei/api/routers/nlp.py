"""
NLP analysis endpoints for Sensei AI API.

Provides advanced NLP analysis of sales conversation transcripts.
"""

import numpy as np
import pandas as pd
from typing import List, Dict, Any
from fastapi import APIRouter, Depends, HTTPException
from datetime import datetime

from ..models import (
    NLPAnalysisRequest,
    NLPAnalysisResponse,
    ConversationInsight,
)
from ..auth import require_read_access, User
from ...utils import get_logger
from ...utils.monitoring import track_model_prediction
from ...models import get_model_registry
from ...features import create_feature_pipeline

logger = get_logger(__name__)
router = APIRouter()


def _extract_basic_features(request: NLPAnalysisRequest) -> Dict[str, Any]:
    """
    Extract basic features from conversation transcript.
    
    Args:
        request: NLP analysis request
        
    Returns:
        Dictionary of basic features
    """
    transcript = request.transcript
    
    # Basic text statistics
    word_count = len(transcript.split())
    sentence_count = len([s for s in transcript.split('.') if s.strip()])
    char_count = len(transcript)
    
    # Simple sentiment indicators
    positive_words = ['good', 'great', 'excellent', 'perfect', 'amazing', 'wonderful', 'yes', 'agree']
    negative_words = ['bad', 'terrible', 'awful', 'horrible', 'disappointing', 'no', 'disagree']
    question_words = ['what', 'how', 'when', 'where', 'why', 'which', 'who']
    
    positive_count = sum(1 for word in positive_words if word in transcript.lower())
    negative_count = sum(1 for word in negative_words if word in transcript.lower())
    question_count = sum(1 for word in question_words if word in transcript.lower())
    
    # Speaking patterns
    exclamation_count = transcript.count('!')
    question_mark_count = transcript.count('?')
    
    return {
        'word_count': word_count,
        'sentence_count': sentence_count,
        'char_count': char_count,
        'avg_word_length': char_count / word_count if word_count > 0 else 0,
        'positive_word_count': positive_count,
        'negative_word_count': negative_count,
        'question_word_count': question_count,
        'exclamation_count': exclamation_count,
        'question_mark_count': question_mark_count,
        'sentiment_ratio': (positive_count - negative_count) / word_count if word_count > 0 else 0,
    }


def _calculate_engagement_level(features: Dict[str, Any]) -> str:
    """Calculate engagement level based on features."""
    engagement_score = 0
    
    # Word count indicates engagement
    if features['word_count'] > 500:
        engagement_score += 2
    elif features['word_count'] > 200:
        engagement_score += 1
    
    # Questions indicate engagement
    if features['question_word_count'] > 5:
        engagement_score += 2
    elif features['question_word_count'] > 2:
        engagement_score += 1
    
    # Positive sentiment indicates engagement
    if features['sentiment_ratio'] > 0.02:
        engagement_score += 2
    elif features['sentiment_ratio'] > 0:
        engagement_score += 1
    
    # Exclamations can indicate engagement
    if features['exclamation_count'] > 0:
        engagement_score += 1
    
    if engagement_score >= 5:
        return "high"
    elif engagement_score >= 3:
        return "medium"
    else:
        return "low"


def _generate_conversation_insights(
    features: Dict[str, Any],
    request: NLPAnalysisRequest,
) -> List[ConversationInsight]:
    """Generate conversation insights based on analysis."""
    insights = []
    
    # Communication quality insight
    communication_score = min(100, max(0, 50 + features['sentiment_ratio'] * 1000))
    insights.append(ConversationInsight(
        category="Communication Quality",
        score=communication_score,
        description=f"Overall communication effectiveness based on language patterns",
        recommendations=[
            "Use more positive language" if communication_score < 60 else "Maintain positive communication style",
            "Ask more engaging questions" if features['question_word_count'] < 3 else "Good use of questions",
        ]
    ))
    
    # Engagement insight
    engagement_score = min(100, max(0, (features['word_count'] / 10) + (features['question_word_count'] * 5)))
    insights.append(ConversationInsight(
        category="Prospect Engagement",
        score=engagement_score,
        description=f"Level of prospect participation and interest",
        recommendations=[
            "Encourage more prospect participation" if engagement_score < 50 else "Good prospect engagement",
            "Use open-ended questions to increase interaction",
        ]
    ))
    
    # Conversation flow insight
    flow_score = min(100, max(0, 70 - abs(features['question_mark_count'] - features['sentence_count'] * 0.1) * 10))
    insights.append(ConversationInsight(
        category="Conversation Flow",
        score=flow_score,
        description="Natural flow and structure of the conversation",
        recommendations=[
            "Balance questions and statements" if flow_score < 60 else "Good conversation balance",
            "Allow for natural pauses and responses",
        ]
    ))
    
    # Content depth insight
    depth_score = min(100, max(0, features['word_count'] / 5))
    insights.append(ConversationInsight(
        category="Content Depth",
        score=depth_score,
        description="Depth and substance of conversation content",
        recommendations=[
            "Explore topics in more detail" if depth_score < 50 else "Good conversation depth",
            "Ask follow-up questions to dive deeper",
        ]
    ))
    
    return insights


def _extract_main_topics(transcript: str) -> List[str]:
    """Extract main topics from transcript using simple keyword analysis."""
    # Simple topic extraction based on common business keywords
    topics = []
    
    business_keywords = {
        'pricing': ['price', 'cost', 'budget', 'expensive', 'cheap', 'affordable'],
        'features': ['feature', 'functionality', 'capability', 'option', 'tool'],
        'timeline': ['timeline', 'schedule', 'deadline', 'when', 'time'],
        'integration': ['integrate', 'connect', 'api', 'system', 'platform'],
        'support': ['support', 'help', 'assistance', 'training', 'onboarding'],
        'decision': ['decision', 'decide', 'choose', 'select', 'approve'],
        'team': ['team', 'colleague', 'manager', 'department', 'organization'],
        'competition': ['competitor', 'alternative', 'compare', 'versus'],
    }
    
    transcript_lower = transcript.lower()
    
    for topic, keywords in business_keywords.items():
        if any(keyword in transcript_lower for keyword in keywords):
            topics.append(topic.title())
    
    return topics[:5]  # Return top 5 topics


def _extract_keywords(transcript: str) -> List[str]:
    """Extract important keywords from transcript."""
    # Simple keyword extraction
    words = transcript.lower().split()
    
    # Filter out common words
    stop_words = {
        'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for',
        'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have',
        'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should',
        'i', 'you', 'he', 'she', 'it', 'we', 'they', 'me', 'him', 'her', 'us', 'them'
    }
    
    # Count word frequency
    word_freq = {}
    for word in words:
        word = word.strip('.,!?;:"()[]{}')
        if len(word) > 3 and word not in stop_words:
            word_freq[word] = word_freq.get(word, 0) + 1
    
    # Return most frequent words
    keywords = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)
    return [word for word, freq in keywords[:10]]


@router.post("/analyze", response_model=NLPAnalysisResponse)
async def analyze_conversation(
    request: NLPAnalysisRequest,
    user: User = Depends(require_read_access),
):
    """
    Analyze sales conversation transcript.
    
    Provides comprehensive NLP analysis including:
    - Overall conversation quality and sentiment
    - Engagement level and participation metrics
    - Key topics and themes discussed
    - Speaking patterns and conversation flow
    - Actionable insights and recommendations
    
    Returns detailed analysis to help improve sales conversations.
    """
    logger.info(
        "NLP analysis requested",
        user_id=user.user_id,
        conversation_id=request.conversation_id,
        transcript_length=len(request.transcript),
    )
    
    try:
        # Extract basic features
        features = _extract_basic_features(request)
        
        # Try to use ML model if available
        registry = get_model_registry()
        model = registry.get_model("conversation_analyzer", auto_load=True)
        
        overall_quality_score = 75.0  # Default
        sentiment_score = 0.0
        
        if model:
            try:
                # Prepare features for model
                features_df = pd.DataFrame([features])
                
                # Create feature pipeline and transform
                feature_pipeline = create_feature_pipeline("nlp")
                
                try:
                    features_transformed = feature_pipeline.transform(features_df)
                    
                    # Get model predictions
                    if hasattr(model, 'analyze_conversations'):
                        analysis = model.analyze_conversations(features_transformed)
                        if 'insights' in analysis and 'sentiment' in analysis['insights']:
                            sentiment_insights = analysis['insights']['sentiment']
                            overall_quality_score = sentiment_insights.get('average_sentiment', 0.5) * 100
                    
                    # Track prediction
                    track_model_prediction(
                        model_name=model.model_name,
                        model_version=model.version
                    )
                    
                except Exception as e:
                    logger.warning(f"Model prediction failed, using fallback: {e}")
            
            except Exception as e:
                logger.warning(f"Model analysis failed, using rule-based analysis: {e}")
        
        # Calculate sentiment score
        sentiment_score = max(-1, min(1, features['sentiment_ratio'] * 10))
        
        # Determine engagement level
        engagement_level = _calculate_engagement_level(features)
        
        # Generate insights
        insights = _generate_conversation_insights(features, request)
        
        # Extract topics and keywords
        main_topics = _extract_main_topics(request.transcript)
        keywords = _extract_keywords(request.transcript)
        
        # Calculate speaking time distribution (simplified)
        speaking_time_distribution = {
            "sales_rep": 0.6,  # Assume 60% sales rep
            "prospect": 0.4,   # Assume 40% prospect
        }
        
        # Adjust quality score based on features
        if features['word_count'] < 100:
            overall_quality_score *= 0.8  # Penalize very short conversations
        
        if features['question_word_count'] == 0:
            overall_quality_score *= 0.9  # Penalize no questions
        
        overall_quality_score = max(0, min(100, overall_quality_score))
        
        response = NLPAnalysisResponse(
            conversation_id=request.conversation_id,
            overall_quality_score=overall_quality_score,
            sentiment_score=sentiment_score,
            engagement_level=engagement_level,
            insights=insights,
            main_topics=main_topics,
            keywords=keywords,
            speaking_time_distribution=speaking_time_distribution,
            model_version=model.version if model else "rule-based-1.0.0",
        )
        
        logger.info(
            "NLP analysis completed",
            user_id=user.user_id,
            conversation_id=request.conversation_id,
            quality_score=overall_quality_score,
            sentiment_score=sentiment_score,
            engagement_level=engagement_level,
        )
        
        return response
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "NLP analysis failed",
            user_id=user.user_id,
            conversation_id=request.conversation_id,
            error=str(e),
            exc_info=True,
        )
        raise HTTPException(
            status_code=500,
            detail="Failed to analyze conversation"
        )
