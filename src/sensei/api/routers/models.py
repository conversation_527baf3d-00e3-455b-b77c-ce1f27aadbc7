"""
Model management endpoints for Sensei AI API.

Provides endpoints for model information, training status, and management.
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from datetime import datetime

from ..models import ModelInfoResponse, ModelInfo, APIResponse
from ..auth import require_model_access, require_model_train_access, User
from ...utils import get_logger
from ...models import get_model_registry

logger = get_logger(__name__)
router = APIRouter()


@router.get("/", response_model=ModelInfoResponse)
async def list_models(
    model_type: Optional[str] = Query(None, description="Filter by model type"),
    user: User = Depends(require_model_access),
):
    """
    List all available models.
    
    Returns information about all models in the registry including:
    - Model metadata and performance metrics
    - Training status and version information
    - Resource usage and availability
    """
    logger.info(
        "Models list requested",
        user_id=user.user_id,
        model_type_filter=model_type,
    )
    
    try:
        registry = get_model_registry()
        models_data = registry.list_models(model_type=model_type)
        
        # Convert to API model format
        models = []
        for model_data in models_data:
            # Determine status
            status = "active" if model_data['is_active'] else "inactive"
            
            # Extract performance metrics
            metrics = model_data.get('performance_metrics', {})
            
            model_info = ModelInfo(
                name=model_data['name'],
                type=model_data['type'],
                version=model_data['version'],
                status=status,
                accuracy=metrics.get('accuracy'),
                precision=metrics.get('precision'),
                recall=metrics.get('recall'),
                f1_score=metrics.get('f1_score'),
                created_at=model_data['created_at'],
                last_trained=model_data['trained_at'],
                training_samples=None,  # Would need to get from metadata
                model_size_mb=None,     # Would need to get from metadata
            )
            models.append(model_info)
        
        # Get registry stats
        stats = registry.get_registry_stats()
        
        response = ModelInfoResponse(
            models=models,
            total_models=stats['total_models'],
            active_models=stats['active_models'],
        )
        
        logger.info(
            "Models list returned",
            user_id=user.user_id,
            total_models=len(models),
            filtered_type=model_type,
        )
        
        return response
    
    except Exception as e:
        logger.error(
            "Failed to list models",
            user_id=user.user_id,
            error=str(e),
            exc_info=True,
        )
        raise HTTPException(
            status_code=500,
            detail="Failed to retrieve model information"
        )


@router.get("/{model_name}", response_model=ModelInfo)
async def get_model_info(
    model_name: str,
    version: Optional[str] = Query(None, description="Model version"),
    user: User = Depends(require_model_access),
):
    """
    Get detailed information about a specific model.
    
    Returns comprehensive model information including:
    - Performance metrics and validation results
    - Training history and configuration
    - Feature importance and model insights
    """
    logger.info(
        "Model info requested",
        user_id=user.user_id,
        model_name=model_name,
        version=version,
    )
    
    try:
        registry = get_model_registry()
        
        # Get model
        model = registry.get_model(model_name, version=version, auto_load=False)
        if not model:
            raise HTTPException(
                status_code=404,
                detail=f"Model '{model_name}' not found"
            )
        
        # Get model metadata
        metadata = model.metadata
        
        # Determine status
        status = "active" if model_name in registry._active_models else "inactive"
        
        model_info = ModelInfo(
            name=metadata.model_name,
            type=metadata.model_type,
            version=metadata.version,
            status=status,
            accuracy=metadata.performance_metrics.get('accuracy'),
            precision=metadata.performance_metrics.get('precision'),
            recall=metadata.performance_metrics.get('recall'),
            f1_score=metadata.performance_metrics.get('f1_score'),
            created_at=metadata.created_at,
            last_trained=metadata.trained_at,
            training_samples=metadata.training_samples,
            model_size_mb=metadata.model_size_mb,
        )
        
        logger.info(
            "Model info returned",
            user_id=user.user_id,
            model_name=model_name,
            version=model_info.version,
        )
        
        return model_info
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Failed to get model info",
            user_id=user.user_id,
            model_name=model_name,
            error=str(e),
            exc_info=True,
        )
        raise HTTPException(
            status_code=500,
            detail="Failed to retrieve model information"
        )


@router.post("/{model_name}/load", response_model=APIResponse)
async def load_model(
    model_name: str,
    version: Optional[str] = Query(None, description="Model version to load"),
    user: User = Depends(require_model_access),
):
    """
    Load a model into memory for serving.
    
    Loads the specified model version into the active model cache
    for faster prediction serving.
    """
    logger.info(
        "Model load requested",
        user_id=user.user_id,
        model_name=model_name,
        version=version,
    )
    
    try:
        registry = get_model_registry()
        
        # Load model
        model = registry.load_model(model_name, version=version)
        
        logger.info(
            "Model loaded successfully",
            user_id=user.user_id,
            model_name=model_name,
            version=model.version,
        )
        
        return APIResponse(
            success=True,
            timestamp=datetime.now(),
        )
    
    except FileNotFoundError:
        raise HTTPException(
            status_code=404,
            detail=f"Model '{model_name}' version '{version}' not found"
        )
    except Exception as e:
        logger.error(
            "Failed to load model",
            user_id=user.user_id,
            model_name=model_name,
            version=version,
            error=str(e),
            exc_info=True,
        )
        raise HTTPException(
            status_code=500,
            detail="Failed to load model"
        )


@router.delete("/{model_name}", response_model=APIResponse)
async def delete_model(
    model_name: str,
    version: Optional[str] = Query(None, description="Model version to delete (all if not specified)"),
    user: User = Depends(require_model_train_access),
):
    """
    Delete a model from the registry.
    
    Removes the specified model version(s) from the registry.
    This action cannot be undone.
    """
    logger.warning(
        "Model deletion requested",
        user_id=user.user_id,
        model_name=model_name,
        version=version,
    )
    
    try:
        registry = get_model_registry()
        
        # Delete model
        success = registry.delete_model(model_name, version=version)
        
        if not success:
            raise HTTPException(
                status_code=404,
                detail=f"Model '{model_name}' not found"
            )
        
        logger.warning(
            "Model deleted",
            user_id=user.user_id,
            model_name=model_name,
            version=version,
        )
        
        return APIResponse(
            success=True,
            timestamp=datetime.now(),
        )
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Failed to delete model",
            user_id=user.user_id,
            model_name=model_name,
            version=version,
            error=str(e),
            exc_info=True,
        )
        raise HTTPException(
            status_code=500,
            detail="Failed to delete model"
        )


@router.get("/{model_name}/performance")
async def get_model_performance(
    model_name: str,
    version: Optional[str] = Query(None, description="Model version"),
    user: User = Depends(require_model_access),
):
    """
    Get detailed performance metrics for a model.
    
    Returns comprehensive performance analysis including:
    - Accuracy, precision, recall, F1 score
    - Feature importance rankings
    - Model validation results
    - Performance over time (if available)
    """
    logger.info(
        "Model performance requested",
        user_id=user.user_id,
        model_name=model_name,
        version=version,
    )
    
    try:
        registry = get_model_registry()
        
        # Get model
        model = registry.get_model(model_name, version=version, auto_load=False)
        if not model:
            raise HTTPException(
                status_code=404,
                detail=f"Model '{model_name}' not found"
            )
        
        # Get performance data
        metadata = model.metadata
        feature_importance = model.get_feature_importance()
        
        performance_data = {
            "model_name": metadata.model_name,
            "version": metadata.version,
            "performance_metrics": metadata.performance_metrics,
            "feature_importance": feature_importance,
            "training_info": {
                "training_samples": metadata.training_samples,
                "feature_count": metadata.feature_count,
                "training_duration_seconds": metadata.training_duration_seconds,
                "data_quality_score": metadata.data_quality_score,
            },
            "model_info": {
                "model_type": metadata.model_type,
                "created_at": metadata.created_at,
                "trained_at": metadata.trained_at,
                "model_size_mb": metadata.model_size_mb,
            }
        }
        
        logger.info(
            "Model performance returned",
            user_id=user.user_id,
            model_name=model_name,
            version=metadata.version,
        )
        
        return performance_data
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Failed to get model performance",
            user_id=user.user_id,
            model_name=model_name,
            error=str(e),
            exc_info=True,
        )
        raise HTTPException(
            status_code=500,
            detail="Failed to retrieve model performance data"
        )


@router.get("/registry/stats")
async def get_registry_stats(
    user: User = Depends(require_model_access),
):
    """
    Get model registry statistics.
    
    Returns overall registry health and usage statistics.
    """
    logger.info(
        "Registry stats requested",
        user_id=user.user_id,
    )
    
    try:
        registry = get_model_registry()
        stats = registry.get_registry_stats()
        
        logger.info(
            "Registry stats returned",
            user_id=user.user_id,
            total_models=stats['total_models'],
        )
        
        return {
            "success": True,
            "timestamp": datetime.now(),
            "stats": stats,
        }
    
    except Exception as e:
        logger.error(
            "Failed to get registry stats",
            user_id=user.user_id,
            error=str(e),
            exc_info=True,
        )
        raise HTTPException(
            status_code=500,
            detail="Failed to retrieve registry statistics"
        )
