"""
Health check endpoints for Sensei AI API.

Provides comprehensive health monitoring including system status,
component health, and performance metrics.
"""

import time
import psutil
from datetime import datetime
from typing import List
from fastapi import APIRouter, Depends
from fastapi.responses import JSONResponse

from ..models import HealthResponse, HealthStatus, ComponentHealth
from ..auth import optional_auth, User
from ...utils import get_logger, get_config
from ...utils.monitoring import get_metrics_registry
from ...models import get_model_registry
from ...data import get_bigquery_client

logger = get_logger(__name__)
router = APIRouter()

# Track startup time for uptime calculation
startup_time = time.time()


async def check_database_health() -> ComponentHealth:
    """Check BigQuery database connectivity."""
    start_time = time.time()
    
    try:
        # Test BigQuery connection with a simple query
        bq_client = get_bigquery_client()
        
        # Validate query (dry run)
        test_query = "SELECT 1 as test_value"
        validation_result = bq_client.validate_query(test_query)
        
        response_time = (time.time() - start_time) * 1000
        
        if validation_result.get('valid', False):
            return ComponentHealth(
                name="bigquery",
                status=HealthStatus.HEALTHY,
                message="BigQuery connection successful",
                response_time_ms=response_time,
            )
        else:
            return ComponentHealth(
                name="bigquery",
                status=HealthStatus.UNHEALTHY,
                message=f"BigQuery validation failed: {validation_result.get('error', 'Unknown error')}",
                response_time_ms=response_time,
            )
    
    except Exception as e:
        response_time = (time.time() - start_time) * 1000
        logger.error("BigQuery health check failed", error=str(e))
        
        return ComponentHealth(
            name="bigquery",
            status=HealthStatus.UNHEALTHY,
            message=f"BigQuery connection failed: {str(e)}",
            response_time_ms=response_time,
        )


async def check_model_registry_health() -> ComponentHealth:
    """Check model registry health."""
    start_time = time.time()
    
    try:
        registry = get_model_registry()
        stats = registry.get_registry_stats()
        
        response_time = (time.time() - start_time) * 1000
        
        # Consider healthy if we can get stats
        return ComponentHealth(
            name="model_registry",
            status=HealthStatus.HEALTHY,
            message=f"Registry operational with {stats['total_models']} models",
            response_time_ms=response_time,
        )
    
    except Exception as e:
        response_time = (time.time() - start_time) * 1000
        logger.error("Model registry health check failed", error=str(e))
        
        return ComponentHealth(
            name="model_registry",
            status=HealthStatus.UNHEALTHY,
            message=f"Model registry error: {str(e)}",
            response_time_ms=response_time,
        )


async def check_metrics_health() -> ComponentHealth:
    """Check metrics system health."""
    start_time = time.time()
    
    try:
        metrics_registry = get_metrics_registry()
        
        # Try to get metrics
        metrics_data = metrics_registry.get_metrics()
        
        response_time = (time.time() - start_time) * 1000
        
        return ComponentHealth(
            name="metrics",
            status=HealthStatus.HEALTHY,
            message="Metrics system operational",
            response_time_ms=response_time,
        )
    
    except Exception as e:
        response_time = (time.time() - start_time) * 1000
        logger.error("Metrics health check failed", error=str(e))
        
        return ComponentHealth(
            name="metrics",
            status=HealthStatus.DEGRADED,
            message=f"Metrics system error: {str(e)}",
            response_time_ms=response_time,
        )


def get_system_metrics():
    """Get system performance metrics."""
    try:
        # Memory usage
        memory = psutil.virtual_memory()
        memory_usage_mb = (memory.total - memory.available) / (1024 * 1024)
        
        # CPU usage
        cpu_usage = psutil.cpu_percent(interval=0.1)
        
        return {
            "memory_usage_mb": round(memory_usage_mb, 2),
            "cpu_usage_percent": round(cpu_usage, 2),
        }
    
    except Exception as e:
        logger.warning(f"Failed to get system metrics: {e}")
        return {
            "memory_usage_mb": None,
            "cpu_usage_percent": None,
        }


@router.get("/", response_model=HealthResponse)
async def health_check(user: User = Depends(optional_auth)):
    """
    Comprehensive health check endpoint.
    
    Returns detailed health information including:
    - Overall system status
    - Individual component health
    - System performance metrics
    - Uptime information
    """
    logger.debug("Health check requested", user_id=user.user_id if user else None)
    
    # Check all components
    components = []
    
    # Database health
    db_health = await check_database_health()
    components.append(db_health)
    
    # Model registry health
    registry_health = await check_model_registry_health()
    components.append(registry_health)
    
    # Metrics health
    metrics_health = await check_metrics_health()
    components.append(metrics_health)
    
    # Determine overall status
    unhealthy_components = [c for c in components if c.status == HealthStatus.UNHEALTHY]
    degraded_components = [c for c in components if c.status == HealthStatus.DEGRADED]
    
    if unhealthy_components:
        overall_status = HealthStatus.UNHEALTHY
    elif degraded_components:
        overall_status = HealthStatus.DEGRADED
    else:
        overall_status = HealthStatus.HEALTHY
    
    # Get system metrics
    system_metrics = get_system_metrics()
    
    # Get active models count
    active_models = None
    try:
        registry = get_model_registry()
        stats = registry.get_registry_stats()
        active_models = stats.get('active_models', 0)
    except Exception:
        pass
    
    # Calculate uptime
    uptime_seconds = time.time() - startup_time
    
    response = HealthResponse(
        status=overall_status,
        uptime_seconds=round(uptime_seconds, 2),
        components=components,
        memory_usage_mb=system_metrics["memory_usage_mb"],
        cpu_usage_percent=system_metrics["cpu_usage_percent"],
        active_models=active_models,
    )
    
    # Log health check result
    logger.info(
        "Health check completed",
        status=overall_status.value,
        components_checked=len(components),
        unhealthy_count=len(unhealthy_components),
        degraded_count=len(degraded_components),
        uptime_seconds=uptime_seconds,
    )
    
    return response


@router.get("/liveness")
async def liveness_check():
    """
    Simple liveness check for Kubernetes/Docker.
    
    Returns 200 OK if the service is running.
    """
    return {"status": "alive", "timestamp": datetime.now()}


@router.get("/readiness")
async def readiness_check():
    """
    Readiness check for Kubernetes/Docker.
    
    Returns 200 OK if the service is ready to handle requests.
    """
    # Check critical components
    try:
        # Test database connection
        db_health = await check_database_health()
        if db_health.status == HealthStatus.UNHEALTHY:
            return JSONResponse(
                status_code=503,
                content={
                    "status": "not_ready",
                    "reason": "Database unavailable",
                    "timestamp": datetime.now().isoformat(),
                }
            )
        
        # Test model registry
        registry_health = await check_model_registry_health()
        if registry_health.status == HealthStatus.UNHEALTHY:
            return JSONResponse(
                status_code=503,
                content={
                    "status": "not_ready",
                    "reason": "Model registry unavailable",
                    "timestamp": datetime.now().isoformat(),
                }
            )
        
        return {
            "status": "ready",
            "timestamp": datetime.now(),
            "components": {
                "database": db_health.status.value,
                "model_registry": registry_health.status.value,
            }
        }
    
    except Exception as e:
        logger.error("Readiness check failed", error=str(e))
        return JSONResponse(
            status_code=503,
            content={
                "status": "not_ready",
                "reason": f"Health check failed: {str(e)}",
                "timestamp": datetime.now().isoformat(),
            }
        )


@router.get("/startup")
async def startup_check():
    """
    Startup check for Kubernetes/Docker.
    
    Returns 200 OK when the service has completed startup.
    """
    # Check if service has been running for at least 10 seconds
    uptime = time.time() - startup_time
    
    if uptime < 10:
        return JSONResponse(
            status_code=503,
            content={
                "status": "starting",
                "uptime_seconds": round(uptime, 2),
                "timestamp": datetime.now().isoformat(),
            }
        )
    
    # Check if critical components are available
    try:
        db_health = await check_database_health()
        registry_health = await check_model_registry_health()
        
        if (db_health.status == HealthStatus.UNHEALTHY or 
            registry_health.status == HealthStatus.UNHEALTHY):
            return JSONResponse(
                status_code=503,
                content={
                    "status": "starting",
                    "reason": "Critical components not ready",
                    "uptime_seconds": round(uptime, 2),
                    "timestamp": datetime.now().isoformat(),
                }
            )
        
        return {
            "status": "started",
            "uptime_seconds": round(uptime, 2),
            "timestamp": datetime.now(),
        }
    
    except Exception as e:
        logger.error("Startup check failed", error=str(e))
        return JSONResponse(
            status_code=503,
            content={
                "status": "starting",
                "reason": f"Startup check failed: {str(e)}",
                "uptime_seconds": round(uptime, 2),
                "timestamp": datetime.now().isoformat(),
            }
        )
