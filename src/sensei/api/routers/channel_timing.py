"""
Channel and timing optimization endpoints for Sensei AI API.

Provides ML-powered recommendations for optimal communication channels and timing.
"""

import numpy as np
import pandas as pd
from typing import List, Dict, Any
from fastapi import APIRouter, Depends, HTTPException
from datetime import datetime

from ..models import (
    ChannelTimingRequest,
    ChannelTimingResponse,
    ChannelRecommendation,
)
from ..auth_simple import require_read_access, User
from ...utils import get_logger
from ...utils.monitoring import track_model_prediction
from ...models import get_model_registry
from ...features import create_feature_pipeline

logger = get_logger(__name__)
router = APIRouter()


def _prepare_features_from_request(request: ChannelTimingRequest) -> pd.DataFrame:
    """
    Convert API request to feature DataFrame.
    
    Args:
        request: Channel timing request
        
    Returns:
        DataFrame with features
    """
    # Map request fields to feature names
    feature_data = {
        'id_contact': request.prospect_id,
        'total_emails': request.email_interactions or 0,
        'total_calls': request.call_interactions or 0,
        'total_meetings': request.meeting_interactions or 0,
        
        # Success rates
        'email_success_rate': request.email_success_rate or 0,
        'call_success_rate': request.call_success_rate or 0,
        'meeting_success_rate': request.meeting_success_rate or 0,
        
        # Deal information
        'montant': request.deal_value or 0,
        'dealstage': request.deal_stage or 'unknown',
        
        # Timing features (simplified)
        'creation_hour': 12,  # Default to noon
        'creation_day_of_week': 3,  # Default to Wednesday
        'creation_month': datetime.now().month,
        
        # Derived features
        'preferred_channel': _determine_preferred_channel(request),
        'optimal_time_slot': 'afternoon',  # Default
        'optimal_day_type': 'weekday_peak',  # Default
    }
    
    return pd.DataFrame([feature_data])


def _determine_preferred_channel(request: ChannelTimingRequest) -> str:
    """Determine preferred channel based on success rates."""
    channels = {
        'email': request.email_success_rate or 0,
        'call': request.call_success_rate or 0,
        'meeting': request.meeting_success_rate or 0,
    }
    
    # Return channel with highest success rate
    return max(channels, key=channels.get)


@router.post("/recommend", response_model=ChannelTimingResponse)
async def recommend_channel_timing(
    request: ChannelTimingRequest,
    user: User = Depends(require_read_access),
):
    """
    Recommend optimal communication channel and timing.
    
    Analyzes prospect interaction history and preferences to recommend:
    - Best communication channel (email, call, meeting)
    - Optimal contact timing
    - Alternative channels with success probabilities
    - Times to avoid based on historical data
    
    Returns actionable recommendations for sales outreach optimization.
    """
    logger.info(
        "Channel timing recommendation requested",
        user_id=user.user_id,
        prospect_id=request.prospect_id,
    )
    
    try:
        # Get model
        registry = get_model_registry()
        model = registry.get_model("channel_timing_optimizer", auto_load=True)
        
        if not model:
            # Fallback to rule-based recommendations
            return _generate_fallback_recommendations(request, user)
        
        # Prepare features
        features_df = _prepare_features_from_request(request)
        
        # Create feature pipeline and transform
        feature_pipeline = create_feature_pipeline("channel_timing")
        
        try:
            features_transformed = feature_pipeline.transform(features_df)
        except Exception:
            # Fallback if pipeline fails
            features_transformed = np.zeros((1, 30))  # Dummy features
        
        # Make prediction
        predictions = model.predict(features_transformed)
        
        try:
            probabilities = model.predict_proba(features_transformed)
        except Exception:
            probabilities = None
        
        # Get channel recommendations
        if hasattr(model, 'get_channel_recommendations'):
            recommendations = model.get_channel_recommendations(
                features_transformed,
                top_k=3,
                include_probabilities=True
            )
            
            if recommendations:
                rec = recommendations[0]
                primary_channel = rec['primary_channel']
                channel_recs = rec['recommendations']
            else:
                primary_channel = _determine_preferred_channel(request)
                channel_recs = []
        else:
            # Fallback logic
            primary_channel = _determine_preferred_channel(request)
            channel_recs = []
        
        # Create primary recommendation
        primary_recommendation = ChannelRecommendation(
            channel=primary_channel,
            probability=0.75,  # Default probability
            confidence="medium",
            optimal_timing="9:00-11:00 AM or 2:00-4:00 PM"
        )
        
        # Create alternative recommendations
        alternative_recommendations = []
        channels = ['email', 'call', 'meeting']
        
        for channel in channels:
            if channel != primary_channel:
                prob = 0.6 if channel == 'email' else 0.5
                alt_rec = ChannelRecommendation(
                    channel=channel,
                    probability=prob,
                    confidence="medium" if prob > 0.5 else "low",
                    optimal_timing="10:00 AM - 12:00 PM" if channel == 'call' else "Afternoon"
                )
                alternative_recommendations.append(alt_rec)
        
        # Generate timing insights
        optimal_contact_times = [
            "Tuesday-Thursday 9:00-11:00 AM",
            "Tuesday-Thursday 2:00-4:00 PM",
        ]
        
        avoid_times = [
            "Monday mornings",
            "Friday afternoons",
            "Lunch hours (12:00-1:00 PM)",
        ]
        
        # Track prediction
        track_model_prediction(
            model_name=model.model_name,
            model_version=model.version
        )
        
        response = ChannelTimingResponse(
            prospect_id=request.prospect_id,
            primary_recommendation=primary_recommendation,
            alternative_recommendations=alternative_recommendations,
            optimal_contact_times=optimal_contact_times,
            avoid_times=avoid_times,
            model_version=model.version,
        )
        
        logger.info(
            "Channel timing recommendation completed",
            user_id=user.user_id,
            prospect_id=request.prospect_id,
            primary_channel=primary_channel,
        )
        
        return response
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Channel timing recommendation failed",
            user_id=user.user_id,
            prospect_id=request.prospect_id,
            error=str(e),
            exc_info=True,
        )
        raise HTTPException(
            status_code=500,
            detail="Failed to generate channel timing recommendations"
        )


def _generate_fallback_recommendations(
    request: ChannelTimingRequest,
    user: User,
) -> ChannelTimingResponse:
    """Generate rule-based recommendations when model is unavailable."""
    logger.warning(
        "Using fallback recommendations",
        user_id=user.user_id,
        prospect_id=request.prospect_id,
    )
    
    # Determine best channel based on success rates
    channels = {
        'email': request.email_success_rate or 0.3,
        'call': request.call_success_rate or 0.2,
        'meeting': request.meeting_success_rate or 0.1,
    }
    
    primary_channel = max(channels, key=channels.get)
    primary_prob = channels[primary_channel]
    
    # Create primary recommendation
    primary_recommendation = ChannelRecommendation(
        channel=primary_channel,
        probability=primary_prob,
        confidence="medium" if primary_prob > 0.5 else "low",
        optimal_timing="9:00-11:00 AM or 2:00-4:00 PM"
    )
    
    # Create alternatives
    alternative_recommendations = []
    for channel, prob in sorted(channels.items(), key=lambda x: x[1], reverse=True)[1:]:
        alt_rec = ChannelRecommendation(
            channel=channel,
            probability=prob,
            confidence="medium" if prob > 0.3 else "low",
            optimal_timing="Business hours"
        )
        alternative_recommendations.append(alt_rec)
    
    # Default timing recommendations
    optimal_contact_times = [
        "Tuesday-Thursday 9:00-11:00 AM",
        "Tuesday-Thursday 2:00-4:00 PM",
    ]
    
    avoid_times = [
        "Monday mornings",
        "Friday afternoons",
        "Weekends",
    ]
    
    return ChannelTimingResponse(
        prospect_id=request.prospect_id,
        primary_recommendation=primary_recommendation,
        alternative_recommendations=alternative_recommendations,
        optimal_contact_times=optimal_contact_times,
        avoid_times=avoid_times,
        model_version="fallback-1.0.0",
    )
