"""
Simplified Authentication for Sensei AI API v1.0

This module provides simplified authentication for development and testing.
For production, implement proper JWT authentication.
"""

import hashlib
import secrets
from typing import Optional, List, Dict, Any
from datetime import datetime, timedelta
from fastapi import HTTPException, Depends, Security
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel
import logging

logger = logging.getLogger(__name__)

# Security scheme
security = HTTPBearer()


class User(BaseModel):
    """User model for authentication."""
    user_id: str
    username: str
    email: str
    roles: List[str] = []
    permissions: List[str] = []
    is_active: bool = True
    last_login: Optional[datetime] = None


class APIKey(BaseModel):
    """API Key model."""
    key_id: str
    name: str
    key_hash: str
    permissions: List[str] = []
    is_active: bool = True
    created_at: datetime
    last_used: Optional[datetime] = None
    expires_at: Optional[datetime] = None


class SimplifiedAuthManager:
    """Simplified authentication manager for development."""
    
    def __init__(self):
        self.users: Dict[str, User] = {}
        self.api_keys: Dict[str, APIKey] = {}
        self._init_default_users()
    
    def _init_default_users(self):
        """Initialize default users for development."""
        # Default admin user
        admin_user = User(
            user_id="admin",
            username="admin",
            email="<EMAIL>",
            roles=["admin"],
            permissions=["read", "write", "admin"],
            is_active=True
        )
        self.users["admin"] = admin_user
        
        # Default API key for development
        dev_key = "dev-api-key-12345"
        key_hash = hashlib.sha256(dev_key.encode()).hexdigest()
        
        api_key = APIKey(
            key_id="dev-key-1",
            name="Development Key",
            key_hash=key_hash,
            permissions=["read", "write"],
            is_active=True,
            created_at=datetime.now()
        )
        self.api_keys[key_hash] = api_key
        
        logger.info("Initialized simplified auth with default users")
        logger.info(f"Development API key: {dev_key}")
    
    def authenticate_api_key(self, api_key: str) -> Optional[User]:
        """
        Authenticate using API key.
        
        Args:
            api_key: The API key to authenticate
            
        Returns:
            User object if authentication successful, None otherwise
        """
        try:
            key_hash = hashlib.sha256(api_key.encode()).hexdigest()
            
            if key_hash not in self.api_keys:
                logger.warning(f"Invalid API key attempted: {api_key[:8]}...")
                return None
            
            api_key_obj = self.api_keys[key_hash]
            
            if not api_key_obj.is_active:
                logger.warning(f"Inactive API key attempted: {api_key_obj.key_id}")
                return None
            
            # Check expiration
            if api_key_obj.expires_at and api_key_obj.expires_at < datetime.now():
                logger.warning(f"Expired API key attempted: {api_key_obj.key_id}")
                return None
            
            # Update last used
            api_key_obj.last_used = datetime.now()
            
            # Return admin user for simplicity
            user = self.users["admin"]
            user.last_login = datetime.now()
            
            logger.info(f"API key authenticated: {api_key_obj.key_id}")
            return user
            
        except Exception as e:
            logger.error(f"Authentication error: {e}")
            return None
    
    def check_permission(self, user: User, required_permission: str) -> bool:
        """
        Check if user has required permission.
        
        Args:
            user: User object
            required_permission: Required permission string
            
        Returns:
            True if user has permission
        """
        if not user.is_active:
            return False
        
        # Admin role has all permissions
        if "admin" in user.roles:
            return True
        
        return required_permission in user.permissions


# Global auth manager instance
auth_manager = SimplifiedAuthManager()


async def get_current_user(credentials: HTTPAuthorizationCredentials = Security(security)) -> User:
    """
    Get current authenticated user from API key.
    
    Args:
        credentials: HTTP authorization credentials
        
    Returns:
        Authenticated user
        
    Raises:
        HTTPException: If authentication fails
    """
    if not credentials:
        raise HTTPException(
            status_code=401,
            detail="Missing authentication credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # Extract API key from Bearer token
    api_key = credentials.credentials
    
    # Authenticate
    user = auth_manager.authenticate_api_key(api_key)
    
    if not user:
        raise HTTPException(
            status_code=401,
            detail="Invalid authentication credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    return user


def require_permission(permission: str):
    """
    Decorator to require specific permission.
    
    Args:
        permission: Required permission string
        
    Returns:
        Dependency function
    """
    def permission_checker(user: User = Depends(get_current_user)) -> User:
        if not auth_manager.check_permission(user, permission):
            raise HTTPException(
                status_code=403,
                detail=f"Insufficient permissions. Required: {permission}"
            )
        return user
    
    return permission_checker


# Convenience functions for common permissions
def require_read_access(user: User = Depends(require_permission("read"))) -> User:
    """Require read access."""
    return user


def require_write_access(user: User = Depends(require_permission("write"))) -> User:
    """Require write access."""
    return user


def require_admin_access(user: User = Depends(require_permission("admin"))) -> User:
    """Require admin access."""
    return user


# Optional authentication (for public endpoints with optional auth)
async def get_optional_user(credentials: Optional[HTTPAuthorizationCredentials] = Security(security)) -> Optional[User]:
    """
    Get current user if authenticated, None otherwise.
    
    Args:
        credentials: Optional HTTP authorization credentials
        
    Returns:
        User if authenticated, None otherwise
    """
    if not credentials:
        return None
    
    try:
        return await get_current_user(credentials)
    except HTTPException:
        return None


def create_api_key(name: str, permissions: List[str], expires_in_days: Optional[int] = None) -> str:
    """
    Create a new API key.
    
    Args:
        name: Name for the API key
        permissions: List of permissions
        expires_in_days: Optional expiration in days
        
    Returns:
        The generated API key
    """
    # Generate random API key
    api_key = f"sk-{secrets.token_urlsafe(32)}"
    key_hash = hashlib.sha256(api_key.encode()).hexdigest()
    
    # Calculate expiration
    expires_at = None
    if expires_in_days:
        expires_at = datetime.now() + timedelta(days=expires_in_days)
    
    # Create API key object
    api_key_obj = APIKey(
        key_id=f"key-{secrets.token_hex(8)}",
        name=name,
        key_hash=key_hash,
        permissions=permissions,
        is_active=True,
        created_at=datetime.now(),
        expires_at=expires_at
    )
    
    # Store API key
    auth_manager.api_keys[key_hash] = api_key_obj
    
    logger.info(f"Created API key: {api_key_obj.key_id}")
    
    return api_key


def revoke_api_key(key_id: str) -> bool:
    """
    Revoke an API key.
    
    Args:
        key_id: ID of the key to revoke
        
    Returns:
        True if key was revoked
    """
    for api_key in auth_manager.api_keys.values():
        if api_key.key_id == key_id:
            api_key.is_active = False
            logger.info(f"API key revoked: {key_id}")
            return True
    
    return False


def list_api_keys() -> List[Dict[str, Any]]:
    """
    List all API keys (without sensitive data).
    
    Returns:
        List of API key information
    """
    keys = []
    for api_key in auth_manager.api_keys.values():
        keys.append({
            "key_id": api_key.key_id,
            "name": api_key.name,
            "permissions": api_key.permissions,
            "is_active": api_key.is_active,
            "created_at": api_key.created_at,
            "last_used": api_key.last_used,
            "expires_at": api_key.expires_at
        })
    
    return keys
