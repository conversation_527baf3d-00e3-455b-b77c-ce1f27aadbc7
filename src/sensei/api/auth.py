"""
Authentication and authorization for Sensei AI API.

Provides secure API key authentication with role-based access control.
"""

import hashlib
import secrets
from typing import Optional, List, Dict, Any
from datetime import datetime, timedelta
from fastapi import HTTPException, Depends, Security
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel
# import jwt  # Temporarily disabled for Docker compatibility

from ..utils import get_logger, get_config
# from ..utils.security import generate_audit_log_entry  # Temporarily disabled

logger = get_logger(__name__)

# Security scheme
security = HTTPBearer()


class User(BaseModel):
    """User model for authentication."""
    user_id: str
    username: str
    email: str
    roles: List[str]
    permissions: List[str]
    is_active: bool = True
    created_at: datetime
    last_login: Optional[datetime] = None


class APIKey(BaseModel):
    """API key model."""
    key_id: str
    key_hash: str
    user_id: str
    name: str
    permissions: List[str]
    is_active: bool = True
    expires_at: Optional[datetime] = None
    created_at: datetime
    last_used: Optional[datetime] = None


class AuthManager:
    """Manages authentication and authorization."""
    
    def __init__(self):
        """Initialize auth manager."""
        self.config = get_config()
        
        # In-memory storage for demo (use database in production)
        self.users: Dict[str, User] = {}
        self.api_keys: Dict[str, APIKey] = {}
        
        # Create default admin user and API key for demo
        self._create_default_credentials()
    
    def _create_default_credentials(self) -> None:
        """Create default credentials for demo purposes."""
        # Create admin user
        admin_user = User(
            user_id="admin",
            username="admin",
            email="<EMAIL>",
            roles=["admin"],
            permissions=["*"],
            created_at=datetime.now(),
        )
        self.users["admin"] = admin_user
        
        # Create API key
        api_key = "sk-sensei-demo-" + secrets.token_urlsafe(32)
        key_hash = hashlib.sha256(api_key.encode()).hexdigest()
        
        api_key_obj = APIKey(
            key_id="demo-key",
            key_hash=key_hash,
            user_id="admin",
            name="Demo API Key",
            permissions=["*"],
            created_at=datetime.now(),
        )
        self.api_keys[key_hash] = api_key_obj
        
        logger.info(
            "Default credentials created",
            api_key=api_key,  # Only log in development
            user_id=admin_user.user_id,
        )
    
    def authenticate_api_key(self, api_key: str) -> Optional[User]:
        """
        Authenticate API key and return user.
        
        Args:
            api_key: API key to authenticate
            
        Returns:
            User object if authentication successful, None otherwise
        """
        # Hash the provided key
        key_hash = hashlib.sha256(api_key.encode()).hexdigest()
        
        # Find API key
        api_key_obj = self.api_keys.get(key_hash)
        if not api_key_obj:
            logger.warning("Invalid API key attempted", key_hash=key_hash[:8] + "...")
            return None
        
        # Check if key is active
        if not api_key_obj.is_active:
            logger.warning("Inactive API key used", key_id=api_key_obj.key_id)
            return None
        
        # Check expiration
        if api_key_obj.expires_at and datetime.now() > api_key_obj.expires_at:
            logger.warning("Expired API key used", key_id=api_key_obj.key_id)
            return None
        
        # Get user
        user = self.users.get(api_key_obj.user_id)
        if not user or not user.is_active:
            logger.warning("User not found or inactive", user_id=api_key_obj.user_id)
            return None
        
        # Update last used
        api_key_obj.last_used = datetime.now()
        user.last_login = datetime.now()
        
        # Generate audit log
        audit_entry = generate_audit_log_entry(
            action="api_key_authentication",
            user_id=user.user_id,
            details={
                "key_id": api_key_obj.key_id,
                "key_name": api_key_obj.name,
            }
        )
        logger.info("API key authenticated", **audit_entry)
        
        return user
    
    def check_permission(self, user: User, required_permission: str) -> bool:
        """
        Check if user has required permission.
        
        Args:
            user: User to check
            required_permission: Permission to check for
            
        Returns:
            True if user has permission
        """
        # Admin users have all permissions
        if "*" in user.permissions:
            return True
        
        # Check specific permission
        if required_permission in user.permissions:
            return True
        
        # Check wildcard permissions
        for permission in user.permissions:
            if permission.endswith("*"):
                prefix = permission[:-1]
                if required_permission.startswith(prefix):
                    return True
        
        return False


# Global auth manager
auth_manager = AuthManager()


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Security(security)
) -> User:
    """
    Get current authenticated user.
    
    Args:
        credentials: HTTP authorization credentials
        
    Returns:
        Authenticated user
        
    Raises:
        HTTPException: If authentication fails
    """
    if not credentials:
        raise HTTPException(
            status_code=401,
            detail="Missing authentication credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # Extract API key from Bearer token
    api_key = credentials.credentials
    
    # Authenticate
    user = auth_manager.authenticate_api_key(api_key)
    if not user:
        raise HTTPException(
            status_code=401,
            detail="Invalid authentication credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    return user


def require_permissions(*required_permissions: str):
    """
    Decorator to require specific permissions.
    
    Args:
        required_permissions: List of required permissions
        
    Returns:
        Dependency function
    """
    def permission_dependency(user: User = Depends(get_current_user)) -> User:
        """Check if user has required permissions."""
        for permission in required_permissions:
            if not auth_manager.check_permission(user, permission):
                logger.warning(
                    "Permission denied",
                    user_id=user.user_id,
                    required_permission=permission,
                    user_permissions=user.permissions,
                )
                
                raise HTTPException(
                    status_code=403,
                    detail=f"Insufficient permissions. Required: {permission}",
                )
        
        return user
    
    return permission_dependency


# Common permission dependencies
require_read_access = require_permissions("read")
require_write_access = require_permissions("write")
require_admin_access = require_permissions("admin")
require_model_access = require_permissions("model:read")
require_model_train_access = require_permissions("model:train")


class OptionalAuth:
    """Optional authentication for public endpoints."""
    
    def __init__(self):
        self.security = HTTPBearer(auto_error=False)
    
    async def __call__(
        self,
        credentials: Optional[HTTPAuthorizationCredentials] = Security(HTTPBearer(auto_error=False))
    ) -> Optional[User]:
        """Get user if authenticated, None otherwise."""
        if not credentials:
            return None
        
        try:
            return auth_manager.authenticate_api_key(credentials.credentials)
        except Exception:
            return None


optional_auth = OptionalAuth()


def create_api_key(
    user_id: str,
    name: str,
    permissions: List[str],
    expires_in_days: Optional[int] = None,
) -> str:
    """
    Create a new API key.
    
    Args:
        user_id: User ID to associate with the key
        name: Human-readable name for the key
        permissions: List of permissions for the key
        expires_in_days: Number of days until expiration
        
    Returns:
        Generated API key
    """
    # Generate key
    api_key = "sk-sensei-" + secrets.token_urlsafe(32)
    key_hash = hashlib.sha256(api_key.encode()).hexdigest()
    
    # Calculate expiration
    expires_at = None
    if expires_in_days:
        expires_at = datetime.now() + timedelta(days=expires_in_days)
    
    # Create API key object
    api_key_obj = APIKey(
        key_id=secrets.token_urlsafe(8),
        key_hash=key_hash,
        user_id=user_id,
        name=name,
        permissions=permissions,
        expires_at=expires_at,
        created_at=datetime.now(),
    )
    
    # Store API key
    auth_manager.api_keys[key_hash] = api_key_obj
    
    logger.info(
        "API key created",
        key_id=api_key_obj.key_id,
        user_id=user_id,
        name=name,
        permissions=permissions,
    )
    
    return api_key


def revoke_api_key(key_id: str) -> bool:
    """
    Revoke an API key.
    
    Args:
        key_id: ID of the key to revoke
        
    Returns:
        True if key was revoked
    """
    for key_hash, api_key in auth_manager.api_keys.items():
        if api_key.key_id == key_id:
            api_key.is_active = False
            logger.info("API key revoked", key_id=key_id)
            return True
    
    return False


def list_api_keys(user_id: str) -> List[Dict[str, Any]]:
    """
    List API keys for a user.
    
    Args:
        user_id: User ID to list keys for
        
    Returns:
        List of API key information
    """
    keys = []
    
    for api_key in auth_manager.api_keys.values():
        if api_key.user_id == user_id:
            keys.append({
                "key_id": api_key.key_id,
                "name": api_key.name,
                "permissions": api_key.permissions,
                "is_active": api_key.is_active,
                "created_at": api_key.created_at,
                "last_used": api_key.last_used,
                "expires_at": api_key.expires_at,
            })
    
    return keys
