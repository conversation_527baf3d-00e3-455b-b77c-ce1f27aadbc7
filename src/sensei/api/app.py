"""
FastAPI application factory for Sensei AI.

Creates and configures the main FastAPI application with all routes,
middleware, and production-ready settings.
"""

from fastapi import Fast<PERSON><PERSON>, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.responses import JSONResponse
import time
import uuid

from .routers import conversion, channel_timing, nlp, health, models
from .middleware import setup_middleware
from .models import ErrorResponse
from ..utils import get_logger, get_config
from ..utils.monitoring import track_api_request, get_metrics_registry

logger = get_logger(__name__)


def create_app() -> FastAPI:
    """
    Create and configure FastAPI application.
    
    Returns:
        Configured FastAPI application
    """
    config = get_config()
    
    # Create FastAPI app
    app = FastAPI(
        title="Sensei AI - B2B Sales Optimization Platform",
        description="""
        Production-ready ML platform for B2B sales optimization.
        
        ## Features
        
        * **Conversion Scoring**: Predict prospect conversion probability
        * **Channel Optimization**: Recommend optimal communication channels
        * **Timing Optimization**: Suggest best contact timing
        * **NLP Analysis**: Analyze conversation transcripts for insights
        * **Real-time Predictions**: <100ms P95 latency
        * **Enterprise Security**: Comprehensive audit logging and access controls
        
        ## Authentication
        
        All endpoints require valid API authentication. Contact your administrator
        for API keys and access permissions.
        
        ## Rate Limits
        
        * Standard tier: 1000 requests/minute
        * Premium tier: 10000 requests/minute
        * Enterprise tier: Unlimited
        """,
        version="1.0.0",
        contact={
            "name": "Sensei AI Team",
            "email": "<EMAIL>",
        },
        license_info={
            "name": "MIT",
            "url": "https://opensource.org/licenses/MIT",
        },
        docs_url="/docs" if config.debug else None,
        redoc_url="/redoc" if config.debug else None,
        openapi_url="/openapi.json" if config.debug else None,
    )
    
    # Setup middleware
    setup_middleware(app)
    
    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=config.api.cors_origins,
        allow_credentials=True,
        allow_methods=["GET", "POST"],
        allow_headers=["*"],
    )
    
    # Add compression middleware
    app.add_middleware(GZipMiddleware, minimum_size=1000)
    
    # Include routers
    app.include_router(
        health.router,
        prefix="/health",
        tags=["Health & Monitoring"]
    )
    
    app.include_router(
        models.router,
        prefix="/models",
        tags=["Model Management"]
    )
    
    app.include_router(
        conversion.router,
        prefix="/conversion",
        tags=["Conversion Scoring"]
    )
    
    app.include_router(
        channel_timing.router,
        prefix="/channel-timing",
        tags=["Channel & Timing Optimization"]
    )
    
    app.include_router(
        nlp.router,
        prefix="/nlp",
        tags=["NLP Analysis"]
    )
    
    # Global exception handler
    @app.exception_handler(Exception)
    async def global_exception_handler(request: Request, exc: Exception):
        """Handle unexpected exceptions."""
        correlation_id = getattr(request.state, 'correlation_id', str(uuid.uuid4()))
        
        logger.error(
            "Unhandled exception",
            correlation_id=correlation_id,
            path=request.url.path,
            method=request.method,
            error=str(exc),
            exc_info=True,
        )
        
        # Track error in metrics
        track_api_request(
            method=request.method,
            endpoint=request.url.path,
            status_code=500
        )
        
        return JSONResponse(
            status_code=500,
            content=ErrorResponse(
                error="Internal server error",
                message="An unexpected error occurred. Please try again later.",
                correlation_id=correlation_id,
            ).dict()
        )
    
    # HTTP exception handler
    @app.exception_handler(HTTPException)
    async def http_exception_handler(request: Request, exc: HTTPException):
        """Handle HTTP exceptions."""
        correlation_id = getattr(request.state, 'correlation_id', str(uuid.uuid4()))
        
        logger.warning(
            "HTTP exception",
            correlation_id=correlation_id,
            path=request.url.path,
            method=request.method,
            status_code=exc.status_code,
            detail=exc.detail,
        )
        
        # Track error in metrics
        track_api_request(
            method=request.method,
            endpoint=request.url.path,
            status_code=exc.status_code
        )
        
        return JSONResponse(
            status_code=exc.status_code,
            content=ErrorResponse(
                error=f"HTTP {exc.status_code}",
                message=exc.detail,
                correlation_id=correlation_id,
            ).dict()
        )
    
    # Startup event
    @app.on_event("startup")
    async def startup_event():
        """Initialize application on startup."""
        logger.info(
            "Sensei AI API starting up",
            version="1.0.0",
            environment=config.environment.value,
            debug=config.debug,
        )
        
        # Warm up models (optional)
        try:
            from ..models import get_model_registry
            registry = get_model_registry()
            
            # Pre-load commonly used models
            models_to_preload = ["conversion_predictor", "channel_timing_optimizer"]
            
            for model_name in models_to_preload:
                try:
                    model = registry.get_model(model_name, auto_load=True)
                    if model:
                        logger.info(f"Pre-loaded model: {model_name}")
                except Exception as e:
                    logger.warning(f"Failed to pre-load model {model_name}: {e}")
        
        except Exception as e:
            logger.warning(f"Model pre-loading failed: {e}")
        
        logger.info("Sensei AI API startup completed")
    
    # Shutdown event
    @app.on_event("shutdown")
    async def shutdown_event():
        """Clean up on application shutdown."""
        logger.info("Sensei AI API shutting down")
        
        # Clean up resources if needed
        try:
            # Close any open connections, clean up caches, etc.
            pass
        except Exception as e:
            logger.error(f"Error during shutdown: {e}")
        
        logger.info("Sensei AI API shutdown completed")
    
    # Root endpoint
    @app.get("/", include_in_schema=False)
    async def root():
        """Root endpoint with API information."""
        return {
            "name": "Sensei AI - B2B Sales Optimization Platform",
            "version": "1.0.0",
            "status": "operational",
            "documentation": "/docs",
            "health_check": "/health",
            "features": [
                "Conversion Scoring",
                "Channel Optimization", 
                "Timing Optimization",
                "NLP Analysis"
            ]
        }
    
    # Metrics endpoint (if monitoring enabled)
    if config.monitoring.enable_metrics:
        @app.get("/metrics", include_in_schema=False)
        async def metrics():
            """Prometheus metrics endpoint."""
            metrics_registry = get_metrics_registry()
            return JSONResponse(
                content=metrics_registry.get_metrics(),
                media_type="text/plain"
            )
    
    logger.info(
        "FastAPI application created",
        title=app.title,
        version=app.version,
        debug=config.debug,
    )
    
    return app


# Create app instance for uvicorn
app = create_app()
