"""
FastAPI application for Sensei AI.

Production-ready API server providing ML-powered insights for B2B sales optimization.
Features secure endpoints, comprehensive monitoring, and enterprise-grade performance.
"""

from .app import create_app
from .models import (
    ConversionRequest,
    ConversionResponse,
    ChannelTimingRequest,
    ChannelTimingResponse,
    NLPAnalysisRequest,
    NLPAnalysisResponse,
    HealthResponse,
    ModelInfoResponse,
)
from .middleware import setup_middleware
from .auth import get_current_user, require_permissions

__all__ = [
    "create_app",
    "ConversionRequest",
    "ConversionResponse", 
    "ChannelTimingRequest",
    "ChannelTimingResponse",
    "NLPAnalysisRequest",
    "NLPAnalysisResponse",
    "HealthResponse",
    "ModelInfoResponse",
    "setup_middleware",
    "get_current_user",
    "require_permissions",
]
