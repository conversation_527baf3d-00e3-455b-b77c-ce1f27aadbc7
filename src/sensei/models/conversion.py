"""
Conversion prediction model for Sensei AI.

Advanced ML model for predicting prospect conversion probability using
ensemble methods and sophisticated feature engineering.
"""

import numpy as np
import pandas as pd
from typing import Any, Dict, List, Optional, Tuple
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score
from sklearn.model_selection import cross_val_score
import lightgbm as lgb
import catboost as cb

from .base import BaseModel
from ..utils import get_logger

logger = get_logger(__name__)


class ConversionModel(BaseModel):
    """
    Advanced conversion prediction model.
    
    Uses ensemble methods to predict prospect conversion probability.
    Combines multiple algorithms for robust performance:
    - LightGBM for gradient boosting
    - CatBoost for categorical feature handling
    - Random Forest for feature importance
    - Logistic Regression for interpretability
    """
    
    def __init__(
        self,
        version: str = "1.0.0",
        ensemble_method: str = "voting",
        use_calibration: bool = True,
        **kwargs
    ):
        """
        Initialize conversion model.
        
        Args:
            version: Model version
            ensemble_method: Ensemble method ('voting', 'stacking', 'single')
            use_calibration: Whether to calibrate probabilities
            **kwargs: Additional arguments for BaseModel
        """
        super().__init__(
            model_name="conversion_predictor",
            model_type="conversion",
            version=version,
            **kwargs
        )
        
        self.ensemble_method = ensemble_method
        self.use_calibration = use_calibration
        self.base_models = {}
        self.ensemble_model = None
        self.calibrator = None
        
        # Target column for conversion prediction
        self.metadata.target_column = "converted"
    
    def _get_default_hyperparameters(self) -> Dict[str, Any]:
        """Get default hyperparameters for conversion model."""
        return {
            # LightGBM parameters
            'lgb_params': {
                'objective': 'binary',
                'metric': 'binary_logloss',
                'boosting_type': 'gbdt',
                'num_leaves': 31,
                'learning_rate': 0.05,
                'feature_fraction': 0.9,
                'bagging_fraction': 0.8,
                'bagging_freq': 5,
                'verbose': -1,
                'random_state': 42,
                'n_estimators': 100,
            },
            
            # CatBoost parameters
            'catboost_params': {
                'iterations': 100,
                'depth': 6,
                'learning_rate': 0.03,
                'loss_function': 'Logloss',
                'eval_metric': 'AUC',
                'random_seed': 42,
                'verbose': False,
            },
            
            # Random Forest parameters
            'rf_params': {
                'n_estimators': 100,
                'max_depth': 10,
                'min_samples_split': 5,
                'min_samples_leaf': 2,
                'random_state': 42,
                'n_jobs': -1,
            },
            
            # Logistic Regression parameters
            'lr_params': {
                'C': 1.0,
                'penalty': 'l2',
                'solver': 'liblinear',
                'random_state': 42,
                'max_iter': 1000,
            },
            
            # Ensemble parameters
            'ensemble_weights': [0.4, 0.3, 0.2, 0.1],  # LGB, CatBoost, RF, LR
        }
    
    def _create_model(self, hyperparameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create ensemble of base models.
        
        Args:
            hyperparameters: Model hyperparameters
            
        Returns:
            Dictionary of base models
        """
        models = {}
        
        # LightGBM
        models['lightgbm'] = lgb.LGBMClassifier(**hyperparameters['lgb_params'])
        
        # CatBoost
        models['catboost'] = cb.CatBoostClassifier(**hyperparameters['catboost_params'])
        
        # Random Forest
        models['random_forest'] = RandomForestClassifier(**hyperparameters['rf_params'])
        
        # Logistic Regression
        models['logistic_regression'] = LogisticRegression(**hyperparameters['lr_params'])
        
        logger.info(
            "Base models created",
            models=list(models.keys()),
            ensemble_method=self.ensemble_method,
        )
        
        return models
    
    def _validate_input(self, X: np.ndarray, y: Optional[np.ndarray] = None) -> None:
        """Validate input data for conversion model."""
        if X.shape[0] == 0:
            raise ValueError("Empty feature matrix")
        
        if X.shape[1] == 0:
            raise ValueError("No features provided")
        
        if np.isnan(X).any():
            raise ValueError("Feature matrix contains NaN values")
        
        if np.isinf(X).any():
            raise ValueError("Feature matrix contains infinite values")
        
        if y is not None:
            if len(y) != X.shape[0]:
                raise ValueError("Feature matrix and target vector length mismatch")
            
            # Check if binary classification
            unique_values = np.unique(y)
            if len(unique_values) != 2:
                raise ValueError(f"Expected binary target, got {len(unique_values)} unique values")
            
            if not set(unique_values).issubset({0, 1}):
                raise ValueError("Target values must be 0 and 1 for binary classification")
    
    def _train_model(
        self,
        X: np.ndarray,
        y: np.ndarray,
        validation_data: Optional[Tuple[np.ndarray, np.ndarray]] = None,
    ) -> Dict[str, Any]:
        """
        Train the ensemble conversion model.
        
        Args:
            X: Training features
            y: Training targets
            validation_data: Validation data
            
        Returns:
            Training results
        """
        logger.info("Training conversion model ensemble")
        
        training_results = {
            'base_model_scores': {},
            'ensemble_score': 0.0,
            'cross_validation_scores': {},
        }
        
        # Train base models
        for name, model in self.base_models.items():
            logger.info(f"Training {name}")
            
            try:
                # Train model
                if name == 'lightgbm' and validation_data is not None:
                    # Use early stopping for LightGBM
                    X_val, y_val = validation_data
                    model.fit(
                        X, y,
                        eval_set=[(X_val, y_val)],
                        callbacks=[lgb.early_stopping(stopping_rounds=10, verbose=False)]
                    )
                else:
                    model.fit(X, y)
                
                # Evaluate on training data
                train_pred = model.predict(X)
                train_score = accuracy_score(y, train_pred)
                training_results['base_model_scores'][name] = train_score
                
                # Cross-validation score
                cv_scores = cross_val_score(model, X, y, cv=5, scoring='accuracy')
                training_results['cross_validation_scores'][name] = {
                    'mean': cv_scores.mean(),
                    'std': cv_scores.std(),
                }
                
                logger.info(
                    f"{name} training completed",
                    train_accuracy=train_score,
                    cv_mean=cv_scores.mean(),
                    cv_std=cv_scores.std(),
                )
                
            except Exception as e:
                logger.error(f"Failed to train {name}: {e}")
                # Remove failed model
                del self.base_models[name]
        
        # Create ensemble
        if self.ensemble_method == "voting":
            self._create_voting_ensemble()
        elif self.ensemble_method == "stacking":
            self._create_stacking_ensemble(X, y)
        elif self.ensemble_method == "single":
            # Use best performing model
            best_model_name = max(
                training_results['base_model_scores'],
                key=training_results['base_model_scores'].get
            )
            self.ensemble_model = self.base_models[best_model_name]
            logger.info(f"Using single model: {best_model_name}")
        
        # Evaluate ensemble
        if self.ensemble_method != "single":
            ensemble_pred = self._predict_ensemble(X)
            training_results['ensemble_score'] = accuracy_score(y, ensemble_pred)
        else:
            training_results['ensemble_score'] = max(training_results['base_model_scores'].values())
        
        # Calibrate probabilities if requested
        if self.use_calibration:
            self._calibrate_probabilities(X, y)
        
        logger.info(
            "Conversion model training completed",
            ensemble_score=training_results['ensemble_score'],
            base_models=len(self.base_models),
        )
        
        return training_results
    
    def _create_voting_ensemble(self) -> None:
        """Create voting ensemble from base models."""
        from sklearn.ensemble import VotingClassifier
        
        estimators = [(name, model) for name, model in self.base_models.items()]
        self.ensemble_model = VotingClassifier(
            estimators=estimators,
            voting='soft',  # Use probability voting
        )
        
        logger.info("Voting ensemble created", base_models=len(estimators))
    
    def _create_stacking_ensemble(self, X: np.ndarray, y: np.ndarray) -> None:
        """Create stacking ensemble from base models."""
        from sklearn.ensemble import StackingClassifier
        from sklearn.linear_model import LogisticRegression
        
        estimators = [(name, model) for name, model in self.base_models.items()]
        meta_classifier = LogisticRegression(random_state=42)
        
        self.ensemble_model = StackingClassifier(
            estimators=estimators,
            final_estimator=meta_classifier,
            cv=5,
        )
        
        # Fit the stacking ensemble
        self.ensemble_model.fit(X, y)
        
        logger.info("Stacking ensemble created", base_models=len(estimators))
    
    def _calibrate_probabilities(self, X: np.ndarray, y: np.ndarray) -> None:
        """Calibrate model probabilities using Platt scaling."""
        from sklearn.calibration import CalibratedClassifierCV
        
        if self.ensemble_model is not None:
            self.calibrator = CalibratedClassifierCV(
                self.ensemble_model,
                method='sigmoid',
                cv=3,
            )
            self.calibrator.fit(X, y)
            
            logger.info("Probability calibration completed")
    
    def _predict_ensemble(self, X: np.ndarray) -> np.ndarray:
        """Make ensemble predictions."""
        if self.ensemble_method == "voting" and self.ensemble_model is not None:
            return self.ensemble_model.predict(X)
        elif self.ensemble_method == "stacking" and self.ensemble_model is not None:
            return self.ensemble_model.predict(X)
        elif self.ensemble_method == "single" and self.ensemble_model is not None:
            return self.ensemble_model.predict(X)
        else:
            # Weighted average of base model predictions
            predictions = []
            weights = self.metadata.hyperparameters.get('ensemble_weights', [])
            
            if len(weights) != len(self.base_models):
                weights = [1.0 / len(self.base_models)] * len(self.base_models)
            
            for i, (name, model) in enumerate(self.base_models.items()):
                pred_proba = model.predict_proba(X)[:, 1]  # Probability of positive class
                predictions.append(pred_proba * weights[i])
            
            # Average weighted predictions
            ensemble_proba = np.sum(predictions, axis=0)
            return (ensemble_proba > 0.5).astype(int)
    
    def _predict(self, X: np.ndarray) -> np.ndarray:
        """Make predictions with the conversion model."""
        if self.calibrator is not None:
            return self.calibrator.predict(X)
        else:
            return self._predict_ensemble(X)
    
    def predict_proba(self, X: np.ndarray) -> np.ndarray:
        """Predict conversion probabilities."""
        if not self.is_trained:
            raise ValueError("Model must be trained before making predictions")
        
        self._validate_input(X)
        
        if self.calibrator is not None:
            return self.calibrator.predict_proba(X)
        elif self.ensemble_model is not None and hasattr(self.ensemble_model, 'predict_proba'):
            return self.ensemble_model.predict_proba(X)
        else:
            # Manual ensemble probability prediction
            predictions = []
            weights = self.metadata.hyperparameters.get('ensemble_weights', [])
            
            if len(weights) != len(self.base_models):
                weights = [1.0 / len(self.base_models)] * len(self.base_models)
            
            for i, (name, model) in enumerate(self.base_models.items()):
                pred_proba = model.predict_proba(X)
                predictions.append(pred_proba * weights[i])
            
            # Average weighted predictions
            ensemble_proba = np.sum(predictions, axis=0)
            return ensemble_proba
    
    def get_conversion_insights(self, X: np.ndarray) -> Dict[str, Any]:
        """
        Get detailed conversion insights for prospects.
        
        Args:
            X: Feature matrix
            
        Returns:
            Dictionary with conversion insights
        """
        if not self.is_trained:
            raise ValueError("Model must be trained to get insights")
        
        # Get predictions and probabilities
        predictions = self.predict(X)
        probabilities = self.predict_proba(X)
        
        # Get feature importance
        feature_importance = self.get_feature_importance()
        
        # Calculate confidence scores
        confidence_scores = np.max(probabilities, axis=1)
        
        insights = {
            'predictions': predictions,
            'conversion_probabilities': probabilities[:, 1] if probabilities.shape[1] > 1 else probabilities,
            'confidence_scores': confidence_scores,
            'feature_importance': feature_importance,
            'high_confidence_predictions': (confidence_scores > 0.8).sum(),
            'predicted_conversions': predictions.sum(),
            'average_conversion_probability': np.mean(probabilities[:, 1] if probabilities.shape[1] > 1 else probabilities),
        }
        
        return insights
    
    def get_feature_importance(self) -> Optional[Dict[str, float]]:
        """Get aggregated feature importance from ensemble."""
        if not self.is_trained:
            return None
        
        importance_scores = {}
        
        # Aggregate importance from base models that support it
        for name, model in self.base_models.items():
            if hasattr(model, 'feature_importances_'):
                model_importance = model.feature_importances_
                
                for i, importance in enumerate(model_importance):
                    feature_name = (
                        self.metadata.feature_names[i] 
                        if i < len(self.metadata.feature_names) 
                        else f"feature_{i}"
                    )
                    
                    if feature_name not in importance_scores:
                        importance_scores[feature_name] = 0.0
                    
                    importance_scores[feature_name] += importance / len(self.base_models)
        
        # Sort by importance
        return dict(sorted(importance_scores.items(), key=lambda x: x[1], reverse=True))
    
    def evaluate_model(
        self,
        X_test: np.ndarray,
        y_test: np.ndarray,
    ) -> Dict[str, float]:
        """
        Evaluate model performance on test data.
        
        Args:
            X_test: Test features
            y_test: Test targets
            
        Returns:
            Performance metrics
        """
        if not self.is_trained:
            raise ValueError("Model must be trained before evaluation")
        
        # Make predictions
        y_pred = self.predict(X_test)
        y_proba = self.predict_proba(X_test)
        
        # Calculate metrics
        metrics = {
            'accuracy': accuracy_score(y_test, y_pred),
            'precision': precision_score(y_test, y_pred, average='binary'),
            'recall': recall_score(y_test, y_pred, average='binary'),
            'f1_score': f1_score(y_test, y_pred, average='binary'),
        }
        
        # Add AUC if probabilities are available
        if y_proba.shape[1] > 1:
            metrics['roc_auc'] = roc_auc_score(y_test, y_proba[:, 1])
        
        # Update model metadata
        self.update_performance_metrics(metrics)
        
        logger.info(
            "Model evaluation completed",
            model_name=self.model_name,
            **metrics,
        )
        
        return metrics
