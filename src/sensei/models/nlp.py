"""
NLP model for conversation analysis in Sensei AI.

Advanced NLP model for analyzing sales conversation transcripts from Modjo,
extracting insights, sentiment, and conversation patterns.
"""

import numpy as np
import pandas as pd
from typing import Any, Dict, List, Optional, Tuple, Union
from sklearn.cluster import KMeans
from sklearn.metrics import silhouette_score, adjusted_rand_score
from sklearn.decomposition import LatentDirichletAllocation
import umap
import hdbscan

from .base import BaseModel
from ..utils import get_logger

logger = get_logger(__name__)


class NLPModel(BaseModel):
    """
    NLP model for conversation analysis.
    
    Analyzes sales conversation transcripts to extract:
    - Conversation topics and themes
    - Sentiment and emotional patterns
    - Speaking patterns and engagement metrics
    - Success indicators and quality scores
    - Conversation clustering and segmentation
    """
    
    def __init__(
        self,
        version: str = "1.0.0",
        analysis_type: str = "clustering",
        embedding_model: str = "sentence_transformers",
        **kwargs
    ):
        """
        Initialize NLP model.
        
        Args:
            version: Model version
            analysis_type: Type of analysis ('clustering', 'topic_modeling', 'sentiment')
            embedding_model: Embedding model to use
            **kwargs: Additional arguments for BaseModel
        """
        super().__init__(
            model_name="conversation_analyzer",
            model_type="nlp",
            version=version,
            **kwargs
        )
        
        self.analysis_type = analysis_type
        self.embedding_model = embedding_model
        
        # Model components
        self.clustering_model = None
        self.topic_model = None
        self.dimensionality_reducer = None
        self.embeddings_model = None
        
        # Analysis results
        self.cluster_labels_ = None
        self.topic_distributions_ = None
        self.embeddings_ = None
        
        # Target column for NLP analysis
        self.metadata.target_column = "conversation_quality"
    
    def _get_default_hyperparameters(self) -> Dict[str, Any]:
        """Get default hyperparameters for NLP model."""
        return {
            # Clustering parameters
            'clustering': {
                'algorithm': 'hdbscan',  # hdbscan, kmeans
                'min_cluster_size': 5,
                'min_samples': 3,
                'cluster_selection_epsilon': 0.1,
                'n_clusters': 8,  # for kmeans
            },
            
            # Topic modeling parameters
            'topic_modeling': {
                'n_topics': 10,
                'max_iter': 100,
                'learning_method': 'batch',
                'random_state': 42,
            },
            
            # Dimensionality reduction parameters
            'umap': {
                'n_components': 2,
                'n_neighbors': 15,
                'min_dist': 0.1,
                'metric': 'cosine',
                'random_state': 42,
            },
            
            # Embedding parameters
            'embeddings': {
                'model_name': 'all-MiniLM-L6-v2',
                'max_seq_length': 512,
            },
        }
    
    def _create_model(self, hyperparameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create NLP model components.
        
        Args:
            hyperparameters: Model hyperparameters
            
        Returns:
            Dictionary of model components
        """
        models = {}
        
        # Create embedding model
        if self.embedding_model == "sentence_transformers":
            from sentence_transformers import SentenceTransformer
            models['embeddings'] = SentenceTransformer(
                hyperparameters['embeddings']['model_name']
            )
        
        # Create clustering model
        clustering_params = hyperparameters['clustering']
        if clustering_params['algorithm'] == 'hdbscan':
            models['clustering'] = hdbscan.HDBSCAN(
                min_cluster_size=clustering_params['min_cluster_size'],
                min_samples=clustering_params['min_samples'],
                cluster_selection_epsilon=clustering_params['cluster_selection_epsilon'],
            )
        elif clustering_params['algorithm'] == 'kmeans':
            models['clustering'] = KMeans(
                n_clusters=clustering_params['n_clusters'],
                random_state=42,
            )
        
        # Create topic model
        models['topic_model'] = LatentDirichletAllocation(
            **hyperparameters['topic_modeling']
        )
        
        # Create dimensionality reducer
        models['umap'] = umap.UMAP(**hyperparameters['umap'])
        
        logger.info(
            "NLP model components created",
            analysis_type=self.analysis_type,
            embedding_model=self.embedding_model,
        )
        
        return models
    
    def _validate_input(self, X: np.ndarray, y: Optional[np.ndarray] = None) -> None:
        """Validate input data for NLP model."""
        if X.shape[0] == 0:
            raise ValueError("Empty feature matrix")
        
        if X.shape[1] == 0:
            raise ValueError("No features provided")
        
        # Check for text data if this is the first step
        if hasattr(self, '_raw_texts') and len(self._raw_texts) != X.shape[0]:
            raise ValueError("Text data and feature matrix length mismatch")
        
        if np.isnan(X).any():
            logger.warning("Feature matrix contains NaN values - will be handled")
        
        if np.isinf(X).any():
            logger.warning("Feature matrix contains infinite values - will be handled")
    
    def _train_model(
        self,
        X: np.ndarray,
        y: np.ndarray,
        validation_data: Optional[Tuple[np.ndarray, np.ndarray]] = None,
    ) -> Dict[str, Any]:
        """
        Train the NLP model.
        
        Args:
            X: Training features (can include text embeddings)
            y: Training targets (conversation quality scores)
            validation_data: Validation data
            
        Returns:
            Training results
        """
        logger.info("Training NLP model")
        
        training_results = {
            'n_samples': len(X),
            'n_features': X.shape[1],
            'analysis_results': {},
        }
        
        # Handle NaN and infinite values
        X_clean = np.nan_to_num(X, nan=0.0, posinf=1e6, neginf=-1e6)
        
        if self.analysis_type == "clustering":
            training_results.update(self._train_clustering(X_clean))
        elif self.analysis_type == "topic_modeling":
            training_results.update(self._train_topic_modeling(X_clean))
        elif self.analysis_type == "sentiment":
            training_results.update(self._train_sentiment_analysis(X_clean, y))
        else:
            # Combined analysis
            training_results.update(self._train_combined_analysis(X_clean, y))
        
        logger.info(
            "NLP model training completed",
            analysis_type=self.analysis_type,
            **{k: v for k, v in training_results.items() if isinstance(v, (int, float))}
        )
        
        return training_results
    
    def _train_clustering(self, X: np.ndarray) -> Dict[str, Any]:
        """Train conversation clustering model."""
        # Dimensionality reduction
        self.dimensionality_reducer = self.model['umap']
        X_reduced = self.dimensionality_reducer.fit_transform(X)
        
        # Clustering
        self.clustering_model = self.model['clustering']
        cluster_labels = self.clustering_model.fit_predict(X_reduced)
        
        self.cluster_labels_ = cluster_labels
        
        # Calculate clustering metrics
        n_clusters = len(set(cluster_labels)) - (1 if -1 in cluster_labels else 0)
        n_noise = list(cluster_labels).count(-1)
        
        results = {
            'n_clusters': n_clusters,
            'n_noise_points': n_noise,
            'noise_ratio': n_noise / len(cluster_labels),
        }
        
        # Silhouette score (if we have enough clusters)
        if n_clusters > 1 and n_noise < len(cluster_labels) - 1:
            # Remove noise points for silhouette calculation
            mask = cluster_labels != -1
            if mask.sum() > 1:
                silhouette = silhouette_score(X_reduced[mask], cluster_labels[mask])
                results['silhouette_score'] = silhouette
        
        logger.info(
            "Clustering training completed",
            n_clusters=n_clusters,
            noise_ratio=results['noise_ratio'],
        )
        
        return results
    
    def _train_topic_modeling(self, X: np.ndarray) -> Dict[str, Any]:
        """Train topic modeling."""
        # Assume X contains TF-IDF features or similar
        self.topic_model = self.model['topic_model']
        
        # Fit topic model
        self.topic_model.fit(X)
        
        # Get topic distributions
        self.topic_distributions_ = self.topic_model.transform(X)
        
        # Calculate perplexity
        perplexity = self.topic_model.perplexity(X)
        
        results = {
            'n_topics': self.topic_model.n_components,
            'perplexity': perplexity,
            'log_likelihood': self.topic_model.score(X),
        }
        
        logger.info(
            "Topic modeling training completed",
            n_topics=results['n_topics'],
            perplexity=perplexity,
        )
        
        return results
    
    def _train_sentiment_analysis(self, X: np.ndarray, y: np.ndarray) -> Dict[str, Any]:
        """Train sentiment analysis model."""
        # For sentiment analysis, we can use the conversation quality scores
        # as a proxy for sentiment/success
        
        from sklearn.ensemble import RandomForestRegressor
        from sklearn.metrics import mean_squared_error, r2_score
        
        # Create sentiment model
        sentiment_model = RandomForestRegressor(
            n_estimators=100,
            random_state=42,
        )
        
        # Train model
        sentiment_model.fit(X, y)
        
        # Predictions
        y_pred = sentiment_model.predict(X)
        
        # Metrics
        mse = mean_squared_error(y, y_pred)
        r2 = r2_score(y, y_pred)
        
        # Store model
        self.model['sentiment'] = sentiment_model
        
        results = {
            'mse': mse,
            'r2_score': r2,
            'feature_importance_available': True,
        }
        
        logger.info(
            "Sentiment analysis training completed",
            mse=mse,
            r2_score=r2,
        )
        
        return results
    
    def _train_combined_analysis(self, X: np.ndarray, y: np.ndarray) -> Dict[str, Any]:
        """Train combined NLP analysis."""
        results = {}
        
        # Run clustering
        clustering_results = self._train_clustering(X)
        results['clustering'] = clustering_results
        
        # Run topic modeling (if we have appropriate features)
        if X.shape[1] >= 10:  # Minimum features for topic modeling
            topic_results = self._train_topic_modeling(X)
            results['topic_modeling'] = topic_results
        
        # Run sentiment analysis
        sentiment_results = self._train_sentiment_analysis(X, y)
        results['sentiment'] = sentiment_results
        
        return results
    
    def _predict(self, X: np.ndarray) -> np.ndarray:
        """Make predictions with the NLP model."""
        if self.analysis_type == "clustering":
            return self._predict_clusters(X)
        elif self.analysis_type == "topic_modeling":
            return self._predict_topics(X)
        elif self.analysis_type == "sentiment":
            return self._predict_sentiment(X)
        else:
            # Combined prediction - return sentiment scores
            return self._predict_sentiment(X)
    
    def _predict_clusters(self, X: np.ndarray) -> np.ndarray:
        """Predict cluster assignments."""
        if self.dimensionality_reducer is None or self.clustering_model is None:
            raise ValueError("Clustering model not trained")
        
        # Transform to reduced space
        X_reduced = self.dimensionality_reducer.transform(X)
        
        # Predict clusters
        if hasattr(self.clustering_model, 'predict'):
            return self.clustering_model.predict(X_reduced)
        else:
            # For HDBSCAN, use approximate prediction
            labels, _ = hdbscan.approximate_predict(self.clustering_model, X_reduced)
            return labels
    
    def _predict_topics(self, X: np.ndarray) -> np.ndarray:
        """Predict topic distributions."""
        if self.topic_model is None:
            raise ValueError("Topic model not trained")
        
        topic_distributions = self.topic_model.transform(X)
        # Return dominant topic for each document
        return np.argmax(topic_distributions, axis=1)
    
    def _predict_sentiment(self, X: np.ndarray) -> np.ndarray:
        """Predict sentiment/quality scores."""
        if 'sentiment' not in self.model:
            raise ValueError("Sentiment model not trained")
        
        return self.model['sentiment'].predict(X)
    
    def analyze_conversations(
        self,
        X: np.ndarray,
        conversation_texts: Optional[List[str]] = None,
    ) -> Dict[str, Any]:
        """
        Analyze conversations and extract insights.
        
        Args:
            X: Feature matrix
            conversation_texts: Original conversation texts
            
        Returns:
            Analysis results dictionary
        """
        if not self.is_trained:
            raise ValueError("Model must be trained to analyze conversations")
        
        analysis = {
            'predictions': self.predict(X),
            'insights': {},
        }
        
        if self.analysis_type == "clustering" or "clustering" in self.model:
            cluster_insights = self._get_cluster_insights(X)
            analysis['insights']['clustering'] = cluster_insights
        
        if self.analysis_type == "topic_modeling" or "topic_model" in self.model:
            topic_insights = self._get_topic_insights(X)
            analysis['insights']['topics'] = topic_insights
        
        if self.analysis_type == "sentiment" or "sentiment" in self.model:
            sentiment_insights = self._get_sentiment_insights(X)
            analysis['insights']['sentiment'] = sentiment_insights
        
        return analysis
    
    def _get_cluster_insights(self, X: np.ndarray) -> Dict[str, Any]:
        """Get clustering insights."""
        if self.cluster_labels_ is None:
            return {}
        
        cluster_predictions = self._predict_clusters(X)
        unique_clusters, counts = np.unique(cluster_predictions, return_counts=True)
        
        insights = {
            'cluster_distribution': dict(zip(unique_clusters.tolist(), counts.tolist())),
            'dominant_cluster': unique_clusters[np.argmax(counts)],
            'n_clusters_found': len(unique_clusters),
        }
        
        return insights
    
    def _get_topic_insights(self, X: np.ndarray) -> Dict[str, Any]:
        """Get topic modeling insights."""
        if self.topic_model is None:
            return {}
        
        topic_distributions = self.topic_model.transform(X)
        dominant_topics = np.argmax(topic_distributions, axis=1)
        
        unique_topics, counts = np.unique(dominant_topics, return_counts=True)
        
        insights = {
            'topic_distribution': dict(zip(unique_topics.tolist(), counts.tolist())),
            'dominant_topic': unique_topics[np.argmax(counts)],
            'average_topic_confidence': np.mean(np.max(topic_distributions, axis=1)),
        }
        
        return insights
    
    def _get_sentiment_insights(self, X: np.ndarray) -> Dict[str, Any]:
        """Get sentiment analysis insights."""
        if 'sentiment' not in self.model:
            return {}
        
        sentiment_scores = self._predict_sentiment(X)
        
        insights = {
            'average_sentiment': np.mean(sentiment_scores),
            'sentiment_std': np.std(sentiment_scores),
            'positive_conversations': (sentiment_scores > 0.6).sum(),
            'negative_conversations': (sentiment_scores < 0.4).sum(),
            'sentiment_distribution': {
                'high': (sentiment_scores > 0.7).sum(),
                'medium': ((sentiment_scores >= 0.4) & (sentiment_scores <= 0.7)).sum(),
                'low': (sentiment_scores < 0.4).sum(),
            }
        }
        
        return insights
    
    def get_conversation_topics(self, n_words: int = 10) -> List[List[str]]:
        """
        Get top words for each topic.
        
        Args:
            n_words: Number of top words per topic
            
        Returns:
            List of top words for each topic
        """
        if self.topic_model is None:
            return []
        
        # This would require access to the original vocabulary
        # For now, return placeholder
        n_topics = self.topic_model.n_components
        return [
            [f"topic_{i}_word_{j}" for j in range(n_words)]
            for i in range(n_topics)
        ]
    
    def evaluate_model(
        self,
        X_test: np.ndarray,
        y_test: Optional[np.ndarray] = None,
    ) -> Dict[str, Any]:
        """
        Evaluate NLP model performance.
        
        Args:
            X_test: Test features
            y_test: Test targets (optional)
            
        Returns:
            Performance metrics
        """
        if not self.is_trained:
            raise ValueError("Model must be trained before evaluation")
        
        metrics = {}
        
        if self.analysis_type == "sentiment" and y_test is not None:
            from sklearn.metrics import mean_squared_error, r2_score
            
            y_pred = self._predict_sentiment(X_test)
            metrics['mse'] = mean_squared_error(y_test, y_pred)
            metrics['r2_score'] = r2_score(y_test, y_pred)
        
        elif self.analysis_type == "clustering":
            cluster_pred = self._predict_clusters(X_test)
            unique_clusters = len(set(cluster_pred))
            metrics['n_clusters_predicted'] = unique_clusters
            
            if unique_clusters > 1:
                X_reduced = self.dimensionality_reducer.transform(X_test)
                mask = cluster_pred != -1
                if mask.sum() > 1:
                    silhouette = silhouette_score(X_reduced[mask], cluster_pred[mask])
                    metrics['silhouette_score'] = silhouette
        
        # Update model metadata
        self.update_performance_metrics(metrics)
        
        logger.info(
            "NLP model evaluation completed",
            model_name=self.model_name,
            **metrics,
        )
        
        return metrics
