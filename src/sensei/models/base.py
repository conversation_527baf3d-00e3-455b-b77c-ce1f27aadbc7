"""
Base model class for Sensei AI.

Provides common interface and functionality for all ML models.
"""

import pickle
import joblib
from abc import ABC, abstractmethod
from dataclasses import dataclass, asdict
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union
import numpy as np
import pandas as pd

from ..utils import get_logger, get_config
from ..utils.monitoring import track_performance, update_model_accuracy

logger = get_logger(__name__)


@dataclass
class ModelMetadata:
    """Metadata for ML models."""
    model_name: str
    model_type: str
    version: str
    created_at: datetime
    trained_at: Optional[datetime] = None
    training_samples: int = 0
    feature_count: int = 0
    performance_metrics: Dict[str, float] = None
    hyperparameters: Dict[str, Any] = None
    feature_names: List[str] = None
    target_column: str = ""
    data_quality_score: float = 0.0
    model_size_mb: float = 0.0
    training_duration_seconds: float = 0.0
    
    def __post_init__(self):
        """Initialize default values."""
        if self.performance_metrics is None:
            self.performance_metrics = {}
        if self.hyperparameters is None:
            self.hyperparameters = {}
        if self.feature_names is None:
            self.feature_names = []
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        data = asdict(self)
        # Convert datetime objects to strings
        if self.created_at:
            data['created_at'] = self.created_at.isoformat()
        if self.trained_at:
            data['trained_at'] = self.trained_at.isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ModelMetadata':
        """Create from dictionary."""
        # Convert string dates back to datetime
        if 'created_at' in data and isinstance(data['created_at'], str):
            data['created_at'] = datetime.fromisoformat(data['created_at'])
        if 'trained_at' in data and isinstance(data['trained_at'], str):
            data['trained_at'] = datetime.fromisoformat(data['trained_at'])
        return cls(**data)


class BaseModel(ABC):
    """
    Abstract base class for all ML models.
    
    Provides common interface and functionality including:
    - Model training and prediction
    - Serialization and versioning
    - Performance monitoring
    - Metadata management
    """
    
    def __init__(
        self,
        model_name: str,
        model_type: str,
        version: str = "1.0.0",
        model_dir: Optional[Path] = None,
    ):
        """
        Initialize base model.
        
        Args:
            model_name: Name of the model
            model_type: Type of model (conversion, channel_timing, nlp)
            version: Model version
            model_dir: Directory to save models
        """
        self.config = get_config()
        
        # Model identification
        self.model_name = model_name
        self.model_type = model_type
        self.version = version
        
        # Model storage
        self.model_dir = model_dir or self.config.models.model_dir
        self.model_dir.mkdir(parents=True, exist_ok=True)
        
        # Model state
        self.model = None
        self.is_trained = False
        self.metadata = ModelMetadata(
            model_name=model_name,
            model_type=model_type,
            version=version,
            created_at=datetime.now(),
        )
        
        logger.info(
            "Model initialized",
            model_name=model_name,
            model_type=model_type,
            version=version,
        )
    
    @abstractmethod
    def _create_model(self, hyperparameters: Dict[str, Any]) -> Any:
        """
        Create the underlying ML model.
        
        Args:
            hyperparameters: Model hyperparameters
            
        Returns:
            Initialized model instance
        """
        pass
    
    @abstractmethod
    def _get_default_hyperparameters(self) -> Dict[str, Any]:
        """
        Get default hyperparameters for the model.
        
        Returns:
            Default hyperparameters dictionary
        """
        pass
    
    @abstractmethod
    def _validate_input(self, X: np.ndarray, y: Optional[np.ndarray] = None) -> None:
        """
        Validate input data.
        
        Args:
            X: Feature matrix
            y: Target vector (optional)
            
        Raises:
            ValueError: If input is invalid
        """
        pass
    
    def train(
        self,
        X: np.ndarray,
        y: np.ndarray,
        hyperparameters: Optional[Dict[str, Any]] = None,
        feature_names: Optional[List[str]] = None,
        validation_data: Optional[Tuple[np.ndarray, np.ndarray]] = None,
    ) -> Dict[str, Any]:
        """
        Train the model.
        
        Args:
            X: Training features
            y: Training targets
            hyperparameters: Model hyperparameters
            feature_names: Names of features
            validation_data: Validation data tuple (X_val, y_val)
            
        Returns:
            Training results dictionary
        """
        with track_performance(
            "model_training",
            labels={"model_name": self.model_name, "model_type": self.model_type}
        ):
            start_time = datetime.now()
            
            logger.info(
                "Starting model training",
                model_name=self.model_name,
                training_samples=len(X),
                features=X.shape[1],
            )
            
            # Validate input
            self._validate_input(X, y)
            
            # Use default hyperparameters if not provided
            if hyperparameters is None:
                hyperparameters = self._get_default_hyperparameters()
            
            # Create and train model
            self.model = self._create_model(hyperparameters)
            training_results = self._train_model(X, y, validation_data)
            
            # Update metadata
            training_duration = (datetime.now() - start_time).total_seconds()
            self.metadata.trained_at = datetime.now()
            self.metadata.training_samples = len(X)
            self.metadata.feature_count = X.shape[1]
            self.metadata.hyperparameters = hyperparameters
            self.metadata.feature_names = feature_names or []
            self.metadata.training_duration_seconds = training_duration
            
            # Mark as trained
            self.is_trained = True
            
            logger.info(
                "Model training completed",
                model_name=self.model_name,
                duration_seconds=training_duration,
                **training_results,
            )
            
            return training_results
    
    @abstractmethod
    def _train_model(
        self,
        X: np.ndarray,
        y: np.ndarray,
        validation_data: Optional[Tuple[np.ndarray, np.ndarray]] = None,
    ) -> Dict[str, Any]:
        """
        Train the underlying model.
        
        Args:
            X: Training features
            y: Training targets
            validation_data: Validation data
            
        Returns:
            Training results
        """
        pass
    
    def predict(self, X: np.ndarray) -> np.ndarray:
        """
        Make predictions.
        
        Args:
            X: Features for prediction
            
        Returns:
            Predictions
        """
        if not self.is_trained:
            raise ValueError("Model must be trained before making predictions")
        
        with track_performance(
            "model_prediction",
            labels={"model_name": self.model_name, "model_type": self.model_type}
        ):
            self._validate_input(X)
            predictions = self._predict(X)
            
            logger.debug(
                "Predictions made",
                model_name=self.model_name,
                samples=len(X),
            )
            
            return predictions
    
    @abstractmethod
    def _predict(self, X: np.ndarray) -> np.ndarray:
        """
        Make predictions with the underlying model.
        
        Args:
            X: Features for prediction
            
        Returns:
            Predictions
        """
        pass
    
    def predict_proba(self, X: np.ndarray) -> np.ndarray:
        """
        Predict class probabilities (for classification models).
        
        Args:
            X: Features for prediction
            
        Returns:
            Class probabilities
        """
        if not hasattr(self.model, 'predict_proba'):
            raise NotImplementedError("Model does not support probability prediction")
        
        if not self.is_trained:
            raise ValueError("Model must be trained before making predictions")
        
        with track_performance(
            "model_prediction_proba",
            labels={"model_name": self.model_name, "model_type": self.model_type}
        ):
            self._validate_input(X)
            probabilities = self.model.predict_proba(X)
            
            logger.debug(
                "Probabilities predicted",
                model_name=self.model_name,
                samples=len(X),
            )
            
            return probabilities
    
    def save(self, file_path: Optional[Path] = None) -> Path:
        """
        Save the model to disk.
        
        Args:
            file_path: Path to save the model
            
        Returns:
            Path where model was saved
        """
        if not self.is_trained:
            raise ValueError("Cannot save untrained model")
        
        if file_path is None:
            file_path = self.model_dir / f"{self.model_name}_v{self.version}.pkl"
        
        # Calculate model size
        model_data = {
            'model': self.model,
            'metadata': self.metadata,
            'is_trained': self.is_trained,
        }
        
        # Save model
        with open(file_path, 'wb') as f:
            joblib.dump(model_data, f)
        
        # Update metadata with file size
        self.metadata.model_size_mb = file_path.stat().st_size / (1024 * 1024)
        
        logger.info(
            "Model saved",
            model_name=self.model_name,
            file_path=str(file_path),
            size_mb=self.metadata.model_size_mb,
        )
        
        return file_path
    
    def load(self, file_path: Path) -> None:
        """
        Load model from disk.
        
        Args:
            file_path: Path to load the model from
        """
        if not file_path.exists():
            raise FileNotFoundError(f"Model file not found: {file_path}")
        
        try:
            with open(file_path, 'rb') as f:
                model_data = joblib.load(f)
            
            self.model = model_data['model']
            self.metadata = model_data['metadata']
            self.is_trained = model_data['is_trained']
            
            logger.info(
                "Model loaded",
                model_name=self.model_name,
                file_path=str(file_path),
            )
            
        except Exception as e:
            logger.error(
                "Failed to load model",
                model_name=self.model_name,
                file_path=str(file_path),
                error=str(e),
            )
            raise
    
    def get_feature_importance(self) -> Optional[Dict[str, float]]:
        """
        Get feature importance scores.
        
        Returns:
            Dictionary of feature names and importance scores
        """
        if not self.is_trained:
            return None
        
        if not hasattr(self.model, 'feature_importances_'):
            return None
        
        importance_scores = self.model.feature_importances_
        feature_names = self.metadata.feature_names
        
        if len(feature_names) != len(importance_scores):
            # Generate generic names if mismatch
            feature_names = [f"feature_{i}" for i in range(len(importance_scores))]
        
        return dict(zip(feature_names, importance_scores))
    
    def update_performance_metrics(self, metrics: Dict[str, float]) -> None:
        """
        Update model performance metrics.
        
        Args:
            metrics: Performance metrics dictionary
        """
        self.metadata.performance_metrics.update(metrics)
        
        # Update monitoring metrics
        if 'accuracy' in metrics:
            update_model_accuracy(
                self.model_name,
                self.version,
                metrics['accuracy']
            )
        
        logger.debug(
            "Performance metrics updated",
            model_name=self.model_name,
            metrics=metrics,
        )
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        Get comprehensive model information.
        
        Returns:
            Model information dictionary
        """
        return {
            'model_name': self.model_name,
            'model_type': self.model_type,
            'version': self.version,
            'is_trained': self.is_trained,
            'metadata': self.metadata.to_dict(),
            'feature_importance': self.get_feature_importance(),
        }
