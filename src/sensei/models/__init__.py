"""
Machine Learning models for Sensei AI.

This package provides advanced ML models for:
- Conversion scoring and prospect prioritization
- Channel and timing optimization for sales outreach
- NLP analysis of sales conversation transcripts

Features:
- State-of-the-art ensemble methods
- Automated hyperparameter optimization
- Model versioning and management
- Performance monitoring and drift detection
"""

from .base import BaseModel, ModelMetadata
from .conversion import ConversionModel
from .channel import ChannelTimingModel
from .nlp import NLPModel
from .registry import ModelRegistry, get_model_registry
from .optimization import HyperparameterOptimizer
from .evaluation import ModelEvaluator, EvaluationMetrics

__all__ = [
    "BaseModel",
    "ModelMetadata",
    "ConversionModel",
    "ChannelTimingModel",
    "NLPModel",
    "ModelRegistry",
    "get_model_registry",
    "HyperparameterOptimizer",
    "ModelEvaluator",
    "EvaluationMetrics",
]
