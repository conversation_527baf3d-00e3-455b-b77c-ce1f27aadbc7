"""
Channel and timing optimization model for Sensei AI.

Advanced ML model for predicting optimal communication channels and timing
for sales outreach based on historical interaction patterns.
"""

import numpy as np
import pandas as pd
from typing import Any, Dict, List, Optional, Tuple
from sklearn.ensemble import RandomForestClassifier
from sklearn.multiclass import OneVsRestClassifier
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
from sklearn.preprocessing import LabelEncoder
import lightgbm as lgb
import catboost as cb

from .base import BaseModel
from ..utils import get_logger

logger = get_logger(__name__)


class ChannelTimingModel(BaseModel):
    """
    Channel and timing optimization model.
    
    Predicts optimal communication channels and timing for prospects
    based on historical interaction patterns and success rates.
    
    Supports multi-class prediction for:
    - Communication channels (email, call, meeting, etc.)
    - Optimal timing (time of day, day of week)
    - Success probability for each channel/timing combination
    """
    
    def __init__(
        self,
        version: str = "1.0.0",
        prediction_type: str = "channel",
        use_multi_output: bool = True,
        **kwargs
    ):
        """
        Initialize channel timing model.
        
        Args:
            version: Model version
            prediction_type: Type of prediction ('channel', 'timing', 'both')
            use_multi_output: Whether to predict multiple outputs simultaneously
            **kwargs: Additional arguments for BaseModel
        """
        super().__init__(
            model_name="channel_timing_optimizer",
            model_type="channel_timing",
            version=version,
            **kwargs
        )
        
        self.prediction_type = prediction_type
        self.use_multi_output = use_multi_output
        
        # Label encoders for categorical targets
        self.channel_encoder = LabelEncoder()
        self.timing_encoder = LabelEncoder()
        
        # Model components
        self.channel_model = None
        self.timing_model = None
        self.combined_model = None
        
        # Target columns
        self.metadata.target_column = "recommended_channel"
        if prediction_type == "timing":
            self.metadata.target_column = "optimal_timing"
        elif prediction_type == "both":
            self.metadata.target_column = "channel_timing_combined"
    
    def _get_default_hyperparameters(self) -> Dict[str, Any]:
        """Get default hyperparameters for channel timing model."""
        return {
            # LightGBM parameters for multi-class classification
            'lgb_params': {
                'objective': 'multiclass',
                'metric': 'multi_logloss',
                'boosting_type': 'gbdt',
                'num_leaves': 31,
                'learning_rate': 0.05,
                'feature_fraction': 0.9,
                'bagging_fraction': 0.8,
                'bagging_freq': 5,
                'verbose': -1,
                'random_state': 42,
                'n_estimators': 100,
            },
            
            # CatBoost parameters
            'catboost_params': {
                'iterations': 100,
                'depth': 6,
                'learning_rate': 0.03,
                'loss_function': 'MultiClass',
                'eval_metric': 'Accuracy',
                'random_seed': 42,
                'verbose': False,
            },
            
            # Random Forest parameters
            'rf_params': {
                'n_estimators': 100,
                'max_depth': 15,
                'min_samples_split': 5,
                'min_samples_leaf': 2,
                'random_state': 42,
                'n_jobs': -1,
            },
            
            # Model selection
            'primary_algorithm': 'lightgbm',  # lightgbm, catboost, random_forest
        }
    
    def _create_model(self, hyperparameters: Dict[str, Any]) -> Any:
        """
        Create channel timing model.
        
        Args:
            hyperparameters: Model hyperparameters
            
        Returns:
            Model instance
        """
        algorithm = hyperparameters.get('primary_algorithm', 'lightgbm')
        
        if algorithm == 'lightgbm':
            model = lgb.LGBMClassifier(**hyperparameters['lgb_params'])
        elif algorithm == 'catboost':
            model = cb.CatBoostClassifier(**hyperparameters['catboost_params'])
        elif algorithm == 'random_forest':
            model = RandomForestClassifier(**hyperparameters['rf_params'])
        else:
            raise ValueError(f"Unknown algorithm: {algorithm}")
        
        logger.info(
            "Channel timing model created",
            algorithm=algorithm,
            prediction_type=self.prediction_type,
        )
        
        return model
    
    def _validate_input(self, X: np.ndarray, y: Optional[np.ndarray] = None) -> None:
        """Validate input data for channel timing model."""
        if X.shape[0] == 0:
            raise ValueError("Empty feature matrix")
        
        if X.shape[1] == 0:
            raise ValueError("No features provided")
        
        if np.isnan(X).any():
            raise ValueError("Feature matrix contains NaN values")
        
        if np.isinf(X).any():
            raise ValueError("Feature matrix contains infinite values")
        
        if y is not None:
            if len(y) != X.shape[0]:
                raise ValueError("Feature matrix and target vector length mismatch")
            
            # Check for valid categorical targets
            unique_values = len(np.unique(y))
            if unique_values < 2:
                raise ValueError("Target must have at least 2 unique values")
            
            if unique_values > 20:
                logger.warning(f"High number of target classes: {unique_values}")
    
    def _prepare_targets(self, y: np.ndarray) -> Tuple[np.ndarray, Dict[str, Any]]:
        """
        Prepare target variables for training.
        
        Args:
            y: Raw target data
            
        Returns:
            Tuple of (encoded_targets, encoding_info)
        """
        encoding_info = {}
        
        if self.prediction_type == "channel":
            # Encode channel labels
            y_encoded = self.channel_encoder.fit_transform(y)
            encoding_info['channel_classes'] = self.channel_encoder.classes_.tolist()
            
        elif self.prediction_type == "timing":
            # Encode timing labels
            y_encoded = self.timing_encoder.fit_transform(y)
            encoding_info['timing_classes'] = self.timing_encoder.classes_.tolist()
            
        elif self.prediction_type == "both":
            # For combined prediction, we need to handle multi-output
            # Assume y is a 2D array with [channel, timing] columns
            if y.ndim == 1:
                raise ValueError("For 'both' prediction type, y must be 2D array with [channel, timing]")
            
            channel_encoded = self.channel_encoder.fit_transform(y[:, 0])
            timing_encoded = self.timing_encoder.fit_transform(y[:, 1])
            
            y_encoded = np.column_stack([channel_encoded, timing_encoded])
            encoding_info['channel_classes'] = self.channel_encoder.classes_.tolist()
            encoding_info['timing_classes'] = self.timing_encoder.classes_.tolist()
        
        else:
            raise ValueError(f"Unknown prediction type: {self.prediction_type}")
        
        return y_encoded, encoding_info
    
    def _train_model(
        self,
        X: np.ndarray,
        y: np.ndarray,
        validation_data: Optional[Tuple[np.ndarray, np.ndarray]] = None,
    ) -> Dict[str, Any]:
        """
        Train the channel timing model.
        
        Args:
            X: Training features
            y: Training targets
            validation_data: Validation data
            
        Returns:
            Training results
        """
        logger.info("Training channel timing model")
        
        # Prepare targets
        y_encoded, encoding_info = self._prepare_targets(y)
        
        training_results = {
            'encoding_info': encoding_info,
            'training_accuracy': 0.0,
            'validation_accuracy': 0.0,
            'class_distribution': {},
        }
        
        if self.prediction_type in ["channel", "timing"]:
            # Single output prediction
            self.model.fit(X, y_encoded)
            
            # Training accuracy
            train_pred = self.model.predict(X)
            training_results['training_accuracy'] = accuracy_score(y_encoded, train_pred)
            
            # Validation accuracy
            if validation_data is not None:
                X_val, y_val = validation_data
                y_val_encoded, _ = self._prepare_targets(y_val)
                val_pred = self.model.predict(X_val)
                training_results['validation_accuracy'] = accuracy_score(y_val_encoded, val_pred)
            
            # Class distribution
            unique, counts = np.unique(y_encoded, return_counts=True)
            training_results['class_distribution'] = dict(zip(unique, counts))
            
        elif self.prediction_type == "both":
            # Multi-output prediction
            if self.use_multi_output:
                from sklearn.multioutput import MultiOutputClassifier
                self.model = MultiOutputClassifier(self.model)
                self.model.fit(X, y_encoded)
                
                # Training accuracy (average across outputs)
                train_pred = self.model.predict(X)
                channel_acc = accuracy_score(y_encoded[:, 0], train_pred[:, 0])
                timing_acc = accuracy_score(y_encoded[:, 1], train_pred[:, 1])
                training_results['training_accuracy'] = (channel_acc + timing_acc) / 2
                training_results['channel_accuracy'] = channel_acc
                training_results['timing_accuracy'] = timing_acc
                
            else:
                # Train separate models
                self.channel_model = self._create_model(self.metadata.hyperparameters)
                self.timing_model = self._create_model(self.metadata.hyperparameters)
                
                self.channel_model.fit(X, y_encoded[:, 0])
                self.timing_model.fit(X, y_encoded[:, 1])
                
                # Training accuracies
                channel_pred = self.channel_model.predict(X)
                timing_pred = self.timing_model.predict(X)
                
                channel_acc = accuracy_score(y_encoded[:, 0], channel_pred)
                timing_acc = accuracy_score(y_encoded[:, 1], timing_pred)
                
                training_results['training_accuracy'] = (channel_acc + timing_acc) / 2
                training_results['channel_accuracy'] = channel_acc
                training_results['timing_accuracy'] = timing_acc
        
        logger.info(
            "Channel timing model training completed",
            training_accuracy=training_results['training_accuracy'],
            prediction_type=self.prediction_type,
        )
        
        return training_results
    
    def _predict(self, X: np.ndarray) -> np.ndarray:
        """Make predictions with the channel timing model."""
        if self.prediction_type in ["channel", "timing"]:
            return self.model.predict(X)
        
        elif self.prediction_type == "both":
            if self.use_multi_output:
                return self.model.predict(X)
            else:
                channel_pred = self.channel_model.predict(X)
                timing_pred = self.timing_model.predict(X)
                return np.column_stack([channel_pred, timing_pred])
    
    def predict_proba(self, X: np.ndarray) -> np.ndarray:
        """Predict class probabilities."""
        if not self.is_trained:
            raise ValueError("Model must be trained before making predictions")
        
        self._validate_input(X)
        
        if self.prediction_type in ["channel", "timing"]:
            return self.model.predict_proba(X)
        
        elif self.prediction_type == "both":
            if self.use_multi_output:
                # Multi-output probabilities
                probas = []
                for estimator in self.model.estimators_:
                    probas.append(estimator.predict_proba(X))
                return probas
            else:
                channel_proba = self.channel_model.predict_proba(X)
                timing_proba = self.timing_model.predict_proba(X)
                return [channel_proba, timing_proba]
    
    def get_channel_recommendations(
        self,
        X: np.ndarray,
        top_k: int = 3,
        include_probabilities: bool = True,
    ) -> List[Dict[str, Any]]:
        """
        Get channel recommendations for prospects.
        
        Args:
            X: Feature matrix
            top_k: Number of top recommendations to return
            include_probabilities: Whether to include prediction probabilities
            
        Returns:
            List of recommendation dictionaries
        """
        if not self.is_trained:
            raise ValueError("Model must be trained to get recommendations")
        
        if self.prediction_type not in ["channel", "both"]:
            raise ValueError("Channel recommendations require channel prediction")
        
        recommendations = []
        
        if self.prediction_type == "channel":
            probabilities = self.predict_proba(X)
            predictions = self.model.predict(X)
            
            for i in range(len(X)):
                # Get top k channels
                top_indices = np.argsort(probabilities[i])[-top_k:][::-1]
                
                prospect_recommendations = {
                    'prospect_id': i,
                    'primary_channel': self.channel_encoder.inverse_transform([predictions[i]])[0],
                    'recommendations': []
                }
                
                for idx in top_indices:
                    channel = self.channel_encoder.inverse_transform([idx])[0]
                    prob = probabilities[i][idx] if include_probabilities else None
                    
                    prospect_recommendations['recommendations'].append({
                        'channel': channel,
                        'probability': prob,
                        'confidence': 'high' if prob > 0.7 else 'medium' if prob > 0.4 else 'low'
                    })
                
                recommendations.append(prospect_recommendations)
        
        elif self.prediction_type == "both":
            if self.use_multi_output:
                predictions = self.model.predict(X)
                probabilities = self.predict_proba(X)
                
                for i in range(len(X)):
                    channel_pred = predictions[i][0]
                    timing_pred = predictions[i][1]
                    
                    channel = self.channel_encoder.inverse_transform([channel_pred])[0]
                    timing = self.timing_encoder.inverse_transform([timing_pred])[0]
                    
                    recommendation = {
                        'prospect_id': i,
                        'recommended_channel': channel,
                        'recommended_timing': timing,
                    }
                    
                    if include_probabilities:
                        channel_proba = probabilities[0][i][channel_pred]
                        timing_proba = probabilities[1][i][timing_pred]
                        recommendation['channel_confidence'] = channel_proba
                        recommendation['timing_confidence'] = timing_proba
                        recommendation['overall_confidence'] = (channel_proba + timing_proba) / 2
                    
                    recommendations.append(recommendation)
        
        return recommendations
    
    def get_timing_insights(self, X: np.ndarray) -> Dict[str, Any]:
        """
        Get timing insights for optimal outreach.
        
        Args:
            X: Feature matrix
            
        Returns:
            Dictionary with timing insights
        """
        if not self.is_trained:
            raise ValueError("Model must be trained to get insights")
        
        if self.prediction_type not in ["timing", "both"]:
            raise ValueError("Timing insights require timing prediction")
        
        insights = {
            'optimal_times': {},
            'time_distribution': {},
            'confidence_scores': {},
        }
        
        if self.prediction_type == "timing":
            predictions = self.model.predict(X)
            probabilities = self.predict_proba(X)
            
            # Convert predictions back to timing labels
            timing_labels = self.timing_encoder.inverse_transform(predictions)
            
            # Calculate distribution
            unique, counts = np.unique(timing_labels, return_counts=True)
            insights['time_distribution'] = dict(zip(unique, counts))
            
            # Calculate average confidence per timing
            for timing in unique:
                mask = timing_labels == timing
                if mask.any():
                    timing_idx = self.timing_encoder.transform([timing])[0]
                    avg_confidence = np.mean(probabilities[mask, timing_idx])
                    insights['confidence_scores'][timing] = avg_confidence
            
            # Most recommended timing
            most_common_timing = unique[np.argmax(counts)]
            insights['most_recommended_timing'] = most_common_timing
        
        return insights
    
    def evaluate_model(
        self,
        X_test: np.ndarray,
        y_test: np.ndarray,
    ) -> Dict[str, Any]:
        """
        Evaluate model performance on test data.
        
        Args:
            X_test: Test features
            y_test: Test targets
            
        Returns:
            Performance metrics
        """
        if not self.is_trained:
            raise ValueError("Model must be trained before evaluation")
        
        # Prepare test targets
        y_test_encoded, _ = self._prepare_targets(y_test)
        
        # Make predictions
        y_pred = self._predict(X_test)
        
        metrics = {}
        
        if self.prediction_type in ["channel", "timing"]:
            # Single output evaluation
            metrics['accuracy'] = accuracy_score(y_test_encoded, y_pred)
            
            # Classification report
            target_names = (
                self.channel_encoder.classes_ if self.prediction_type == "channel"
                else self.timing_encoder.classes_
            )
            
            report = classification_report(
                y_test_encoded, y_pred,
                target_names=target_names,
                output_dict=True
            )
            
            metrics['classification_report'] = report
            metrics['macro_f1'] = report['macro avg']['f1-score']
            metrics['weighted_f1'] = report['weighted avg']['f1-score']
            
        elif self.prediction_type == "both":
            # Multi-output evaluation
            channel_acc = accuracy_score(y_test_encoded[:, 0], y_pred[:, 0])
            timing_acc = accuracy_score(y_test_encoded[:, 1], y_pred[:, 1])
            
            metrics['channel_accuracy'] = channel_acc
            metrics['timing_accuracy'] = timing_acc
            metrics['overall_accuracy'] = (channel_acc + timing_acc) / 2
        
        # Update model metadata
        self.update_performance_metrics(metrics)
        
        logger.info(
            "Channel timing model evaluation completed",
            model_name=self.model_name,
            **{k: v for k, v in metrics.items() if isinstance(v, (int, float))}
        )
        
        return metrics
