"""
Model registry for Sensei AI.

Manages model lifecycle including training, versioning, deployment, and monitoring.
Provides centralized access to all ML models with automatic model management.
"""

import json
import shutil
from pathlib import Path
from typing import Dict, List, Optional, Any, Type, Union
from datetime import datetime, timedelta
import threading

from .base import BaseModel, ModelMetadata
from .conversion import ConversionModel
from .channel import ChannelTimingModel
from .nlp import NLPModel
from ..features import create_feature_pipeline, get_feature_store
from ..utils import get_logger, get_config
from ..utils.monitoring import track_performance, update_active_models

logger = get_logger(__name__)


class ModelRegistry:
    """
    Centralized registry for managing ML models.
    
    Features:
    - Model training and deployment
    - Version management and rollback
    - Performance monitoring
    - Automatic model cleanup
    - Feature pipeline integration
    """
    
    def __init__(self, registry_dir: Optional[Path] = None):
        """
        Initialize model registry.
        
        Args:
            registry_dir: Directory for storing models and metadata
        """
        self.config = get_config()
        self.registry_dir = registry_dir or self.config.models.model_dir
        self.registry_dir.mkdir(parents=True, exist_ok=True)
        
        # Model type mapping
        self.model_classes: Dict[str, Type[BaseModel]] = {
            'conversion': ConversionModel,
            'channel_timing': ChannelTimingModel,
            'nlp': NLPModel,
        }
        
        # Active models cache
        self._active_models: Dict[str, BaseModel] = {}
        self._model_metadata: Dict[str, ModelMetadata] = {}
        self._lock = threading.Lock()
        
        # Load existing models
        self._load_registry()
        
        logger.info(
            "Model registry initialized",
            registry_dir=str(self.registry_dir),
            model_types=list(self.model_classes.keys()),
        )
    
    def _load_registry(self) -> None:
        """Load existing models from registry."""
        metadata_file = self.registry_dir / "registry.json"
        
        if metadata_file.exists():
            try:
                with open(metadata_file, 'r') as f:
                    registry_data = json.load(f)
                
                for model_name, metadata_dict in registry_data.items():
                    metadata = ModelMetadata.from_dict(metadata_dict)
                    self._model_metadata[model_name] = metadata
                
                logger.info(
                    "Registry loaded",
                    models_found=len(self._model_metadata),
                )
                
            except Exception as e:
                logger.warning(f"Failed to load registry: {e}")
        
        # Update active models count
        update_active_models(len(self._active_models))
    
    def _save_registry(self) -> None:
        """Save registry metadata to disk."""
        metadata_file = self.registry_dir / "registry.json"
        
        try:
            registry_data = {
                name: metadata.to_dict()
                for name, metadata in self._model_metadata.items()
            }
            
            with open(metadata_file, 'w') as f:
                json.dump(registry_data, f, indent=2, default=str)
            
            logger.debug("Registry saved", models=len(registry_data))
            
        except Exception as e:
            logger.error(f"Failed to save registry: {e}")
    
    def train_model(
        self,
        model_name: str,
        model_type: str,
        max_samples: Optional[int] = None,
        hyperparameters: Optional[Dict[str, Any]] = None,
        version: Optional[str] = None,
        output_dir: Optional[Path] = None,
        config_file: Optional[Path] = None,
    ) -> BaseModel:
        """
        Train a new model.
        
        Args:
            model_name: Name of the model
            model_type: Type of model (conversion, channel_timing, nlp)
            max_samples: Maximum number of training samples
            hyperparameters: Model hyperparameters
            version: Model version
            output_dir: Output directory for model files
            config_file: Custom configuration file
            
        Returns:
            Trained model instance
        """
        with track_performance(
            "model_training_full_pipeline",
            labels={"model_type": model_type, "model_name": model_name}
        ):
            logger.info(
                "Starting model training",
                model_name=model_name,
                model_type=model_type,
                max_samples=max_samples,
            )
            
            # Validate model type
            if model_type not in self.model_classes:
                raise ValueError(f"Unknown model type: {model_type}")
            
            # Generate version if not provided
            if version is None:
                version = self._generate_version(model_name)
            
            # Create feature pipeline
            feature_pipeline = create_feature_pipeline(model_type)
            
            # Extract and prepare data
            X_train, X_test, y_train, y_test, metadata_df = feature_pipeline.extract_and_prepare_data(
                limit=max_samples or self.config.training.max_samples,
                test_size=self.config.training.test_size,
                random_state=self.config.training.random_state,
            )
            
            # Transform features
            X_train_transformed = feature_pipeline.fit_transform(X_train, y_train)
            X_test_transformed = feature_pipeline.transform(X_test)
            
            # Create model
            model_class = self.model_classes[model_type]
            model = model_class(
                version=version,
                model_dir=output_dir or self.registry_dir,
            )
            
            # Train model
            training_results = model.train(
                X_train_transformed,
                y_train,
                hyperparameters=hyperparameters,
                feature_names=feature_pipeline.get_feature_names(),
                validation_data=(X_test_transformed, y_test),
            )
            
            # Evaluate model
            evaluation_results = model.evaluate_model(X_test_transformed, y_test)
            
            # Save model
            model_path = model.save()
            
            # Update registry
            with self._lock:
                full_model_name = f"{model_name}_v{version}"
                self._active_models[full_model_name] = model
                self._model_metadata[full_model_name] = model.metadata
                self._save_registry()
            
            # Update monitoring
            update_active_models(len(self._active_models))
            
            logger.info(
                "Model training completed successfully",
                model_name=model_name,
                version=version,
                model_path=str(model_path),
                training_samples=len(X_train),
                test_samples=len(X_test),
                **evaluation_results,
            )
            
            return model
    
    def load_model(
        self,
        model_name: str,
        version: Optional[str] = None,
    ) -> BaseModel:
        """
        Load a model from the registry.
        
        Args:
            model_name: Name of the model
            version: Model version (latest if not specified)
            
        Returns:
            Loaded model instance
        """
        # Find model file
        if version is None:
            # Get latest version
            version = self._get_latest_version(model_name)
        
        full_model_name = f"{model_name}_v{version}"
        
        # Check if already loaded
        with self._lock:
            if full_model_name in self._active_models:
                logger.debug(f"Model {full_model_name} already loaded")
                return self._active_models[full_model_name]
        
        # Load from disk
        model_file = self.registry_dir / f"{full_model_name}.pkl"
        
        if not model_file.exists():
            raise FileNotFoundError(f"Model file not found: {model_file}")
        
        # Determine model type from metadata
        if full_model_name in self._model_metadata:
            model_type = self._model_metadata[full_model_name].model_type
        else:
            # Try to infer from model name
            model_type = self._infer_model_type(model_name)
        
        # Create model instance
        model_class = self.model_classes[model_type]
        model = model_class(version=version)
        
        # Load model
        model.load(model_file)
        
        # Cache model
        with self._lock:
            self._active_models[full_model_name] = model
            if full_model_name not in self._model_metadata:
                self._model_metadata[full_model_name] = model.metadata
        
        # Update monitoring
        update_active_models(len(self._active_models))
        
        logger.info(f"Model loaded successfully: {full_model_name}")
        return model
    
    def get_model(
        self,
        model_name: str,
        version: Optional[str] = None,
        auto_load: bool = True,
    ) -> Optional[BaseModel]:
        """
        Get a model from the registry.
        
        Args:
            model_name: Name of the model
            version: Model version
            auto_load: Whether to automatically load the model if not in memory
            
        Returns:
            Model instance or None if not found
        """
        if version is None:
            version = self._get_latest_version(model_name)
        
        full_model_name = f"{model_name}_v{version}"
        
        # Check active models
        with self._lock:
            if full_model_name in self._active_models:
                return self._active_models[full_model_name]
        
        # Auto-load if requested
        if auto_load:
            try:
                return self.load_model(model_name, version)
            except Exception as e:
                logger.warning(f"Failed to auto-load model {full_model_name}: {e}")
        
        return None
    
    def list_models(self, model_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        List all models in the registry.
        
        Args:
            model_type: Filter by model type
            
        Returns:
            List of model information dictionaries
        """
        models = []
        
        for model_name, metadata in self._model_metadata.items():
            if model_type is None or metadata.model_type == model_type:
                model_info = {
                    'name': model_name,
                    'type': metadata.model_type,
                    'version': metadata.version,
                    'created_at': metadata.created_at,
                    'trained_at': metadata.trained_at,
                    'performance_metrics': metadata.performance_metrics,
                    'is_active': model_name in self._active_models,
                }
                models.append(model_info)
        
        # Sort by creation date (newest first)
        models.sort(key=lambda x: x['created_at'], reverse=True)
        
        return models
    
    def delete_model(
        self,
        model_name: str,
        version: Optional[str] = None,
    ) -> bool:
        """
        Delete a model from the registry.
        
        Args:
            model_name: Name of the model
            version: Model version (all versions if not specified)
            
        Returns:
            True if successful
        """
        if version is None:
            # Delete all versions
            models_to_delete = [
                name for name in self._model_metadata.keys()
                if name.startswith(f"{model_name}_v")
            ]
        else:
            models_to_delete = [f"{model_name}_v{version}"]
        
        deleted_count = 0
        
        for full_model_name in models_to_delete:
            try:
                # Remove from active models
                with self._lock:
                    if full_model_name in self._active_models:
                        del self._active_models[full_model_name]
                    
                    if full_model_name in self._model_metadata:
                        del self._model_metadata[full_model_name]
                
                # Delete model file
                model_file = self.registry_dir / f"{full_model_name}.pkl"
                if model_file.exists():
                    model_file.unlink()
                
                deleted_count += 1
                logger.info(f"Model deleted: {full_model_name}")
                
            except Exception as e:
                logger.error(f"Failed to delete model {full_model_name}: {e}")
        
        if deleted_count > 0:
            self._save_registry()
            update_active_models(len(self._active_models))
        
        return deleted_count > 0
    
    def cleanup_old_models(self, keep_versions: int = 3) -> int:
        """
        Clean up old model versions.
        
        Args:
            keep_versions: Number of versions to keep per model
            
        Returns:
            Number of models deleted
        """
        # Group models by base name
        model_groups: Dict[str, List[str]] = {}
        
        for full_model_name in self._model_metadata.keys():
            base_name = full_model_name.rsplit('_v', 1)[0]
            if base_name not in model_groups:
                model_groups[base_name] = []
            model_groups[base_name].append(full_model_name)
        
        deleted_count = 0
        
        for base_name, versions in model_groups.items():
            if len(versions) > keep_versions:
                # Sort by creation date (newest first)
                versions.sort(
                    key=lambda x: self._model_metadata[x].created_at,
                    reverse=True
                )
                
                # Delete old versions
                for old_version in versions[keep_versions:]:
                    if self.delete_model(old_version.rsplit('_v', 1)[0], old_version.rsplit('_v', 1)[1]):
                        deleted_count += 1
        
        logger.info(f"Cleanup completed: {deleted_count} old models deleted")
        return deleted_count
    
    def _generate_version(self, model_name: str) -> str:
        """Generate next version number for a model."""
        existing_versions = []
        
        for full_name in self._model_metadata.keys():
            if full_name.startswith(f"{model_name}_v"):
                version_str = full_name.split('_v')[-1]
                try:
                    # Parse semantic version (major.minor.patch)
                    version_parts = [int(x) for x in version_str.split('.')]
                    existing_versions.append(version_parts)
                except ValueError:
                    continue
        
        if not existing_versions:
            return "1.0.0"
        
        # Get latest version and increment patch
        latest = max(existing_versions)
        latest[2] += 1  # Increment patch version
        
        return '.'.join(map(str, latest))
    
    def _get_latest_version(self, model_name: str) -> str:
        """Get the latest version of a model."""
        versions = []
        
        for full_name in self._model_metadata.keys():
            if full_name.startswith(f"{model_name}_v"):
                version_str = full_name.split('_v')[-1]
                try:
                    version_parts = [int(x) for x in version_str.split('.')]
                    versions.append((version_parts, version_str))
                except ValueError:
                    continue
        
        if not versions:
            raise ValueError(f"No versions found for model: {model_name}")
        
        # Return the latest version string
        latest_version = max(versions, key=lambda x: x[0])
        return latest_version[1]
    
    def _infer_model_type(self, model_name: str) -> str:
        """Infer model type from model name."""
        name_lower = model_name.lower()
        
        if 'conversion' in name_lower or 'convert' in name_lower:
            return 'conversion'
        elif 'channel' in name_lower or 'timing' in name_lower:
            return 'channel_timing'
        elif 'nlp' in name_lower or 'conversation' in name_lower or 'text' in name_lower:
            return 'nlp'
        else:
            # Default to conversion
            return 'conversion'
    
    def get_registry_stats(self) -> Dict[str, Any]:
        """Get registry statistics."""
        stats = {
            'total_models': len(self._model_metadata),
            'active_models': len(self._active_models),
            'model_types': {},
            'registry_size_mb': 0.0,
        }
        
        # Count by model type
        for metadata in self._model_metadata.values():
            model_type = metadata.model_type
            if model_type not in stats['model_types']:
                stats['model_types'][model_type] = 0
            stats['model_types'][model_type] += 1
        
        # Calculate registry size
        try:
            total_size = sum(
                f.stat().st_size for f in self.registry_dir.glob("*.pkl")
                if f.is_file()
            )
            stats['registry_size_mb'] = total_size / (1024 * 1024)
        except Exception:
            pass
        
        return stats


# Global registry instance
_model_registry: Optional[ModelRegistry] = None
_registry_lock = threading.Lock()


def get_model_registry() -> ModelRegistry:
    """Get the global model registry instance."""
    global _model_registry
    if _model_registry is None:
        with _registry_lock:
            if _model_registry is None:
                _model_registry = ModelRegistry()
    return _model_registry
