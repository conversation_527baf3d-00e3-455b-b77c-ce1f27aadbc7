"""
Command-line interface for Sensei AI.

Provides commands for training, serving, and managing ML models.
"""

import click
import uvic<PERSON>
from pathlib import Path
from typing import Optional, List

from .utils import get_logger, get_config
from .models import ModelRegistry
from .api import create_app

logger = get_logger(__name__)


@click.group()
@click.version_option()
def main() -> None:
    """Sensei AI - Production ML Platform for B2B Sales Optimization."""
    pass


@main.command()
@click.option(
    "--model",
    type=click.Choice(["conversion", "channel", "nlp", "all"]),
    default="all",
    help="Model to train (default: all)",
)
@click.option(
    "--samples",
    type=int,
    default=None,
    help="Number of samples to use for training",
)
@click.option(
    "--output-dir",
    type=click.Path(path_type=Path),
    default=Path("models"),
    help="Output directory for trained models",
)
@click.option(
    "--config-file",
    type=click.Path(exists=True, path_type=Path),
    default=None,
    help="Custom configuration file",
)
def train(
    model: str,
    samples: Optional[int],
    output_dir: Path,
    config_file: Optional[Path],
) -> None:
    """Train ML models."""
    logger.info(
        "Starting model training",
        model=model,
        samples=samples,
        output_dir=str(output_dir),
    )
    
    try:
        registry = ModelRegistry()
        
        if model == "all":
            models_to_train = ["conversion", "channel", "nlp"]
        else:
            models_to_train = [model]
        
        for model_name in models_to_train:
            logger.info(f"Training {model_name} model")
            registry.train_model(
                model_name=model_name,
                max_samples=samples,
                output_dir=output_dir,
                config_file=config_file,
            )
            logger.info(f"Successfully trained {model_name} model")
        
        logger.info("Model training completed successfully")
        
    except Exception as e:
        logger.error("Model training failed", error=str(e))
        raise click.ClickException(f"Training failed: {e}")


@main.command()
@click.option(
    "--model",
    type=click.Choice(["conversion", "channel", "nlp"]),
    required=True,
    help="Model to optimize",
)
@click.option(
    "--trials",
    type=int,
    default=100,
    help="Number of optimization trials",
)
@click.option(
    "--timeout",
    type=int,
    default=3600,
    help="Optimization timeout in seconds",
)
@click.option(
    "--output-dir",
    type=click.Path(path_type=Path),
    default=Path("models"),
    help="Output directory for optimized models",
)
def optimize(
    model: str,
    trials: int,
    timeout: int,
    output_dir: Path,
) -> None:
    """Optimize model hyperparameters."""
    logger.info(
        "Starting hyperparameter optimization",
        model=model,
        trials=trials,
        timeout=timeout,
    )
    
    try:
        registry = ModelRegistry()
        best_params = registry.optimize_hyperparameters(
            model_name=model,
            n_trials=trials,
            timeout=timeout,
            output_dir=output_dir,
        )
        
        logger.info(
            "Hyperparameter optimization completed",
            model=model,
            best_params=best_params,
        )
        
    except Exception as e:
        logger.error("Hyperparameter optimization failed", error=str(e))
        raise click.ClickException(f"Optimization failed: {e}")


@main.command()
@click.option(
    "--host",
    default="0.0.0.0",
    help="Host to bind the server to",
)
@click.option(
    "--port",
    type=int,
    default=8000,
    help="Port to bind the server to",
)
@click.option(
    "--workers",
    type=int,
    default=1,
    help="Number of worker processes",
)
@click.option(
    "--reload",
    is_flag=True,
    help="Enable auto-reload for development",
)
@click.option(
    "--log-level",
    type=click.Choice(["debug", "info", "warning", "error"]),
    default="info",
    help="Log level",
)
def serve(
    host: str,
    port: int,
    workers: int,
    reload: bool,
    log_level: str,
) -> None:
    """Start the API server."""
    logger.info(
        "Starting API server",
        host=host,
        port=port,
        workers=workers,
        reload=reload,
        log_level=log_level,
    )
    
    try:
        app = create_app()
        
        uvicorn.run(
            "sensei.api:create_app",
            factory=True,
            host=host,
            port=port,
            workers=workers if not reload else 1,
            reload=reload,
            log_level=log_level,
            access_log=True,
        )
        
    except Exception as e:
        logger.error("Failed to start API server", error=str(e))
        raise click.ClickException(f"Server startup failed: {e}")


@main.command()
@click.option(
    "--model",
    type=click.Choice(["conversion", "channel", "nlp", "all"]),
    default="all",
    help="Model to evaluate (default: all)",
)
@click.option(
    "--test-size",
    type=float,
    default=0.2,
    help="Test set size (default: 0.2)",
)
def evaluate(model: str, test_size: float) -> None:
    """Evaluate model performance."""
    logger.info("Starting model evaluation", model=model, test_size=test_size)
    
    try:
        registry = ModelRegistry()
        
        if model == "all":
            models_to_evaluate = ["conversion", "channel", "nlp"]
        else:
            models_to_evaluate = [model]
        
        for model_name in models_to_evaluate:
            logger.info(f"Evaluating {model_name} model")
            metrics = registry.evaluate_model(
                model_name=model_name,
                test_size=test_size,
            )
            
            logger.info(
                f"Evaluation results for {model_name}",
                metrics=metrics,
            )
        
        logger.info("Model evaluation completed successfully")
        
    except Exception as e:
        logger.error("Model evaluation failed", error=str(e))
        raise click.ClickException(f"Evaluation failed: {e}")


@main.command()
def status() -> None:
    """Show system status and health checks."""
    logger.info("Checking system status")
    
    try:
        from .data import get_bigquery_client
        from .utils import get_config
        
        # Check BigQuery connection
        bq_client = get_bigquery_client()
        logger.info("BigQuery connection: OK")
        
        # Check model registry
        registry = ModelRegistry()
        available_models = registry.list_models()
        logger.info("Available models", models=available_models)
        
        # Check configuration
        config = get_config()
        logger.info("Configuration loaded successfully")
        
        click.echo("✅ System status: All checks passed")
        
    except Exception as e:
        logger.error("System health check failed", error=str(e))
        click.echo(f"❌ System status: Health check failed - {e}")
        raise click.ClickException(f"Health check failed: {e}")


if __name__ == "__main__":
    main()
