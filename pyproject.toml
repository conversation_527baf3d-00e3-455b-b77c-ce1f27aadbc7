[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "sensei-ai"
version = "1.0.0"
description = "Production-Ready ML Platform for B2B Sales Optimization"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "Sensei AI Team", email = "<EMAIL>"}
]
maintainers = [
    {name = "Sensei AI Team", email = "<EMAIL>"}
]
keywords = ["machine-learning", "sales", "optimization", "bigquery", "nlp"]
classifiers = [
    "Development Status :: 5 - Production/Stable",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Software Development :: Libraries :: Python Modules",
]
requires-python = ">=3.11"
dependencies = [
    # Core ML and Data Science
    "numpy>=1.24.0,<2.0.0",
    "pandas>=2.0.0,<3.0.0",
    "scikit-learn>=1.3.0,<2.0.0",
    "scipy>=1.11.0,<2.0.0",
    
    # ML Models
    "lightgbm>=4.0.0,<5.0.0",
    "catboost>=1.2.0,<2.0.0",
    "xgboost>=1.7.0,<2.0.0",
    
    # NLP and Embeddings
    "sentence-transformers>=2.2.0,<3.0.0",
    "transformers>=4.30.0,<5.0.0",
    "torch>=2.0.0,<3.0.0",
    "umap-learn>=0.5.0,<1.0.0",
    "hdbscan>=0.8.0,<1.0.0",
    
    # Hyperparameter Optimization
    "optuna>=3.3.0,<4.0.0",
    
    # API Framework
    "fastapi>=0.100.0,<1.0.0",
    "uvicorn[standard]>=0.23.0,<1.0.0",
    "pydantic>=2.3.0,<3.0.0",
    
    # Google Cloud
    "google-cloud-bigquery>=3.11.0,<4.0.0",
    "google-cloud-secret-manager>=2.16.0,<3.0.0",
    "google-auth>=2.20.0,<3.0.0",
    
    # Data Validation and Processing
    "marshmallow>=3.20.0,<4.0.0",
    
    # Logging and Monitoring
    "structlog>=23.1.0,<24.0.0",
    "prometheus-client>=0.17.0,<1.0.0",
    
    # Utilities
    "python-dotenv>=1.0.0,<2.0.0",
    "click>=8.1.0,<9.0.0",
    "tqdm>=4.66.0,<5.0.0",
    "joblib>=1.3.0,<2.0.0",
]

[project.optional-dependencies]
dev = [
    # Testing
    "pytest>=7.4.0,<8.0.0",
    "pytest-cov>=4.1.0,<5.0.0",
    "pytest-asyncio>=0.21.0,<1.0.0",
    "pytest-mock>=3.11.0,<4.0.0",
    "httpx>=0.24.0,<1.0.0",
    
    # Code Quality
    "black>=23.7.0,<24.0.0",
    "isort>=5.12.0,<6.0.0",
    "mypy>=1.5.0,<2.0.0",
    "ruff>=0.0.280,<1.0.0",
    "pre-commit>=3.3.0,<4.0.0",
    
    # Documentation
    "mkdocs>=1.5.0,<2.0.0",
    "mkdocs-material>=9.2.0,<10.0.0",
    "mkdocstrings[python]>=0.22.0,<1.0.0",
]

production = [
    "gunicorn>=21.2.0,<22.0.0",
]

[project.urls]
Homepage = "https://github.com/sensei-ai/sensei-ai"
Documentation = "https://sensei-ai.readthedocs.io/"
Repository = "https://github.com/sensei-ai/sensei-ai.git"
"Bug Tracker" = "https://github.com/sensei-ai/sensei-ai/issues"

[project.scripts]
sensei = "sensei.cli:main"

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-data]
"*" = ["*.yaml", "*.yml", "*.json", "*.txt"]

# Black configuration
[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

# isort configuration
[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["sensei"]
known_third_party = ["numpy", "pandas", "sklearn", "google", "fastapi"]

# MyPy configuration
[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "catboost.*",
    "lightgbm.*",
    "xgboost.*",
    "optuna.*",
    "umap.*",
    "hdbscan.*",
    "sentence_transformers.*",
]
ignore_missing_imports = true

# Pytest configuration
[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "bigquery: marks tests that require BigQuery access",
]

# Coverage configuration
[tool.coverage.run]
source = ["src/sensei"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/venv/*",
    "*/env/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

# Ruff configuration
[tool.ruff]
target-version = "py311"
line-length = 88
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "C4", # flake8-comprehensions
    "UP", # pyupgrade
]
ignore = [
    "E501",  # line too long, handled by black
    "B008",  # do not perform function calls in argument defaults
    "C901",  # too complex
]

[tool.ruff.per-file-ignores]
"__init__.py" = ["F401"]
"tests/**/*" = ["B011"]
